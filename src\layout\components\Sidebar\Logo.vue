<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-16 09:37:31
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-23 18:00:00
 * @FilePath: \办公文档\代码\新建文件夹\src\layout\components\Sidebar\Logo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}" :style="{ backgroundColor: 'var(--menu-logo-back-ground)' }">
    <transition name="sidebarLogoFade" style="font-size: 20px !important">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" :to="indexPage">
        <img v-if="logo" :src="collapseLogo" class="sidebar-logo2" />
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" :to="indexPage">
        <img v-if="logo" :src="expandLogo" class="sidebar-logo" :style="expandStyle" />
      </router-link>
    </transition>
  </div>
</template>

<script>
import logoImg from '@/assets/logo/logo.png'
import logoEnImg from '@/assets/logo/logo_en.png'
import logoImg2 from '@/assets/logo/logo3.png'
import variables from '@/assets/styles/variables.scss'

import { mapState, mapGetters } from 'vuex'
import Cookies from 'js-cookie'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme
    },
    ...mapState({
      indexPage: state => state.permission.indexPage
    }),
    expandLogo() {
      let lang = Cookies.get('language')
      let roles = this.$store.state.user.roles
      let isAdminRole = roles.findIndex(item => item == 'admin') !== -1
      const origin = window.location.origin
      if (lang == 'zh') {
        if (isAdminRole) return this.logo
        if (this.userInfo.dept.logoPathCn) {
          return `${origin}/prod-api${this.userInfo.dept.logoPathCn}`
        } else {
          return this.logo
        }
      } else {
        if (isAdminRole) return this.logoEn
        if (this.userInfo.dept.logoPathUs) {
          return `${origin}/prod-api${this.userInfo.dept.logoPathUs}`
        } else {
          return this.logoEn
        }
      }
    },
    expandStyle() {
      return {
        width: this.userInfo.dept.logoWidth ? this.userInfo.dept.logoWidth + 'px': 'auto',
        height: this.userInfo.dept.logoHeight ? this.userInfo.dept.logoHeight + 'px': '64px',
      }
    },
    collapseLogo() {
      let roles = this.$store.state.user.roles
      let isAdminRole = roles.findIndex(item => item == 'admin') !== -1
      const origin = window.location.origin
      if (isAdminRole) return this.logo2
      if (this.userInfo.dept.zoomLogoPath) {
        return `${origin}/prod-api${this.userInfo.dept.zoomLogoPath}`
      } else {
        return this.logo2
      }
    },
    ...mapGetters([
      'userInfo',
    ]),
  },
  data() {
    return {
      title: '',
      logo: logoImg,
      logoEn: logoEnImg,
      logo2: logoImg2,
    }
  }
}
</script>

<style lang="scss" scoped>
.el-submenu__title{
  font-size: 16px;
}
.el-menu > .el-menu-item {
  font-size: 16px;
}
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 63px;
  line-height: 63px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;
  border-bottom: 1px solid var(--menu-back-ground-active);

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      height: 64px;
      vertical-align: middle;
    }
    & .sidebar-logo2 {
      height: 64px;
      vertical-align: middle;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 16px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
