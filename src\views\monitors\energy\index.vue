<!-- 储能 -->
<template>
  <div id="box">
    <div class="header">
      <div>
        <el-tooltip class="item" effect="dark" :content="control && control['onLineState']" placement="top">
          <svg-icon icon-class="solve" style="margin-right: 5px;" v-if="control['onLineState'] == '在线'" />
          <svg-icon icon-class="solve-off" style="margin-right: 5px;" v-else-if="control['onLineState'] == '离线'" />
          <svg-icon icon-class="solve-off" style="margin-right: 5px;" v-else />
        </el-tooltip>
        <span style="margin-right: 10px;">{{ baseInfo.projectName }} - <b>{{ baseInfo.deviceName }}</b></span>
      </div>
      <div>

        <span style="margin-right: 5px;color: #999;">{{ control.sdt }}({{ baseInfo.timeZone }})</span>
        <el-tooltip class="item" effect="dark" :content="$t('tagsView.refresh')" placement="top">
          <el-button size="mini" circle icon="el-icon-refresh" @click="handleRefreshClick(1)" />
        </el-tooltip>
      </div>
    </div>
    <div class="top">
      <div class="top_box" v-if="isShowDayOutputOfPlant">
        <div class="icon_box">
          <svg-icon icon-class="capacity" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.capacity }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem1') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.deviceBatteryCapacity" :startVal="0" :endVal="baseInfo.deviceBatteryCapacity"
              :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box">
        <div class="icon_box">
          <svg-icon icon-class="ratedPower" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.ratedPower }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem2') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.deviceRatedPower" :startVal="0" :endVal="baseInfo.deviceRatedPower"
              :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kW</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="isShowPhotovoltaicInstalledCapacity">
        <div class="icon_box">
          <svg-icon icon-class="capacityPower" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.capacityPower }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem6') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.photovoltaicInstalledCapacity" :startVal="0"
              :endVal="baseInfo.photovoltaicInstalledCapacity" :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWp</span>
          </div>
        </div>
      </div>
      <div class="top_box">
        <div class="icon_box">
          <svg-icon icon-class="running" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.running }" />
        </div>
        <div class="data_box">
          <div style="margin-bottom: 10px">
            <div class="p">{{ $t('monitor.topItem3') }}</div>
            <span v-if="getStatus == $t('common.offline') || getStatus == $t('common.Closure')" class="red">{{ getStatus }}</span>
            <span v-else-if="getStatus == $t('common.online') || getStatus == $t('common.TurnOn')" class="green">{{ getStatus }}</span>
            <span v-else>{{ getStatus }}</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="isShowPhotovoltaicInstalledCapacity">
        <div class="icon_box">
          <svg-icon icon-class="guangfu" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.guangfu }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem7') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.dayPhotovoltaicPowerCapacityCalculate" :startVal="0"
              :endVal="baseInfo.dayPhotovoltaicPowerCapacityCalculate" :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="isShowDayOutputOfPlant">
        <div class="icon_box">
          <svg-icon icon-class="charge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.charge }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem4') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.dayOutputOfPlant" :startVal="0" :endVal="baseInfo.dayOutputOfPlant"
              :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="isShowDayOutputOfPlant">
        <div class="icon_box">
          <svg-icon icon-class="discharge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.discharge }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem5') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.dayElectricityConsumption" :startVal="0"
              :endVal="baseInfo.dayElectricityConsumption" :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="type == 13">
        <div class="icon_box">
          <svg-icon icon-class="charge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.charge }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('正向电量') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.jk_1083" :startVal="0" :endVal="baseInfo.jk_1083"
              :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="type == 13">
        <div class="icon_box">
          <svg-icon icon-class="discharge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.discharge }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('反向电量') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.jk_1084" :startVal="0"
              :endVal="baseInfo.jk_1084" :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
    </div>
    <div class="tabs">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :label="$t('DataSummary')" name="operations">
          <operations ref="operationsRef" :lineDateInit="control.sdt"></operations>
        </el-tab-pane>
        <el-tab-pane :label="$t('faultMessage')" name="third">
          <fault ref="faultRef"></fault>
        </el-tab-pane>
        <el-tab-pane :label="$t(`param['参数设置']`)" name="parameter" v-if="!isUserRole">
          <parameter v-if="activeName == 'parameter'"></parameter>
        </el-tab-pane>
        <el-tab-pane :label="$t('历史数据')" name="history" v-if="isShowHistory()">
          <History v-if="activeName == 'history'"></History>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import operations from "./operations.vue";
import fault from "./fault.vue";
import parameter from "./parameter.vue";
import History from './history.vue'
import CountTo from 'vue-count-to'
import { getOnAndOff } from '@/utils/parseBinaryToText'
import { checkVersion } from '@/utils/version'
import auth from '@/plugins/auth'
import { isPhotovoltaicFn, isEnergyFn } from '@/hook/useDeviceType'

export default {
  components: {
    operations,
    fault,
    parameter,
    CountTo,
    History
  },  data() {
    return {
      activeName: 'operations',
      nowTime: '',
      time: ''
    }
  },
  mounted() {
    // autofit.init({
    //   dh: 1080,
    //   dw: 1920,
    //   el: "#box",
    //   resize: true
    // })
    this.handleRefreshClick(2)

    this.time = setInterval(() => {
      this.handleRefreshClick(1)
    }, 300000);
  },
  destroyed() {
    clearInterval(this.time)
  },
  computed: {
    baseInfo() {
      return this.$store.state.monitor.baseInfo
    },
    control() {
      return this.$store.state.monitor.control
    },
    type() {
      return this.$route.query.type
    },
    // 光伏装机容量
    isShowPhotovoltaicInstalledCapacity() {
      return isPhotovoltaicFn(this.type)
    },
    // 储能充放电量
    isShowDayOutputOfPlant() {
      return isEnergyFn(this.type)
    },
    // 是否为三级用户
    isUserRole() {
      let roles = this.$store.state.user.roles
      return roles.findIndex(item => item == 'common') !== -1
    },
    getStatus() {
      if (this.control.isAnalysis == 0) {
        return this.control.onLineState == '在线' ? this.$t('common.online') : this.$t('common.offline')
      } else if (this.control.isAnalysis == 1) {
        return this.getOnAndOffFn(this.control['jk_1001'])
      } else {
        return '--'
      }
    },
  },
  methods: {
    isShowHistory() {
      return auth.hasPermi('system:serviceData:list')
    },
    handleClick(tab, event) {
      if (tab.name == 'operations') {
        this.$store.dispatch('powerAnalysisStatisticsFn', {
          deviceSerialNumber: this.$route.query.id,
          deviceType: this.$route.query.type,
          timeZone: this.$route.query.time
        })
        this.$store.dispatch('selectDynamicGraphFn', {
          deviceSerialNumber: this.$route.query.id,
          deviceType: this.$route.query.type
        })
        this.$store.dispatch('electricStatisticsFn', this.$route.query.id)
      } else if (tab.name == 'third') {
        this.$refs.faultRef.getList()
      } else if (tab.name == 'parameter') {
        this.$store.dispatch('bindAliasQueryFn', {
          ac: this.$route.query.id,
          enable: 1,
          type: 0
        })
      }
    },
    handleRefreshClick(type) {
      // type为1是点击刷新按钮获取统计
      // type为2是下发拉取参数只需调用一次
      if (type == 1) {
        this.$store.dispatch('electricStatisticsFn', this.$route.query.id)
      }
      this.nowTime = this.parseTime(new Date())
      this.$store.dispatch('deviceMonitoringDetailTopFn', {
        deviceSerialNumber: this.$route.query.id,
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 本地控制器
        deviceSerialNumber: this.$route.query.id,
        type: 'control',
        deviceType: this.$route.query.type
      }).then(() => {
        this.$store.commit('SET_LINEQUERYINFO', {
          date: this.control.sdt
        })
        this.$store.dispatch('powerAnalysisStatisticsFn', {
          deviceSerialNumber: this.$route.query.id,
          deviceType: this.$route.query.type,
          timeZone: this.$route.query.time,
        })
        if (this.control.onLineState && this.control['onLineState'] != '离线' && type == 2) {
          this.$store.dispatch('param/getJsonDataFn', { ac: this.$route.query.id, types: [1, 2, 5, 6, 7, 8] })
          this.$store.dispatch('param/systemInfoFn', { ac: this.$route.query.id, type: 'one' })
        }
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // pcs
        deviceSerialNumber: this.$route.query.id,
        type: 'pcs',
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // bms
        deviceSerialNumber: this.$route.query.id,
        type: 'bms',
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 电表
        deviceSerialNumber: this.$route.query.id,
        type: 'electricMeter',
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 外设
        deviceSerialNumber: this.$route.query.id,
        type: 'peripherals',
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 充电桩
        deviceSerialNumber: this.$route.query.id,
        type: 'chargingPile',
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 消防、stsIO口
        deviceSerialNumber: this.$route.query.id,
        type: 'Firefighting',
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 电芯
        deviceSerialNumber: this.$route.query.id,
        type: 'BMSCell',
        deviceType: this.$route.query.type
      }).then(() => {
        this.$store.dispatch('getCellConfigFn', {
          ac: this.$route.query.id,
          deviceType: this.$route.query.type
        })
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // bms-bau
        deviceSerialNumber: this.$route.query.id,
        type: 'bms-bau',
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('selectDynamicGraphFn', {
        deviceSerialNumber: this.$route.query.id,
        deviceType: this.$route.query.type
      })
    },
    getOnAndOffFn(num) {
      return getOnAndOff(num)
    }
  },
}
</script>

<style lang="scss" scoped>
#box {
  /* min-height: 100vh; */
  width: 100%;
  background-color: #f7f7f7;
  /* position: absolute; */
  padding: 10px;
  overflow: auto !important;
  height: 100%;
  min-width: 1912px;

  .header {
    border-radius: 14px;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    line-height: 50px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .top {
    border-radius: 14px;
    height: 100px;
    background-color: white;
    display: flex;
    margin-bottom: 10px;

    .top_box {
      flex: 1;
      display: flex;

      .icon_box {
        flex: 0.5;

        .eding-img {
          width: 60px;
          height: 60px;
          margin-top: 20px;
        }

        img {
          height: 64px;
          width: 64px;
          float: right;
          margin-top: 20px;
          margin-right: 10px;
        }
      }

      .data_box {
        flex: 1;
        display: flex;
        flex-direction: column;
        /* justify-content: center; */
        justify-content: space-evenly;

        .p {
          color: var(--base-color);
          /* margin-top: 20px; */
          /* margin-bottom: 10px; */
          display: block;
          text-align: left;
          font-size: 16px;
          height: 42px;
          display: flex;
          align-items: center;
        }

        span {
          font-size: 24px;
          font-weight: 600;
          text-align: left;
        }

        .unit {
          font-size: 14px;
          font-weight: 400;
        }
      }

    }
  }

  .tabs {
    background-color: white;
    border-radius: 14px;
    padding: 5px 20px;

    .income_box {
      div {
        display: inline-block;
        border: 1px solid #D6D6D6;
        padding: 5px 10px 5px 10px;
        margin-right: 20px;
        border-radius: 6px;
      }
    }

  }
}

.svg-img {
  height: 64px;
  width: 64px;
  float: right;
  margin-top: 20px;
  margin-right: 10px;
}
</style>
