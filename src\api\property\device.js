import request from '@/utils/request'

// 获取项目列表
export function projectList(queryInfo) {
  return request({
    url: '/system/project/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增项目
export function addProject(data) {
  return request({
    url: '/system/project',
    method: 'post',
    data
  })
}

// 修改项目
export function editProject(data) {
  return request({
    url: '/system/project',
    method: 'put',
    data
  })
}

// 获取项目详情
export function getProjectInfo(queryInfo) {
  return request({
    url: `/system/project/${queryInfo.projectId}`,
    method: 'get'
  })
}

// 删除项目
export function deleteProject(queryInfo) {
  return request({
    url: `/system/project/${queryInfo.projectIds}`,
    method: 'delete'
  })
}

// 获取全部项目
export function projectAll() {
  return request({
    url: '/system/project/list2',
    method: 'get'
  })
}