<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-28 18:01:53
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
      <el-descriptions-item>
        {{ $t('monitor.deviceInfo') }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions :column="2" border labelClassName="desc-bot" contentClassName="cont-bot">
      <el-descriptions-item :label="$t('device.model')">
        {{ baseInfo.deviceModel }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('device.type')">
        {{ deviceType }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('monitor.ratedCapacity')">
        <span v-if="bms && bms['bms_4040']">{{ bms['bms_4040'] }}kWh</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- <el-descriptions-item :label="$t('device.version')">
          {{ baseInfo.deviceFactoryVersion }}
        </el-descriptions-item> -->
      <el-descriptions-item :label="$t('monitor.topItem2')" v-if="control.ac == groupId[0]">
        {{ baseInfo.deviceRatedPower }}kW
      </el-descriptions-item>
      <el-descriptions-item :label="$t('monitor.topItem6')"
        v-if="isShowPhotovoltaicInstalledCapacity && control.ac == groupId[0]">
        {{ baseInfo.photovoltaicInstalledCapacity }}kWp
      </el-descriptions-item>
      <el-descriptions-item :label="$t('device.sn')">
        <span v-if="control">{{ control.ac }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1001：bit1、bit2 -->
      <el-descriptions-item :label="$t('monitor.jk_1001Bit1AndBit2')">
        <span v-if="control['jk_1001']">{{ getOnAndOffFn(control['jk_1001']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1002 0正常 1故障 -->
      <el-descriptions-item :label="$t('monitor.jk_1002')">
        <span v-if="control['jk_1002']">{{ control['jk_1002'] == '1' ? $t('common.fault') : $t('common.normal')
        }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1005 0正常 1告警 -->
      <el-descriptions-item :label="$t('monitor.jk_1005')">
        <span v-if="control['jk_1005']">{{ control['jk_1005'] == '1' ? $t('alarm.title') : $t('common.normal') }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1001：bit14 0并网 1离网 -->
      <el-descriptions-item :label="$t('monitor.jk_1001Bit14')">
        <span v-if="control['jk_1001']">{{ get1001Bit14Fn(control['jk_1001']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1092 -->
      <el-descriptions-item :label="$t('monitor.jk_1092')">
        <span v-if="control['jk_1092']">{{ control['jk_1092'] == "1" ? $t('common.running') : $t('common.Closure')
        }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1093 -->
      <el-descriptions-item :label="$t('monitor.jk_1093')">
        <span v-if="control['jk_1093']">{{ control['jk_1093'] == "1" ? $t('common.running') : $t('common.Closure')
        }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1001：bit3、bit4、bit5 全为0正常 -->
      <el-descriptions-item :label="$t('monitor.jk_1001Bit3AndBit4Bit5')">
        <span v-if="control['jk_1001']">{{ getWorkStateFn(control['jk_1001']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1106 -->
      <el-descriptions-item :label="$t(`param['运行模式']`)">
        <span>{{ get11056Fn(control['jk_1105'], control['jk_1001'])['1105'] }}</span>
      </el-descriptions-item>
      <!-- 1105 -->
      <el-descriptions-item :label="$t(`monitor['策略状态']`)">
        <span>{{ get11056Fn(control['jk_1105'], control['jk_1001'])['1001bit15'] }}</span>
      </el-descriptions-item>
      <!-- 电池的总状态 -> 0正常 1故障 -->
      <!-- 电池故障 1004 -->
      <!-- <el-descriptions-item :label="$t('monitor.jk_1004')">
        <span v-if="control['jk_1004']">{{ control['jk_1004'] == '1' ? $t('common.fault') :
      $t('common.normal') }}</span>
        <span v-else>--</span>
      </el-descriptions-item> -->
      <!-- 电池告警 1007 0：正常 1：告警 -->
      <!-- <el-descriptions-item :label="$t('电池告警')">
        <span v-if="control['jk_1007']">{{ control['jk_1007'] == '1' ? $t('alarm.title') :
          $t('common.normal') }}</span>
        <span v-else>--</span>
      </el-descriptions-item> -->
      <!-- 1094：0未上高压 1上高压 -->
      <el-descriptions-item :label="$t('monitor.jk_1094')">
        <span v-if="control['jk_1094']">{{ control['jk_1094'] == '1' ? $t('monitor.highV2') :
          $t('monitor.highV1') }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('monitor.onLineState')">
        <span v-if="control['onLineState'] == '在线'">{{ $t('common.online') }}</span>
        <span v-else-if="control['onLineState'] == '离线'" class="red">{{ $t('common.offline') }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1095：0关闭 -->
      <el-descriptions-item :label="$t('monitor.jk_1095')" v-if="isShowV75019()">
        <span v-if="control['jk_1095']">{{ get1095Fn(control['jk_1095']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.water')" v-if="isShowV75019()">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 2) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.smoke')" v-if="isShowV75019()">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 3) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.thunder')">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 4) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.fire')" v-if="isShowV75019()">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 5) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1031-1033：ABC相加 -->
      <el-descriptions-item :label="$t('monitor.jk_1031')">
        <span v-if="control['jk_1031']">{{ control['power'] }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1051 -->
      <!-- <el-descriptions-item :label="$t('monitor.flowItem1')" v-if="isShowGird">
        <span v-if="flowData.power">{{ flowData.power }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item> -->
      <!-- 1015-1017 -->
      <el-descriptions-item :label="$t('monitor.flowItem3')" v-if="isShowLoad">
        <span v-if="flowData.load">{{ flowData.load }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1015-1017 -->
      <el-descriptions-item :label="$t('monitor.flowItem2')" v-if="isShowPhotovoltaicInstalledCapacity">
        <span v-if="control['jk_1074']">{{ control['jk_1074'] }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1000 -->
      <el-descriptions-item :label="$t('monitor.version')">
        <span v-if="control['jk_1000']">{{ control['jk_1000'] }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('device.address')">
        {{ baseInfo.projectAddress }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t(`common['时区']`)">
        {{ baseInfo[getPropFn] }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import { getOnAndOff, get1001Bit14, getWorkState, get1003, get1001Bit15 } from '@/utils/parseBinaryToText'
import { getDeviceType, isPowerFn, isPhotovoltaicFn, isGirdFn } from '@/hook/useDeviceType'
import { controlAirStatusOptions } from '@/constant'

export default {
  props: {
    id: String
  },
  computed: {
    control() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let data = this.$store.state.monitor.groupList[currentNodeKey]
      if (data && Object.keys(data).length == 11) return data.control
      return {
        ac: "null",
        createTime: null,
        isAnalysis: 0,
        jk_1000: null,
        jk_1001: null,
        jk_1002: null,
        jk_1003: null,
        jk_1004: null,
        jk_1005: null,
        jk_1015: null,
        jk_1016: null,
        jk_1017: null,
        jk_1031: null,
        jk_1032: null,
        jk_1033: null,
        jk_1051: null,
        jk_1056: null,
        jk_1074: null,
        jk_1077: null,
        jk_1092: null,
        jk_1093: null,
        jk_1094: null,
        jk_1095: null,
        jk_1105: null,
        jk_1106: null,
        onLineState: "离线",
        sdt: null,
      }
    },
    baseInfo() {
      return this.$store.state.monitor.baseInfo
    },
    groupId() {
      return this.$route.query.groupId.split(',')
    },
    bms() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let data = this.$store.state.monitor.groupList[currentNodeKey]
      if (!data || Object.keys(data).length != 11) return []
      // data.bms.forEach(item => {
      //   item.name = `${parseInt(item.dc) - 161000 + 1}#BMS`
      // })
      // data.bmsBau.forEach(item => {
      //   item.name = `${parseInt(item.dc) - 241000 + 1}#BMS-BAU`
      // })
      return data.bms.length ? data.bms[0] : data.bmsBau[0]
    },
    flowData() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let type = this.$route.query.groupType.split(',')[currentNodeKey]
      // 电网功率，有sts时1031~1033，无1051
      // let powerBoolean = type == 1 || type == 2 || type == 9 || type == 10
      let powerBoolean = isPowerFn(type)
      let power = powerBoolean ? (this.control.jk_1051 != null ? Number(this.control.jk_1051).toFixed(2) : 0.00) : ((Number(this.control.jk_1031) + Number(this.control.jk_1032) + Number(this.control.jk_1033)).toFixed(2))
      // 直流母线功率，储能变流器，用1056，不是用1077
      // let busBoolean = type == 8 || type == 10
      // let bus = busBoolean ? (this.control.jk_1056 != null ? this.control.jk_1056.toFixed(2) : 0.00) : (this.control.jk_1077 != null ? this.control.jk_1077.toFixed(2) : 0.00)

      return {
        power, // 电网功率
        cell: this.control.jk_1071 != null ? Number(this.control.jk_1071).toFixed(2) : 0.00, //电池功率
        load: (Number(this.control.jk_1015) + Number(this.control.jk_1016) + Number(this.control.jk_1017)).toFixed(2), // 负载功率
        photovoltaic: this.control.jk_1074 != null ? Number(this.control.jk_1074).toFixed(2) : 0.00, // 光伏功率
        // bus: bus, // 直流母线功率
      }
    },
    get1095Fn() {
      return (num) => {
        let value = controlAirStatusOptions.find(item => item.value == num)
        if (value) return value.label
        return '--'
      }
    },
    isShowPhotovoltaicInstalledCapacity() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let type = this.$route.query.groupType.split(',')[currentNodeKey]
      return isPhotovoltaicFn(type)
    },
    isShowLoad() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let type = this.$route.query.groupType.split(',')[currentNodeKey]
      return isPowerFn(type)
    },
    isShowGird() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let type = this.$route.query.groupType.split(',')[currentNodeKey]
      return isGirdFn(type)
    },
    get11056Fn() {
      return (pt1, pt2) => {
        switch (pt1) {
          case '0':
            return {
              // '1001bit15': this.$t(`monitor['未使用']`),
              '1001bit15': this.get1001Bit15Fn(pt2) == this.$t(`common['停止']`) ? this.$t(`monitor['未使用']`) : this.get1001Bit15Fn(pt2),
              '1105': this.$t(`param['手动模式']`),
            }
          case '1':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t(`param['削峰填谷']`),
            }
          case '2':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t(`param['后备模式']`),
            }
          case '3':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t('动态扩容'),
            }
          case '4':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t('光伏消纳'),
            }
          default:
            return {
              '1001bit15': '--',
              '1105': '--',
            }
        }
      }
    },
    deviceType() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let type = this.$route.query.groupType.split(',')[currentNodeKey]
      let data = ''
      data = getDeviceType(type, true)
      return data
    },
    getPropFn() {
      let lang = this.$store.getters.language
      switch (lang) {
        case 'zh':
          return 'timeZoneAddress'
        case 'en':
          return 'timeZoneAddressUs'
        case 'it':
          return 'timeZoneAddressIt'
      }
    }
  },
  methods: {
    get1001Bit15Fn(num) {
      return get1001Bit15(num)
    },
    getOnAndOffFn(num) {
      return getOnAndOff(num)
    },
    get1001Bit14Fn(num) {
      return get1001Bit14(num)
    },
    getWorkStateFn(num) {
      return getWorkState(num)
    },
    get1003Fn(num, bit) {
      return get1003(num, bit)
    },
    isShowV75019() {
      let versionStart = this.control?.jk_1000?.split('V')[1].split('.')[0]
      let versionTwo = this.control?.jk_1000?.split('V')[1].split('.')[1]
      let versionThere = this.control?.jk_1000?.split('V')[1].split('.')[2]
      if (versionStart == 7) if (versionTwo == 5019) return false
      return true
    }
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
  overflow: auto;
}
</style>
