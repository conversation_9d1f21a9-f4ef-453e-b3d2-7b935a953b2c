/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-10 10:59:37
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-01-10 11:01:10
 * @FilePath: \elecloud_platform-main\src\api\property\sn.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取设备列表
export function deviceList(queryInfo) {
  return request({
    url: '/system/device/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/system/device',
    method: 'post',
    data
  })
}

// 修改设备
export function editDevice(data) {
  return request({
    url: '/system/device',
    method: 'put',
    data
  })
}

// 获取设备详情
export function getDeviceInfo(queryInfo) {
  return request({
    url: `/system/device/${queryInfo.deviceId}`,
    method: 'get'
  })
}

// 删除设备
export function deleteDevice(queryInfo) {
  return request({
    url: `/system/device/${queryInfo.deviceIds}`,
    method: 'delete'
  })
}

// 查询未绑定的设备
export function getBandingList(queryInfo) {
  return request({
    url: '/system/device/bindingList',
    method: 'get',
    params: queryInfo
  })
}

/**
 * admin
 */
// 获取设备列表
export function deviceAdminList(queryInfo) {
  return request({
    url: '/system/admin/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增设备
export function addAdminDevice(data) {
  return request({
    url: '/system/admin',
    method: 'post',
    data
  })
}

// 修改设备
export function editAdminDevice(data) {
  return request({
    url: '/system/admin',
    method: 'put',
    data
  })
}

// 获取设备详情
export function getAdminDeviceInfo(queryInfo) {
  return request({
    url: `/system/admin/${queryInfo.deviceId}`,
    method: 'get'
  })
}

// 删除设备
export function deleteAdminDevice(queryInfo) {
  return request({
    url: `/system/admin/${queryInfo.deviceIds}`,
    method: 'delete'
  })
}

// 根据设备sn码获取admin设备详细信息
export function getAdminDeviceByAcInfo(queryInfo) {
  return request({
    url: `/system/admin/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}

// 恢复出厂设备
export function recover(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/clearData/${queryInfo.deviceSerialNumber}/${queryInfo.timeZone}/${queryInfo.deviceType}`,
    method: 'get'
  })
}
