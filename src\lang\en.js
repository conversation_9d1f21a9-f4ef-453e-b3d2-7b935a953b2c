// en.js
export default {
  login: {
    title: 'Elecloud',
    logIn: 'Log in',
    username: '<PERSON><PERSON><PERSON>',
    password: 'Password',
    code: 'Code',
    vUserName: 'Please enter your username',
    vPassword: 'Please enter your password',
    vCode: 'Please enter code',
    rememberPwd: 'Remember Pwd',
    loging: 'Logging in',
    company: 'Shenzhen Elecod Electric Co.,Ltd. All right reserved',
    logOut: 'Are you sure you want to exit the system?'
  },
  tagsView: {
    refresh: 'Refresh',
    close: 'Close',
    closeOthers: 'Close Others',
    closeAll: 'Close All',
    home: 'Home'
  },
  settings: {
    title: 'Page style setting',
    theme: 'Theme Color',
    tagsView: 'Open Tags-View',
    fixedHeader: 'Fixed Header',
    sidebarLogo: 'Sidebar Logo',
    screen: 'Full Screen'
  },
  // 数据概括
  home: {
    title: 'Data Generalization',
    item1: 'Total Installed Capacity',
    item2: 'Total Installed Power',
    item3: 'Cumulative Charge Capacity',
    item4: 'Cumulative Discharge Capacity',
    pieTitle: 'Failure Ratio',
    tableTitle: 'Project Summary',
    pieRadio1: 'Power Station',
    pieRadio2: 'Device',
    pieRadioText1: 'Total Number Of Power Stations',
    pieRadioText2: 'Total Number Of Devices',
    '总收益': 'Total Revenue'
  },
  common: {
    search: 'Search',
    confirm: 'Confirm',
    cancel: 'Cancel',
    systemPrompt: 'System Prompt',
    normal: 'Normal',
    fault: 'Fault',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    reset: 'Reset',
    handle: 'Operate',
    createTime: 'Create Time (UTC+08:00)',
    status: 'State',
    unit: 'Unit',
    detailAddress: 'Full address',
    'Please enter': 'Please enter',
    offline: 'Offline',
    online: 'Online',
    all: 'All',
    save: 'Save',
    'Added successfully': 'Added successfully!',
    'Addition Failed': 'Addition Failed!',
    'Modify successfully': 'Modify successfully!',
    'Change failed': 'Change failed!',
    'Deleted successfully': 'Deleted successfully!',
    'Deleted Failed': 'Deleted Failed!',
    'Deletion Cancelled': 'Deletion Cancelled',
    select: 'Select',
    check: 'Check',
    TurnOn: 'Running',
    Closure: 'Closure',
    GridConnection: 'Grid connection',
    OffGrid: 'Off-grid',
    Shutdown: 'Shutdown',
    running: 'Running',
    'Confirm addition': 'Confirm addition',
    submit: 'Submit',
    back: 'Back',
    sele: 'Select',
    BatteryStartable: 'Battery Startable',
    BatteryNoStartable: 'Battery does not start',
    Discharge: 'Discharge',
    Charge: 'Discharge',
    Enable: 'Enable',
    Deactivate: 'Deactivate',
    Closure2: 'Closure',
    switchOff: 'Switch Off',
    export: 'Export',
    exportReport: 'Export Report',
    '时区': 'Time Zone',
    '时区地址': 'Time Zone Adress',
    '停止': 'Stop'
  },
  date: {
    week: 'Week',
    month: 'Month',
    year: 'Year',
    day: 'Day',
    lastWeek: 'Last week',
    lastMonth: 'Last month',
    last3Month: 'Last 3 months',
    to: 'To',
    start: 'Start Date',
    end: 'End Date'
  },
  DataSummary: 'Data Summary',
  faultMessage: 'Fault Message',
  // 设备监控
  monitor: {
    topItem1: 'Installed Battery  Capacity',
    topItem2: 'Rated Power',
    topItem3: 'System Running Status',
    topItem4: 'Energy Storage Charge Capacity',
    topItem5: 'Energy Storage Discharge Capacity',
    topItem6: 'PV Installed Capacity',
    topItem7: 'PV Power Generation',
    flowItem1: 'Grid Power',
    flowItem2: 'PV Power',
    flowItem3: 'Load Power',
    flowItem4: 'Battery Power',
    flowItem5: 'DC Bus Power',
    lineTitle: 'Power Analysis',
    barTitle: 'Power Statistics',
    barItem1: 'Charge Capacity',
    barItem2: 'Discharge Capacity',
    Photovoltaic: 'PV',
    energyStorage: 'Energy Storage',
    PhotovoltaicStorage: 'PV Storage',
    voltage: 'Voltage',
    temperature: 'Temperature',
    cell: 'Cell',
    ratedCapacity: 'Rated Capacity',
    OnlineStatus: 'Online Status',
    WorkingStatus: 'Working Status',
    'Battery rechargeable': 'Battery rechargeable',
    'Battery rechargeable no': 'Battery is not chargeable',
    'Battery dischargeable': 'Battery dischargeable',
    'Battery dischargeable no': 'The battery cannot be discharged',
    // 本地控制器
    control: 'Local Controller',
    deviceInfo: 'Device Information',
    jk_1001Bit1AndBit2: 'System Working Mode',
    jk_1002: 'System Fault Status',
    jk_1005: 'System Alarm Status',
    jk_1001Bit14: 'On-Grid And Off-Grid Status',
    jk_1092: 'AC Running Status',
    jk_1093: 'DC Running Status',
    jk_1001Bit3AndBit4Bit5: 'System Status',
    jk_1004: 'Battery Status',
    jk_1094: 'Battery High Voltage Status',
    onLineState: 'Communication Status',
    jk_1095: 'Air Conditioner Status',
    water: 'Flooded State',
    smoke: 'Smoke State',
    thunder: 'Lightning Protection',
    fire: 'Fire Fighting Action',
    jk_1031: 'Energy Storage Power',
    version: 'Software Version',
    baseInfo: 'Basic Information',
    'Total reactive power of the grid': 'Total reactive power of the grid',
    'Total active power of the grid': 'Total active power of the grid',
    'Grid Frequency A': 'Grid Frequency A',
    'Grid Frequency B': 'Grid Frequency B',
    'Grid Frequency C': 'Grid Frequency C',
    highV1: 'Not on the high pressure',
    highV2: 'On the high pressure',
    Blowers: 'Blowers',
    Cooling: 'Cooling',
    HeatUp: 'Heating',
    Dehumidify: 'Dehumidify',
    '策略状态': 'Policy State',
    '未使用': 'Unused',
    '未运行': 'Not Run',
    // AC
    ac_2010: 'Operating Mode',
    ac_2015Bit5: 'Discharge',
    ac_2015Bit6: 'Running Status',
    ac_2015Bit7: 'Fault Status',
    ac_2039: 'DC Voltage',
    ac_2040: 'DC Current',
    ac_2041: 'DC Power',
    ac_2042: 'Line Voltage Vab',
    ac_2043: 'Line Voltage Vbc',
    ac_2044: 'Line Voltage Vca',
    ac_2045: 'A Phase Current',
    ac_2046: 'B Phase Current',
    ac_2047: 'C Phase Current',
    ac_2049: 'Active power',
    ac_2050: 'Reactive power',
    ac_2061: 'Inspecting Power',
    ac_2048: 'Frequency',
    ac_2056: 'igbt Temperature',
    ac_2051: 'Power Factor',
    ac_2057: 'Positive electrode to ground impedance value',
    ac_2058: 'Negative pole to ground impedance value',
    acVersion: 'Version Number',
    // DC
    dcJoin: 'Access Device',
    dc_3012: 'DC low voltage side 1 voltage',
    dc_3013: 'DC low voltage side 1 current',
    dc_3014: 'DC low voltage side 1 channel power',
    dc_3015: 'DC low voltage side 2 voltage',
    dc_3016: 'DC low voltage side 2 current',
    dc_3017: 'DC low voltage side 2 channel power',
    dc_3018: 'DC high side voltage',
    dc_3019: 'DC high voltage side current',
    dc_3020: 'DC high voltage side power',
    highType: 'High voltage side type',
    lowType: 'Low voltage side type',
    Lbattery: 'Lithium-ion battery',
    DCbusbar: 'DC Busbar',
    DCsource: 'DC Source',
    '铅酸电池': 'Lead Battery',
    // STS
    sts_3500: 'Communication Status',
    deviceStatus: 'Device Status',
    sts_3507: 'Grid Voltage A(AB)',
    sts_3508: 'Grid Voltage B(BC)',
    sts_3509: 'Grid Voltage C(CA)',
    sts_3511: 'Grid Current A',
    sts_3512: 'Grid Current B',
    sts_3513: 'Grid Current C',
    sts_3519: 'Grid Active Power A',
    sts_3520: 'Grid Active Power B',
    sts_3521: 'Grid Active Power C',
    sts_3523: 'Grid Reactive Power A',
    sts_3524: 'Grid Reactive Power B',
    sts_3525: 'Grid Reactive Power C',
    sts_3522: 'The power grid always works',
    sts_3526: 'Total reactive power of the grid',
    sts_3534: 'Total apparent power of the power grid',
    sts_3531: 'Grid Apparent Power A',
    sts_3532: 'Grid Apparent Power B',
    sts_3533: 'Grid Apparent Power C',
    sts_3515: 'Load Voltage A(AB)',
    sts_3516: 'Load Voltage B(BC)',
    sts_3517: 'Load Voltage C(CA)',
    sts_3530: 'Total Power Factor',
    sts_3518: 'Grid Frequency',
    // BMS
    bmsCS: 'BMS Communication Status',
    bms_4020: 'Battery Voltage',
    bms_4021: 'Battery Current',
    bms_4036: 'Battery Rechargeable Capacity',
    bms_4030: 'Charge Limit Current',
    bms_4031: 'Discharge Limit Current',
    bms_4037: 'Battery Discharge Capacity',
    bms_4025: 'Minimum Cell Voltage',
    bms_4024: 'Maximum Cell Voltage',
    bms_4026: 'Average Cell Voltage',
    bms_4027: 'Maximum Cell Temperature',
    bms_4028: 'Minimum Cell Temperature',
    bms_4029: 'Average Cell Temperature',
    bms_4043: 'Minimum Cell Voltage Position',
    bms_4041: 'Highest Cell Voltage Position',
    bms_4054: 'Cell Voltage Difference',
    bms_4045: 'Maximum Cell Temperature Position',
    bms_4047: 'Minimum Cell Temperature Position',
    bms_4053: 'Cell Temperature Difference',
    bms_4038: 'Ambient Temperature (Battery Compartment)',
    bms_4055: 'Ambient Humidity (Battery Compartment)',
    bms_4039: 'Insulation Impedance Value',
    // 电表
    amTitle: 'Ammeter',
    // 电芯
    cell: 'Battery Cell',
    '主机': 'Master',
    '从机': 'Slave',
    '组合类型': 'Combination Type',
    '组合序列号': 'Combined Sequence Number',
    '组合光储系统': 'Combined PV energy storage system',
    '组合光伏控制系统': 'Combined PV control systems(MPPT)',
    '组合储能系统': 'Combined Energy storage system',
  },
  project: {
    name: 'Project Name',
    address: 'Project Address',
    area: 'Area',
    lonAndLat: 'Latitude And Longitude',
    sn: 'SN',
    'Please enter project name': 'Please enter project name',
    'Add item': 'Add Item',
    'Modify Project': 'Modify Project',
    'Please enter full address': 'Please enter full address',
    'Please enter project address': 'Please enter project address',
    'Please enter longitude': 'Please enter longitude',
    'Please enter latitude': 'Please enter latitude',
  },
  device: {
    title: '设备入库',
    name: 'Device Name',
    screenId: 'Machine Serial Number',
    model: 'Device Model',
    type: 'Device Type',
    cellCapacity: 'Battery Capacity',
    software: 'Factory Software Version',
    hardware: 'Factory Hardware Version',
    CustomerOrder: 'Customer Order Number',
    order: 'Order Number',
    orderDetail: 'Special Instructions For Orders',
    module: 'Module Serial Number',
    power: 'Real-time Power (kW)',
    version: 'Protocol Version',
    info: 'Device Info',
    address: 'Device Address',
    sn: 'SN',
    selectDeviceTitle: 'Select Device',
    deleteHint: `This operation will permanently delete the project, and the equipment records of the
project will be retained.If you want to delete the equipment, please go to the equipment storage to delete it! Whether to continue?`,
    deleteDeviceHint: 'This operation will permanently log out the device. Do you want to continue?',
    'Please enter device serial number': 'Please enter device serial number',
    'Please enter machine serial number (screen)': 'Please enter machine serial number (screen)',
    'Please enter device name': 'Please enter device name',
    'Please enter device model': 'Please enter device model',
    'Please enter photovoltaic installed capacity': 'Please enter photovoltaic installed capacity',
    'Please enter battery capacity': 'Please enter battery capacity',
    'Please enter rated power': 'Please enter rated power',
    'Please enter factory software version': 'Please enter factory software version',
    'Please enter factory hardware version': 'Please enter factory hardware version',
    'Please enter order number': 'Please enter order number',
    'Please enter special instructions for orders': 'Please enter special instructions for orders',
    'Please enter module serial number': 'Please enter module serial number',
    'Please enter customer order number': 'Please enter customer order number',
    AddDevice: 'Add Device',
    ModifyDevice: 'Modify Device',
    QueryingDevice: 'Querying Device',
    exceed: 'More Than 10'
  },
  // 告警
  alarm: {
    peStatus: 'Pending',
    prStatus: 'Processed',
    level: 'Alarm Level',
    sn: 'SN',
    status: 'Alarm Status',
    time: 'Start Time',
    reporting: 'Reporting Time (UTC+08:00)',
    name: 'Alarm Name',
    object: 'Alarm Object',
    belong: 'Affiliated Projects',
    'Please enter alarm name': 'Please enter alarm name',
    level1: 'Level One',
    level2: 'Level Two',
    dispose: 'Deal With',
    'Processed successfully!': 'Processed successfully!',
    'Are you sure you want to handle this exception manually?': 'Are you sure you want to handle this exception manually?',
    title: 'Alarm'
  },
  PersonalCenter: 'Personal Center',
  SignOut: 'Sign Out',
  outLint: 'This operation will permanently log out the device. Do you want to continue?',
  '设备详情': 'Device Details',
  // 网络
  axios: {
    limit: `The requested data size exceeds the allowed 5M limit, and the anti-duplicate submission
 verification cannot be performed.`,
    repetition: 'Data is being processed, please do not resubmit.',
    date: 'The login status has expired. You can continue to stay on this page or log in again.',
    anew: 'Re-register',
    invalid: 'Invalid session, or the session has expired, please log in again.',
    error: 'Backend interface connection abnormality.',
    timeout: 'System interface request timeout.',
    interface: 'System interface',
    errorText: 'abnormal',
    being: 'Downloading data, please wait.',
    downloadError: 'There was an error downloading the file, please contact the administrator!'
  },
  // 用户管理
  user: {
    name: 'User Name',
    nickname: `User's Nickname`,
    phone: 'Phone Number',
    id: 'User ID',
    dept: 'Department',
    handlePwd: 'Reset Password',
    handleRole: 'Assigning Roles',
    post: 'Post',
    belongDept: 'Belonging Department',
    'Please enter user name': 'Please enter user name',
    'Please enter user nickname': 'Please enter user nickname',
    'Please enter phone number': 'Please enter phone number',
    'Please enter email': 'Please enter email',
    'Please enter user password': 'Please enter user password',
    'Please enter content': 'Please enter content',
    'Please select post': 'Please select post',
    'Please select belonging department': 'Please select belonging department',
    'Please select role': 'Please select role',
    userState: 'User State',
    pwd: 'User Password',
    role: 'Role',
    remark: 'Remark',
    email: 'Email',
    drag: 'Drag files here,',
    or: 'or',
    click: 'Click to Upload',
    update: 'Whether to update existing user data',
    limit: 'Only xls and xlsx format files are allowed to be imported.',
    template: 'Download Template',
    'Username cannot be empty': 'Username cannot be empty',
    'Nickname cannot be empty': 'Nickname cannot be empty',
    'User password cannot be empty': 'User password cannot be empty',
    'Username length must be between 2 and 20': 'Username length must be between 2 and 20',
    'User password length must be between 5 and 20': 'User password length must be between 5 and 20',
    'Please enter a valid email address': 'Please enter a valid email address',
    'Please enter a valid mobile phone number': 'Please enter a valid mobile phone number',
    'Please enter your new password': 'Please enter your new password',
    importUser: 'User Import',
    importResult: 'Import results',
    personInfo: 'Personal Information',
    userEmail: 'User Email',
    belongRole: 'Role',
    createDate: 'Create Date',
    changePwd: 'Change Password',
    oldPassword: 'Old Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    two: 'The passwords entered twice are inconsistent',
    'Please enter your old password': 'Please enter your old password',
    'Please confirm the new password': 'Please confirm the new password',
    'Old password cannot be empty': 'Old password cannot be empty',
    'New password cannot be empty': 'New password cannot be empty',
    'Confirm password cannot be empty': 'Confirm password cannot be empty',
    '6 to 20 characters in length': '6 to 20 characters in length',
    '5 to 20 characters in length': '5 to 20 characters in length',
    clickUploadAvatar: 'Click to upload avatar',
    editAvatar: 'Edit Avatar',
    formatError: 'The file format is wrong, please upload the image type, such as: JPG, PNG suffix file.'
  },
  'Data Analysis': 'Data Analysis',
  // 角色管理
  role: {
    status: 'Role State',
    id: 'Role Number',
    name: 'Role Name',
    chart: 'Permission Characters',
    show: 'Display Order',
    data: 'Data Permission',
    user: 'Assign Users',
    expand: 'Expand/Collapse',
    scope: 'Scope Of Authority',
    'Please enter role name': 'Please enter role name',
    'Please enter permission characters': 'Please enter permission characters',
    order: 'Role Order',
    perm: 'Menu Permissions',
    select: 'Select all/Unselect all',
    father: 'Father and son linkage',
    loading: 'Loading, please wait',
    all: 'All data permissions',
    custom: 'Custom data permissions',
    ownDept: 'Data permissions of this department',
    ownDeptDown: 'Data permissions for this department and the following',
    own: 'Only personal data permissions',
    'Role name cannot be empty': 'Role name cannot be empty',
    'Permission Characters cannot be empty': 'Permission Characters cannot be empty',
    'Role Order cannot be empty': 'Role Order cannot be empty',
    addRole: 'Add role',
    editRole: 'Edit role',
    assign: 'Assign data permissions',
    'Permission characters defined in the controller, such as:': 'Permission characters defined in the controller, such as:'
  },
  // 数据对比
  contrast: {
    jk_1025: 'Energy Storage Side Voltage A(AB)',
    jk_1026: 'Energy Storage Side Voltage B(BC)',
    jk_1027: 'Energy Storage Side Voltage C(CA)',
    jk_1028: 'Energy Storage Side A Phase Current',
    jk_1029: 'Energy Storage Side B Phase Current',
    jk_1030: 'Energy Storage Side C Phase Current',
    jk_1031: 'Active power of phase A on energy storage side',
    jk_1032: 'Active power of phase B on energy storage side',
    jk_1033: 'Active power of phase C on energy storage side',
    jk_1034: 'Reactive power of phase A on energy storage side',
    jk_1035: 'Reactive power of phase B on energy storage side',
    jk_1036: 'Reactive power of phase C on energy storage side',
    jk_1037: 'Energy storage side frequency A',
    jk_1038: 'Energy storage side frequency B',
    jk_1039: 'Energy storage side frequency C',
    jk_1040: 'Energy storage side total power factor',
    jk_1041: 'Grid Voltage A(AB)',
    jk_1042: 'Grid Voltage B(BC)',
    jk_1043: 'Grid Voltage C(CA)',
    jk_1044: 'AC phase A current',
    jk_1045: 'AC phase B current',
    jk_1046: 'AC phase C current',
    jk_1047: 'Grid Frequency A',
    jk_1048: 'Grid Frequency B',
    jk_1049: 'Grid Frequency C',
    jk_1050: 'Total power factor',
    jk_1051: 'Total reactive power of the grid',
    jk_1052: 'Total active power of the grid',
    jk_1053: 'Total apparent power of the power grid',
    jk_1072: 'PV Voltage',
    jk_1073: 'PV Current',
    jk_1074: 'PV Power',
    bms_4038: 'Warehouse ambient temperature',
    bms_4055: 'Ambient humidity in the warehouse',
    bms_4027: 'Maximum battery cell temperature',
    bms_4028: 'Minimum battery cell temperature',
    bms_4024: 'Maximum voltage of battery cell',
    bms_4025: 'Minimum voltage of battery cell',
    bms_4022: 'Battery level SOC',
    bms_4020: 'Battery Voltage',
    bms_4021: 'Battery Current',
    bms_4056: 'Battery power',
    bms_4030: 'Charge Limit Current',
    bms_4031: 'Discharge Limit Current',
    param: 'Parameter',
    AC_2039: 'DC Voltage',
    AC_2040: 'DC Current',
    AC_2041: 'DC Power',
    AC_2042: 'Line Voltage Vab',
    AC_2043: 'Line Voltage Vbc',
    AC_2044: 'Line Voltage Vca',
    AC_2045: 'A Phase Current',
    AC_2046: 'B Phase Current',
    AC_2047: 'C Phase Current',
    AC_2049: 'Active power',
    AC_2050: 'Reactive power',
    AC_2061: 'Inspecting Power',
    AC_2048: 'Frequency',
    AC_2056: 'igbt Temperature',
    AC_2051: 'Power Factor',
    DC_3012: 'DC low voltage side 1 voltage',
    DC_3013: 'DC low voltage side 1 current',
    DC_3014: 'DC low voltage side 1 channel power',
    DC_3015: 'DC low voltage side 2 voltage',
    DC_3016: 'DC low voltage side 2 current',
    DC_3017: 'DC low voltage side 2 channel power',
    DC_3018: 'DC high side voltage',
    DC_3019: 'DC high voltage side current',
    DC_3020: 'DC high voltage side power',
    DC_3025: 'igbt Temperature',
    em_10003: 'Electric Meter Online Status',
    em_10005: 'A Phase Voltage',
    em_10006: 'B Phase Voltage',
    em_10007: 'C Phase Voltage',
    em_10008: 'A Phase Current',
    em_10009: 'B Phase Current',
    em_10010: 'C Phase Current',
    em_10011: 'Power Factor',
    em_10012: 'Active power',
    em_10013: 'Reactive power',
    em_10014: 'Inspecting Power',
    em_10015: 'Forward Active Energy',
    em_10016: 'Reverse Active Energy',
    em_10017: 'Forward Reactive Energy',
    em_10018: 'Reverse Reactive Energy',
    em_10019: 'Peak Positive Active Power Energy',
    em_10020: 'Peak Reverse Active Power Energy',
    em_10021: 'Peak Forward Reactive Energy',
    em_10022: 'Peak Reverse Reactive Energy',
    em_10023: 'Valley Positive Active Energy',
    em_10024: 'Valley Reverse Active Energy',
    em_10025: 'Valley Forward Reactive Energy',
    em_10026: 'Valley Reverse Reactive Energy',
    em_10027: 'Flat Positive Active Power Energy',
    em_10028: 'Flat Reverse Active Power Energy',
    em_10029: 'Flat Forward Reactive Energy',
    em_10030: 'Flat Reverse Reactive Energy',
    em_10031: 'Sharp Positive Active Power Energy',
    em_10032: 'Sharp Reverse Active Power Energy',
    em_10033: 'Sharp Forward Reactive Energy',
    em_10034: 'Sharp Reverse Reactive Energy',
    em_10035: 'Frequency',
    em_10036: 'Total Forward Power',
    em_10037: 'Total Reverse Power',
    em_10038: 'DC Voltage',
    em_10039: 'DC Current',
    em_10040: 'DC Power',
    em_10041: 'AB Line Voltage',
    em_10042: 'BC Line Voltage',
    em_10043: 'CA Line Voltage',
    chargingPile_19003: 'Total Output Power',
    chargingPile_19004: '1# Gun Output Voltage',
    chargingPile_19005: '1# Gun Output Current',
    chargingPile_19006: '1# Gun Output Power',
    chargingPile_19007: '1# Gun Current SOC',
    chargingPile_19008: '1# Gun Charging Capacity',
    chargingPile_19009: '1# Gun Charge Amount',
    chargingPile_19010: '1# Gun Battery Maximum Temperature',
    chargingPile_19015: '2# Gun Output Voltage',
    chargingPile_19016: '2# Gun Output Current',
    chargingPile_19017: '2# Gun Output Power',
    chargingPile_19018: '2# Gun Current SOC',
    chargingPile_19019: '2# Gun Charging Capacity',
    chargingPile_19020: '2# Gun Charge Amount',
    chargingPile_19021: '2# Gun Battery Maximum Temperature',
    sts_3507: 'Grid Voltage A(AB)',
    sts_3508: 'Grid Voltage B(BC)',
    sts_3509: 'Grid Voltage C(CA)',
    sts_3510: 'Transformer Temperature',
    sts_3511: 'Grid Current A',
    sts_3512: 'Grid Current B',
    sts_3513: 'Grid Current C',
    sts_3514: 'Environment Temperature',
    sts_3515: 'Load Voltage A(AB)',
    sts_3516: 'Load Voltage B(BC)',
    sts_3517: 'Load Voltage C(CA)',
    sts_3518: 'Grid Frequency',
    sts_3519: 'Grid Active Power A',
    sts_3520: 'Grid Active Power B',
    sts_3521: 'Grid Active Power C',
    sts_3522: 'The power grid always works',
    sts_3523: 'Grid Reactive Power A',
    sts_3524: 'Grid Reactive Power B',
    sts_3525: 'Grid Reactive Power C',
    sts_3526: 'Total reactive power of the grid',
    sts_3527: ' Power Factor A',
    sts_3528: ' Power Factor B',
    sts_3529: ' Power Factor C',
    sts_3530: 'Total Power Factor',
    sts_3531: 'Grid Apparent Power A',
    sts_3532: 'Grid Apparent Power B',
    sts_3533: 'Grid Apparent Power C',
    sts_3534: 'Total apparent power of the power grid',
  },
  // 菜单管理
  menu: {
    status: 'Menu State',
    name: 'Menu Name',
    EnglishName: 'English Name',
    icon: 'Icon',
    sort: 'Sort',
    perms: 'Permission ID',
    com: 'Component Path',
    parent: 'Previous Menu',
    type: 'Menu Type',
    type1: 'Table of contents',
    type2: 'Menu',
    type3: 'Button',
    menuIcon: 'Menu Icon',
    show: 'Display Order',
    out: 'Whether to external link',
    yes: 'YES',
    no: 'NO',
    router: 'Routing address',
    param: 'Routing parameters',
    strorg: 'Whether to cache',
    showStatus: 'Display State',
    'Please enter menu name': 'Please enter menu name',
    'Please enter routing address': 'Please enter routing address',
    'Please enter component Path': 'Please enter component Path',
    'Please enter permission id': 'Please enter permission iD',
    'Please enter routing parameters': 'Please enter routing parameters',
    'Select previous menu': 'Select previous menu',
    'Click Select icon': 'Click Select icon',
    'Main category': 'Main category',
    addMenu: 'Add Menu',
    editMenu: 'Edit Menu',
    cache: 'Cache',
    uncached: 'Uncached',
    'Menu name cannot be empty': 'Menu name cannot be empty',
    'Menu sort cannot be empty': 'Menu sort cannot be empty',
    'Route address cannot be empty': 'Route address cannot be empty',
    'Are you sure to delete the data item?': 'Are you sure to delete the data item?',
    "If you select external link, the routing address needs to start with http(s)://": 'If you select external link, the routing address needs to start with `https)://`',
    'The accessed routing address, such as: user, if the external network address requires internal link access, it starts with http(s)://': 'The component path to be accessed, such as: `system/user/index`, which is in the `views` directory by default',
    'The component path to be accessed, such as: system/user/index, which is in the views directory by default': 'The component path to be accessed, such as: `system/user/index`, which is in the `views` directory by default',
    'The authority characters defined in the controller, for example:': 'The authority characters defined in the controller, for example:',
    'Default passed parameters for access routes, such as:': 'Default passed parameters for access routes, such as:',
    "If Yes is selected": "If `Yes` is selected, it will be cached by `keep-alive` and needs to match the component's `name` and address.",
    'Select Hide and the route will not appear in the sidebar but will still be accessible.': 'Select Hide and the route will not appear in the sidebar but will still be accessible.',
    "If Disable is selected, the route will not appear in the sidebar and cannot be accessed": "If 'Disable' is selected, the route will not appear in the sidebar and cannot be accessed",
    'Activated successfully!': 'Activated successfully!',
    'Deactivation successfully!': 'Deactivation successfully!',
    'Are you sure you want to enable it?': 'Are you sure you want to enable it?',
    'Are you sure you want to deactivate it?': 'Are you sure you want to deactivate it?',
    assign: 'Assign Users',
    addUser: 'Add user',
    editUser: 'Edit User',
    batch: 'Batch deauthorization',
    canPerm: 'Cancel authorization',
    'Are you sure you want to cancel the role of this user?': 'Are you sure you want to cancel the role of this user?',
    canPerSuccess: 'Deauthorization successful!',
    'Do you want to uncheck the user authorization data item?': 'Do you want to uncheck the user authorization data item?',
    selectUser: 'Select User',
    'Please select a user to assign': 'Please select a user to assign',
    'Authorization successful!': 'Authorization successful!',
    account: 'Login Account',
    roleInfo: 'Role information'
  },
  // 部门管理
  dept: {
    status: 'Department State',
    name: 'Department Name',
    parent: 'Superior Department',
    principal: 'Responsible Person',
    phone: 'Phone',
    'Superior department cannot be empty': 'Superior department cannot be empty',
    'Department name cannot be empty': 'Department name cannot be empty',
    'Display order cannot be empty': 'Display order cannot be empty',
    'Please enter department name': 'Please enter department name',
    'Please enter responsible Person': 'Please enter responsible Person',
    'Please enter phone': 'Please enter phone',
    'Select superior department': 'Select superior department',
    addDept: 'Add Department',
    editDept: 'Edit Department'
  },
  // 设备类型
  deviceType: {
    type1: 'PV energy storage system(on&off grid)',
    type2: 'PV+storage hybrid Inverter(on&off grid)',
    type3: 'Battery System',
    type4: 'PV control systems(MPPT)',
    type5: 'PV+storage hybrid Inverter(on grid)',
    type6: 'PV energy storage system(on grid)',
    type7: 'Energy storage system(on grid)',
    type8: 'Energy storage converter(on grid)',
    type9: 'Energy storage system(on&off grid)',
    type10: 'Energy storage converter(on&off grid)',
  },
  export: {
    dateType: 'Date Type',
    selectDate: 'Select Date',
    selectWeek: 'Select Week',
    selectMonth: 'Select Month',
    selectYear: 'Select Year',
    exportDetail: 'Export Details',
    fileSuffix: 'electricity statistics report',
    'Please select date': 'Please select date'
  },
  sim: {
    card: 'IoT Card Number',
    status: 'IoT Card Status',
    plan: 'Data Plan',
    use: 'Monthly Used Traffic (MB)',
    residue: 'Monthly Remaining Traffic (MB)',
    expire: 'Expiration Date',
    topUp: 'Top Up',
    unBind: 'Unbundle',
    cActive: 'Can Be Activated',
    active: 'Activated',
    test: 'Testable',
    off: 'Write Off',
    stock: 'In Stock',
    pre: 'Pre-Write-Off',
    maintaining: 'Maintaining',
    'Please enter iot card number': 'Please enter iot card number',
    'Please select iot card status': 'Please select iot card status',
    'Please enter binding item': 'Please enter binding item',
    Bsn: 'Binding SN',
    startTime: 'Start Time',
    endTime: 'End Time',
    sn: 'SN',
    add: 'Add SIM Card'
  },
  // 收益统计
  bill: {
    '总收益(元)': 'Total Revenue (CNY)',
    '储能充电收益(元)': 'Energy Storage Charging Income (CNY)',
    '储能放电收益(元)': 'Energy Storage Discharge Income(CNY)',
    '电网放电收益(元)': 'Grid Discharge Income(CNY)',
    '电网充电收益(元)': 'Grid Charging Income(CNY)',
    '光伏发电量收益(元)': 'PV Power Generation Income(CNY)',
    '等效植树数(棵)': 'Equivalent Planting (Trees)',
    '等效减排co2(t)': 'Equivalent CO2 reduction (T)',
    '更新时间': 'Update Time',
    '合计': 'Sum',
    '放电量(kWh)': 'Discharge Capacity (kWh)',
    '充电量(kWh)': 'Charge Capacity (kWh)',
    '光伏发电量(kWh)': 'PV Power Generation (kWh)',
    '统计时间': 'Statistical Time',
    '棵': 'Trees',
  },
  // 备电方案
  backup: {
    '方案名称': 'Program Name',
    '电网充电': 'Grid Charging',
    '发电机': 'Generator',
    '发电机充电': 'Generator Charging',
    '电池充电功率': 'Battery Charging Power',
    '备电保持SOC': 'Standby Power Reserve SOC',
    '使用电网给电池充电': 'Charging the battery using the grid',
    '电池没电时使用发电机': 'Use generator when battery is low',
    '在发电机工作时给电池充电': 'Charging the battery while the generator is working',
    '使用电网、油机给电池充电的功率（不限制光伏功率）': 'Power to charge batteries using grid, generator (no limit on PV power)',
    '有电网时，电池保留SOC电量，SOC之上电量可给负载使用': 'When connected to the grid, the battery retains SOC power and power above SOC is available to the loads',
    '使能': 'Enabled',
    '不使能': 'Disable',
    '请输入方案名称': 'Please enter program name',
    '请选择电网充电': 'Please select grid charging',
    '请选择发电机': 'Please select generator',
    '请选择发电机充电': 'Please select generator charging',
    '请输入电池充电功率': 'Please enter battery charging power',
    '请输入备电保持SOC': 'Please enter standby power reserve SOC',
    '添加方案': 'Add programme',
    '修改方案': 'Modify programme',
  },
  // oss
  oss: {
    '文件名称': 'File Name',
    '文件类型': 'File Type',
    '文件大小': 'File Size',
    '版本名称': 'Version Name',
    '文件路径': 'File Path',
    '文件版本': 'File Version',
    '下载': 'Download',
    '选择文件': 'Select File',
    '上传文件': 'Upload File',
    '请输入文件名称': 'Please enter the file name',
    '请选择文件版本类型': 'Please select the file version type',
    '请先选择文件版本类型': 'Please select the file version type first',
    '请选择文件': 'Please select file',
    '上传失败': 'Upload failed',
    '上传成功': 'Upload Successful',
    '下载文件成功': 'Download File Successfully',
    '请上传 大小不超过': 'Please upload files up to',
    '的文件': 'in size',
    '文件格式不正确, 请上传': 'File format is not correct, please upload',
    '格式文件': 'format files',
    '上传文件大小不能超过': 'The size of the uploaded file cannot exceed ',
    '正在上传文件，请稍候': 'Uploading files, please wait',
    '上传文件数量不能超过': 'The number of uploaded files cannot exceed',
    '上传文件失败，请重试': 'Failed to upload the file. Please try again',
    '正在下载文件，请稍候': 'Please wait while downloading the file',
    'HMI升级文件': 'HMI Upgrade File',
    'MAC升级文件': 'MAC Upgrade File',
    'MDC升级文件': 'MDC Upgrade File',
    'STS升级文件': 'STS Upgrade File',
    '上传地区': 'Upload Region',
    '访问域名': 'Access Domain Name',
    '升级对象': 'Upgrade object',
  },
  price: {
    '电价': 'Electricity Price',
    '时段': 'Work Shift',
    '请选择使能': 'Please select enabled',
    '请选择开始时间': 'Please select start time',
    '请选择结束时间': 'Please select end time',
    '请输入电价': 'Please enter electricity price',
    '请添加时段': 'Please add time period',
    '最多只能添加12条哦': 'You can only add a maximum of 12 items',
    '至少要有一条哦': 'At least one',
    add: 'Add',
    '功率': 'Power',
    '请输入功率': 'Please enter power',
    '买卖电是否同价': 'Whether the trading electricity is the same price',
    '买电': 'Buy Electricity',
    '卖电': 'Sell Electricity',
    '请选择买卖电是否同价': 'Please select whether the price is the same',
    '元': 'CNY'
  },
  log: {
    '请输入下发参数': 'Please enter delivery parameters',
    '指令类型': 'Instruction Type',
    '下发参数': 'Sending Parameters',
    '下发参数值': 'Dispatch Parameter Values',
    '执行结果': 'Result of Execution',
    '执行成功': 'Executed Successfully',
    '执行失败': 'Execution Failure',
    '已下发，等待执行': 'Wait',
    '下发时间': 'Issue Time',
    '完成时间': 'Completion Time',
    '系统设置参数': 'System Setup Parameters',
    '策略类参数设置': 'Strategy Parameter Setting',
    'MAC参数': 'MAC Parameters',
    'MDC参数': 'MDC Parameters',
    '电池参数': 'Battery Parameters',
    '设备升级': 'Device Upgrade',
    '系统开关机': 'System Switch',
    '收益统计报表': '	revenue statistics report',
    '导出成功': 'Export Successfully',
    '导出失败': 'Export Failure',
    '导出中': 'Wait',
    '电量统计报表': 'electricity statistics report',
    '操作人员': 'Operating Personnel'
  },
  param: {
    '下发状态': 'Issue Status',
    '未下发': 'Not Issued',
    '下发成功': 'Issued Successfully',
    '下发中': 'Is Being Issued',
    '下发失败': 'Issue Failed',
    '该类参数从未下发': 'This type of parameters has never been issued',
    '参数已成功下发至设备，执行未知，请等待': 'Parameters have been successfully delivered to the device. The execution is unknown, please wait.',
    '参数已成功下发至设备并已执行成功': 'The parameters have been successfully delivered to the device and executed successfully.',
    '参数已成功下发至设备，设备并未执行成功': 'The parameters have been successfully delivered to the device, but the device has not executed successfully.',
    '运行模式': 'Operating Mode',
    '削峰填谷': 'Peak Shaving And Valley Filling',
    '手动模式': 'Manual Mode',
    '后备模式': 'Back-Up Mode',
    '防逆流使能': 'Reverse Power Protection Enable',
    '开关机': 'Power On & Off',
    '开机': 'Power On',
    '关机': 'Power Off',
    '下发': 'Send',
    '参数已下发至设备': 'Parameters have been delivered to the device',
    '查看执行结果': 'Viewing the execution result',
    '星期一': 'Monday',
    '星期二': 'Tuesday',
    '星期三': 'Wednesday',
    '星期四': 'Thursday',
    '星期五': 'Friday',
    '星期六': 'Saturday',
    '星期天': 'Sunday',
    '保存成功': 'Save Successfully',
    '正在下发中，请稍后再下发': 'In the process of delivery, please wait for a moment before posting.',
    '正在下发中': 'In the process of delivery',
    '备电方案': 'Backup Power Plan',
    '必选': 'Required',
    '分时电价': 'Time-Of-Use Price',
    '查看更多方案': 'View More Programmes',
    '参数设置': 'Parameter Setting',
    'MAC参数设置': 'MAC Parameter Setting',
    '电池参数设置': 'Battery Parameter Setting',
    'MDC参数设置': 'MDC Parameter Setting',
    '在线升级': 'Online Upgrade',
    '直流源电压设置': 'Dc Source Voltage Setting',
    '电池恒流设置': 'Battery Constant Current Setting',
    '电池恒功率设置': 'Battery Constant Power Setting',
    '光伏限功率设置': 'PV Power Limit Setting',
    '有功功率设置': 'Active Power Setting',
    '无功功率设置': 'Reactive Power Setting',
    '功率因数设置': 'Power Factor Setting',
    '并离网设置': 'On And Off Grid Setting',
    'SOC上限设置': 'SOC Upper Limit Setting',
    'SOC下限设置': 'SOC Lower Limit Setting',
    '充电限流值': 'Charging Current Limit',
    '放电限流值': 'Discharge Current Limit',
    '欠压保护': 'Undervoltage Protection',
    '欠压恢复': 'Undervoltage Recovery',
    '过压保护': 'Overvoltage Protection',
    '过压恢复': 'Overvoltage Recovery',
    '版本文件': 'Version File',
    '请选择版本文件': 'Please select the version file',
    '确定要恢复出厂设置吗？': 'Sure about factory reset?',
    '恢复出厂设置成功，需要等 2 分钟后再查看': 'Factory reset successful, please wait 2 minutes and check again.',
    '已取消该操作': 'The operation has been canceled',
    '恢复出厂设置': 'Factory Reset',
  },
  '新加坡': 'Singapore',
  '深圳': 'Shenzhen',
  '暂无设备': 'No Device',
  '请选择设备': 'Please select device',
  '请选择参数': 'Please select parameters',
  '最多只能对比30条数据哦': 'Only 30 pieces of data can be compared at most.',
  '请输入纬度或点击地图自动生成': 'Please enter the latitude or click on the map to generate automatically',
  '请输入经度或点击地图自动生成': 'Please enter the longitude or click on the map to generate automatically',
  '请输入地址或点击地图自动生成': 'Please enter the address or click on the map to generate automatically',
  '请输入正确的经度坐标(数字)': 'Please enter the correct longitude coordinates (numbers)',
  '请输入正确的纬度坐标(数字)': 'Please enter the correct latitude coordinates (numbers)',
  '请输入经纬度坐标或点击地图自动生成': 'Please enter the latitude and longitude coordinates or click on the map automatically generated',
  '欢迎': 'Welcome',
  '创建人员': 'Creator',
  '请先选择要上传的升级对象': 'Select the upgrade object to be uploaded',
  '文件名称错误，请严格按照格式上传，提示：请带含MAC、DSP的文件名称': 'File name error, please strictly follow the format upload, prompt: Please include MAC, DSP file name',
  '文件名称错误，请严格按照格式上传，提示：请带含MAC、ARM的文件名称': 'File name error, please strictly follow the format upload, prompt: Please include MAC, ARM file name',
  '文件名称错误，请严格按照格式上传，提示：请带含MDC、DSP的文件名称': 'File name error, please strictly follow the format upload, prompt: Please include MDC, DSP file name',
  '文件名称错误，请严格按照格式上传，提示：请带含MDC、ARM的文件名称': 'File name error, please strictly follow the format upload, prompt: Please include MDC, ARM file name',
  '文件名称错误，请严格按照格式上传，提示：请带含STS、DSP的文件名称': 'File name error, please strictly follow the format upload, prompt: Please include STS, DSP file name',
  '文件名称错误，请严格按照格式上传': 'The file name is incorrect. Please upload the file according to the format',
  '格式为': 'format',
  '文件名称格式': 'File Name Format',
  '操作手册': 'Operation Manual',
  '升级进度': 'Upgrade Progress',
  '查看详情': 'View Details',
  '升级记录': 'Upgrade Record',
  '升级文件': 'Upgrade File',
  '升级版本': 'upgrade Version',
  '当前升级个数': 'Number Of Current Updates',
  '总升级个数': 'Total Upgrades',
  '升级结果': 'Upgrade Result',
  '升级描述': 'Upgrade Description',
  '升级时间(UTC+00:00)': 'Upgrade Time (UTC+00:00)',
  '成功': 'Succeed',
  '失败': 'Failed',
  '等待': 'Wait',
  '模块': 'Module',
  '升级成功': 'Update Successfully',
  '升级中': 'Upgrading',
  '升级失败': 'Upgrade Failure',
  '正在下载远程升级包': 'Downloading the remote upgrade package',
  '远程升级包下载成功': 'The remote upgrade package is successfully downloaded',
  '远程升级包下载失败': 'The remote upgrade package fails to be downloaded',
  '测点别名': 'Measurement Point Alias',
  '测点值': 'Measurement Point Value',
  '测点值别名': 'Measurement Point Value Alias',
  '测点编号': 'Measurement Point Number',
  '测点值编号': 'Measurement Point Value Number',
  '测点类型': 'Measurement Point Type',
  '外设': 'Peripheral',
  '电表': 'Electric Meter',
  '添加别名': 'Add Alias',
  '测点编号/测点值编号': 'Measurement Point Number / Measurement Point Value Number',
  '请选择测点类型': 'Please select measurement point type',
  '修改别名': 'Modify Alias',
  '电表在线状态': 'Electric Meter Online Status',
  'A相电压': 'A Phase Voltage',
  'B相电压': 'B Phase Voltage',
  'C相电压': 'C Phase Voltage',
  '正向有功电度': 'Forward Active Energy',
  '反向有功电度': 'Reverse Active Energy',
  '正向无功电度': 'Forward Reactive Energy',
  '反向无功电度': 'Reverse Reactive Energy',
  '尖正向有功电度': 'Sharp Positive Active Power Energy',
  '尖反向有功电度': 'Sharp Reverse Active Power Energy',
  '尖正向无功电度': 'Sharp Forward Reactive Energy',
  '尖反向无功电度': 'Sharp Reverse Reactive Energy',
  '峰正向有功电度': 'Peak Positive Active Power Energy',
  '峰反向有功电度': 'Peak Reverse Active Power Energy',
  '峰正向无功电度': 'Peak Forward Reactive Energy',
  '峰反向无功电度': 'Peak Reverse Reactive Energy',
  '平正向有功电度': 'Flat Positive Active Power Energy',
  '平反向有功电度': 'Flat Reverse Active Power Energy',
  '平正向无功电度': 'Flat Forward Reactive Energy',
  '平反向无功电度': 'Flat Reverse Reactive Energy',
  '谷正向有功电度': 'Valley Positive Active Energy',
  '谷反向有功电度': 'Valley Reverse Active Energy',
  '谷正向无功电度': 'Valley Forward Reactive Energy',
  '谷反向无功电度': 'Valley Reverse Reactive Energy',
  '负荷正向电量': 'Load Forward Active Energy',
  '负荷反向电量': 'Load Reverse Active Energy',
  'PCC电表': 'PCC Meter',
  '储能电表': 'Energy Storage Meter',
  '光伏电表': 'PV Meter',
  '计量点电表': 'Meter At Metering Point',
  '辅助用电电表': 'Auxiliary Power Meter',
  '直流电表': 'DC Meter',
  '未知别名': 'Unknown Alias',
  '合闸': 'Switch On',
  '分闸': 'Switch Off',
  '开关': 'Switch',
  '测点绑定': 'Measurement Point Binding',
  '是否生效': 'Effective or not？',
  '新建设备绑定测点': 'Bind New Device to Measurement Point',
  '设备修改绑定测点': 'Slave communication failure',
  '生效': 'Effective',
  '失效': 'Invalid',
  '负为放电': 'Negative discharge',
  '功率：分正负，正为放电，负为充电': 'Power: divided into positive and negative, positive for discharge, negative for charging',
  '注：': 'comment:',
  '召测': 'Call For Test',
  '已召测': 'Test has been called',
  '召测失败': 'Test Failure',
  '正在召测中，请稍后': 'Testing is under way, please wait',
  '国家': 'Country',
  '用于电网计算收益。': 'Used for calculating power grid revenue.',
  '按照设置时间段给系统充放电。': 'Charge and discharge the system according to the set time period.',
  '防止系统放电馈入电网。': 'Prevent system discharge from feeding into the grid.',
  '控制系统按其工作模式启停。': 'The control system starts and stops according to its operating mode.',
  '在手动模式下，控制系统输入出有功功率，正为放电，负为充电。': 'In manual mode, the control system inputs active power, positive for discharge and negative for charging.',
  '控制系统输出无功功率正为容性，负为感性。': 'The reactive power output of the control system is capacitive when positive and inductive when negative.',
  '调节输出的有功功率和无功功率的比值。': 'Adjust the ratio of output active power to reactive power.',
  '在手动控制下，设置其系统是否并入电网。': 'Under manual control, set whether the system is connected to the grid.',
  '设置电池停止充电时SOC。': 'Set the SOC when the battery stops charging.',
  '设置电池停止放电时SOC。': 'Set the SOC when the battery stops discharging.',
  '设置电池充电时的电流最大值。': 'Set the maximum current when charging the battery.',
  '设置电池放电时的电流最大值。': 'Set the maximum current when discharging the battery.',
  '电池放电保护电压。': 'Battery discharge protection voltage.',
  '电池可放电恢复电压。': 'The battery can be discharged to restore the voltage.',
  '电池充电电保护电压。': 'Battery charging protection voltage.',
  '电池可充电恢复电压。': 'The battery can be recharged to restore voltage.',
  '设置DC模块恒压模式下输出电压。': 'Set the output voltage of the DC module in constant voltage mode.',
  '设置恒流模式下的输出电流。': 'Sets the output current in constant current mode.',
  '设置恒功率模式下的输出功率。': 'Set the output power in constant power mode.',
  '设置光伏功率最大值。': 'Set the maximum PV power.',
  '负载电表': 'Load Meter',
  '正向总电量': 'Total Forward Power',
  '反向总电量': 'Total Reverse Power',
  '发电机功率': 'Generator Power',
  '今天': 'Today',
  '昨天': 'Yesterday',
  '一周前': 'A Week Ago',
  '项目': 'Project',
  '储能收益': 'Energy Storage Benefits',
  '上网电量': 'Internet Power',
  '电网卖电收益': 'Revenue from electricity sales to the power grid',
  '系统收益': 'System Benefits',
  '统计日期': 'Statistical Date',
  '电网侧总充电量': 'Total charging capacity on the grid side',
  '电网侧总放电量': 'Total discharge on the grid side',
  '请选择有效的设备': 'Please select a valid device',
  '忘记密码？': 'Forget Password?',
  '找回账号或者密码': 'Retrieve Account Or Password',
  '请输入您账号绑定的邮箱地址': 'Please enter the email address associated with your account',
  '返回登录': 'Back to Login',
  '发送验证码': 'Obtain',
  '验证身份': 'Verify Identidy',
  '请输入验证码': 'please enter verification code',
  '返回上一步': 'Previous',
  '下一步': 'Next',
  '重置密码': 'Reset Password',
  '成功提示': 'Success Tips',
  '密码已重置成功啦': 'Password Reset Successfully',
  '去登录': 'Go to login',
  '特别说明': 'Special Note',
  '找回步骤：输入您想找回密码或者账号的邮箱地址 - 输入4位验证码，完成身份验证 - 设置新密码。': 'Retrieval steps: Enter the email address of the password or account you want to retrieve - Enter the 4-digit verification code to complete identity verification - Set a new password.',
  '如果您忘记了邮箱地址或者没有邮箱，那么您应该联系管理员帮您重置您的密码，客服电话：': 'If you have forgotten your email address or do not have an email address, you should contact the administrator to help you reset your password. Customer service phone number: ',
  '。': '.',
  '进行身份验证时，验证码有效时长为5分钟，如您在身份验证页面停留过久（5分钟），那么您将身份验证失败。': 'When performing identity authentication, the verification code is valid for 5 minutes. If you stay on the identity authentication page for too long (5 minutes), your identity authentication will fail.',
  '正在发送验证码，请稍后': 'Verification code is being sent, please wait',
  '正在验证身份，请稍后': 'Verifying identity, please wait',
  '验证成功': 'Verification Success',
  '正在重置密码，请稍后': 'Resetting password, please wait',
  '请输入密码': 'please enter password',
  '货币': 'Currency',
  '货币单位': 'Currency Unit',
  '费率': 'Rates',
  '费率一': 'Rate 1',
  '费率二': 'Rate 2',
  '费率三': 'Rate 3',
  '费率四': 'Rate 4',
  '费率五': 'Rate 5',
  'EN国家': 'EN Country',
  'EN货币单位': 'EN Currency Unit',
  '添加货币': 'Adding Currency',
  '修改货币': 'Modify currency',
  '该方案的货币已修改，请重新添加一个方案': 'The currency of this plan has been modified. Please add a new plan.',
  '电表正向总电量': 'Total forward power of the meter',
  '电表反向总电量': 'Total reverse power of the meter',
  '正向总电量': 'Total forward power',
  '反向总电量': 'Total reverse power',
  '电池告警': 'Battery Alarm',
  '电池故障': 'Battery Fault',
  '下载APP': 'Download APP',
  '扫码下载APP': 'Scan code to download APP',
  '电价：电价货币为项目绑定的货币为准，如项目绑定货币为美元，那么电价为0.2美元': 'Electricity price: The electricity price currency is based on the currency bound to the project. If the project is bound to the US dollar, the electricity price is 0.2 US dollars.',
  '电价信息': 'Electricity Price Information',
  '电量类型': 'Power Type',
  '储能电量': 'Energy storage capacity',
  '光伏电量': 'PV Power',
  '电网电量': 'Grid power',
  '1、如若不想导出某一类型的电量，请不要勾选该类型电量': '1. If you do not want to export a certain type of electricity, please do not check that type of electricity;',
  '2、是否计算差值，为某一时间段的汇总值。': '2. whether to calculate the difference, is the summary value of a certain period of time.',
  '导出的数据为你自己选择的数据': 'The exported data is the data you selected',
  '数据分析报表': 'Data Analysis Report',
  '不根据当地时间统计，根据创建项目所选时区统计': 'Statistics are not based on local time, but based on the time zone selected when the project was created.',
  'AB线电压': 'AB Line Voltage',
  'BC线电压': 'BC Line Voltage',
  'CA线电压': 'CA Line Voltage',
  '不记得原来的密码？': "Don't remember your original password?",
  '正在加载页面，请稍候！': 'The page is loading, please wait!',
  '帮助中心': 'Help Center',
  '常见问题': 'FAQ',
  '是否多屏': 'Whether Multiple Screens',
  '是': 'YES',
  '否': 'NO',
  '云平台PC端操作手册': 'Cloud Platform PC Operation Manual',
  '云平台PC端英文版操作手册': 'Cloud Platform PC English Version Operation Manual',
  '云平台常见问题': 'Cloud Platform FAQ',
  '云平台英文版常见问题': 'Cloud Platform English Version FAQ',
  '云平台APP操作手册': 'Cloud Platform APP Operation Manual',
  '云平台APP英文版操作手册': 'Cloud Platform APP English Version Operation Manual',
  '所属国家': 'Affiliated Country',
  '台': '',
  '历史数据': 'Historical Data',
  '获取数据': 'Getting Data',
  '设备离线，不可获取': 'The device is offline and cannot be retrieved.',
  '上传中': 'Uploading',
  '文件上传失败，不可下载或者删除': 'File upload failed, cannot be downloaded or deleted.',
  '文件上传中，不可下载或者删除': 'The file is being uploaded and cannot be downloaded or deleted.',
  '获取文件': 'Get File',
  '拉取oss服务器上的所有升级文件': 'Pull all upgrade files on the OSS server',
  '获取失败': 'Failed to obtain',
  '文件地区': 'File Region',
  '请输入ac': 'Please enter ac',
  '光伏消纳': 'PV Consumption',
  '动态扩容': 'Dynamic Expansion',
  '变压器容量': 'Transformer Capacity',
  '全选': 'ALL',
  '请稍等,数据还在请求中~': 'Please wait, the data is still being requested~',
  '复制成功': 'Successful Replication',
  '光储充系统(纯并网)': 'PV+storage+charging system (on gird)',
  '光储充系统(并离网)': 'PV+storage+charging system (on&off grid)',
  '分配项目': 'Allocate Project',
  '所要分配的用户': 'User to be assigned',
  '注：分配项目，会把项目所绑定的设备也一起被分配给用户。': 'Note: When you assign a project, the devices bound to the project will also be assigned to the user.',
  '分配失败': 'Allocation Failure',
  '分配成功': 'Allocation Successful',
  '整机状态': 'Machine Status',
  '内风机状态': 'Internal Fan Status',
  '外风机状态': 'External Fan Status',
  '压缩机状态': 'Compressor Status',
  '电加热状态': 'Electric Heating Status',
  '应急风机状态': 'Emergency Fan Status',
  '空调开关机状态': 'Air Conditioner On/Off Status',
  '出水温度': 'Outlet Water Temperature',
  '出水压力': 'Outlet Water Pressure',
  '进水温度': 'Inlet Water Temperature',
  '进水压力': 'Inlet Water Pressure',
  '空调状态': 'Air Conditioning Status',
  '空调模式': 'Air Conditioning Mode',
  '内循环': 'Inner Loop',
  '制热': 'Heating',
  '充电桩通信状态': 'Charging Pile Communication Status',
  '充电枪个数': 'Number Of Charging Guns',
  '充电桩状态': 'Charging Pile Status',
  '1#枪输出电流': '1# Gun Output Current',
  '1#枪输出电压': '1# Gun Output Voltage',
  '1#枪输出功率': '1# Gun Output Power',
  '1#枪当前SOC': '1# Gun Current SOC',
  '1#枪充电电量': '1# Gun Charging Capacity',
  '1#枪充电金额': '1# Gun Charge Amount',
  '1#枪电池最高温度': '1# Gun Battery Maximum Temperature',
  '1#枪充电时长': '1# Gun Charging Time',
  '1#枪状态': '1# Gun Status',
  '2#枪输出电流': '2# Gun Output Current',
  '2#枪输出电压': '2# Gun Output Voltage',
  '2#枪输出功率': '2# Gun Output Power',
  '2#枪当前SOC': '2# Gun Current SOC',
  '2#枪充电电量': '2# Gun Charging Capacity',
  '2#枪充电金额': '2# Gun Charge Amount',
  '2#枪电池最高温度': '2# Gun Battery Maximum Temperature',
  '2#枪充电时长': '2# Gun Charging Time',
  '2#枪状态': '2# Gun Status',
  '总输出功率': 'Total Output Power',
  '充电桩': 'Charging Pile',
  '空闲': 'Leisure',
  '插枪': 'Insert The Gun',
  '充电等待': 'Charging Waiting',
  '启动中': 'Starting',
  '充电中': 'Charging',
  '重连': 'Reconnect',
  '结算状态': 'Settlement Status',
  '故障状态': 'Fault Status',
  '放电中': 'Discharging',
  '预约状态': 'Appointment Status',
  '后台预约状态': 'Backstage Appointment Status',
  '充电完成状态': 'Charging Completion Status',
  'APP，预约状态': 'APP Appointment Status',
  '试用期到，停止服务状态': 'The trial period has expired and the service is stopped',
  '枪功率': 'Gun Power',
  '枪': 'Gun',
  '请输入ICCID': 'Please enter ICCID',
  '长度为20个字符': 'The value contains 20 characters',
  '分配SIM卡': 'Allocate SIM cards',
  '未知': 'Unknown',
  '注：ICCID在卡的后面，有20位字符。': 'Note: The ICCID is at the back of the card and has 20 characters.',
  '提示：手动选择或者输入搜索选择': 'Tip: Select manually or enter search selection.',
  '请选择要分配的用户': 'Select the user to assign',
  '添加要绑定项目的设备': 'Add the device to bind to the project',
  '提示：点击上方地图自动生成地址或者手动输入，格式为：经度：113.94876， 纬度：22.636858': 'Tips: Click on the map above to automatically generate the address or enter it manually. The format is: Longitude: 113.94876, Latitude: 22.636858',
  '提示：手动输入或者点击上方地图自动生成地址': 'Tip: Enter manually or click on the map above to automatically generate the address',
  '添加方案': 'Add plan',
  '获取本地设备削峰填谷方案': 'Obtain the peak load shaving and valley filling solution for local devices.',
  '远程控制': 'Remote Control',
  '关闭远程控制': 'Turn off remote control',
  '开启远程控制': 'Enable remote control',
  '设置开启密码': 'Set an open password',
  'VNC模式': 'VNC Mode',
  'SSH端口': 'SSH Port',
  'VNC端口': 'VNC Port',
  'VNC和SSH的端口不能重复': 'The VNC and SSH ports must be unique',
  '开启SSH': 'Enable SSH',
  '开启SSH+VNC': 'Enable SSH+VNC',
  '开启VNC': 'Enable VNC',
  '该设备已开启远程控制，是否需要关闭远程控制': 'Remote control is enabled on the device. Determine whether to disable remote control.',
  '正在关闭中': 'Under closure',
  '关闭远程成功': 'Close remote successfully',
  '确认关闭': 'Confirm Close',
  '查看端口': 'View Port',
  '设备离线，不可操作': 'The device is offline and cannot be operated.',
  '下发远程': 'Remote Delivery',
  '回复类型': 'Reply Type',
  '下发': 'Send',
  '关闭': 'Off',
  '回复结果': 'Reply Result',
  '回复时间': 'Reply Time',
  '目录创建失败': 'Directory creation failure',
  '文件下载失败': 'File download failure',
  '库文件缺失': 'Library file missing',
  '远程控制记录': 'Remote Control Record',
  '选择设备': 'Select Device',
  '查看远程控制信息': 'View remote control information',
  '设备序列号': 'SN',
  '开启内网VNC': 'Enabling Intranet VNC',
  '注：开启内网VNC的端口为5900。': 'Note: The port for enabling Intranet VNC is 5900.',
  '2FA验证': '2FA Verification',
  '变压器温度': 'Transformer Temperature',
  '簇': 'Cluster',
  '簇级传感器1': 'Cluster Sensor 1',
  '簇级传感器2': 'Cluster Sensor 1',
  '最高单体电压': 'Maximum Cell Voltage',
  '最低单体电压': 'Minimum Cell Voltage',
  '最高单体温度': 'Maximum Cell Temperature',
  '最低单体温度': 'Minimum Cell Temperature',
  '温升': 'TR',
  '详情': 'Details',
  '温度': 'TEMP',
  '电压': 'U',
  '未知电表': 'Unknown Meter',
  '暂未配置电池电芯规格信息，请联系管理员或售后人员。': 'No battery cell specifications have been configured. Contact the administrator or after-sales personnel.',
  '日期类型': 'Date Type',
  '选择日期': 'Select Date',
  '开始时间': 'Start Time',
  '结束时间': 'End Time',
  '时间段': 'Time Quantum',
  '是否计算差值': 'Whether to calculate the difference',
  '在线': 'Online',
  '离线': 'Offline',
  '消防': 'Firefighting',
  '默认': 'Default',
  '外设类型': 'Peripheral type',
  '请选择外设类型': 'Please select peripheral type',
  '电流': 'I',
  '阻抗': 'Z',
  '电芯数量': 'Cell Number',
  '电压数量': 'Voltage Number',
  '温度数量': 'Temperature Number',
  '温升数量': 'Temperature Rise Number',
  '电流数量': 'Current Number',
  '阻抗数量': 'Impedance Number',
  '不能高于电芯数量': 'Cannot be higher than the number of cells',
  '添加配置': 'Add configuration',
  '修改配置': 'Modify configuration',
  '是否同步下发': 'Simultaneous Distribution',
  '是：主从机同步下发；否：下发至主机。': 'Yes: Synchronous download from master to slave; No: Download to the master.',
  '告警代码': 'Alarm Point',
  '一月': 'January',
  '二月': 'February',
  '三月': 'March',
  '四月': 'April',
  '五月': 'May',
  '六月': 'June',
  '七月': 'July',
  '八月': 'August',
  '九月': 'September',
  '十月': 'October',
  '十一月': 'November',
  '十二月': 'December',
  '执行周期': 'Execution Cycle',
  '每天；': 'Everyday,',
  '每周（1~7天）；': 'Every week (1 to 7 days),',
  '1~12个月。': '1 to 12 months.',
  '注：该执行周期只适用于削峰填谷。': 'Note: This execution cycle is only applicable for peak shaving and valley filling.',
  '市电电表': 'AC Meter',
  '是否显示柴油机': 'Whether to display diesel engine',
  '升级类型': 'Upgrade Type',
  '主屏': 'Home Screen',
  '副屏': 'Secondary Screen',
  '正母线电压': 'Positive Bus Voltage',
  '负母线电压': 'Negative Bus Voltage',
  '充电桩功率': 'Charging pile power',
  '包': 'Pack',
  '清空': 'Clear',
  '清空成功': 'Cleared Successfully',
  '404错误!': '404 ERROR!',
  '找不到网页！': 'PAGE NOT FOUND!',
  '对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。': 'Sorry, the page you are looking for does not exist. Try checking the URL for errors and pressing the refresh button on your browser or try to find something else in our app.',
  '返回首页': 'Back to Home',
  '401错误!': '401 ERROR!',
  '您没有访问权限！': 'You do not have access permissions!',
  '对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面。': 'Sorry, you do not have access rights, please do not perform illegal operations! You can return to the main page.',
  '需要升级HMI跟模块': 'Need to upgrade HMI and modules',
  '只需要升级HMI': 'Only need to upgrade HMI',
  '采集屏跟云平台通讯': 'The collection screen communicates with the cloud platform',
  '主屏版本': 'Main Screen Version',
  '副屏版本': 'Secondary Screen Version',
  '前往新平台': 'Go to new platform',
  '前往旧平台': 'Go to old platform',
  '采集屏版本': 'Capture Screen Version',
  '采集屏': 'Collection Screen',
  'BMS类型': 'BMS Type',
  '宁德': 'CATE',
  '协能': '协能',
  '高泰昊能': 'Qualtech',
  '华塑': 'Hwasu',
  '高特': 'GOLD ELECTRONIC',
  '华思': 'HUASI',
  '小鸟': 'Bird',
  '山东威马': '山东威马',
  '亿纬锂电': 'EVE Energy',
  '力神': ' Lishen',
  '帷幕-BCU': '帷幕-BCU',
  '宁德液冷': '宁德液冷',
  '三级宁德': '三级宁德',
  '优旦': 'Udan',
  '欣旺达': 'Sunwoda',
  '沛城电子': 'Pace Electronics',
  '帷幕-BCU2': '帷幕-BCU2',
  '群控能源GCE': '群控能源GCE',
  '高特三级bms': '高特三级bms',
  '科工': '科工',
  '描述': 'Description',
  '失败是否重启': 'Restart if failed',
  '该功能只有新版本才支持': 'This feature is only supported in the new version',
  '通用版本': 'Universal version',
  '定制版本': 'Customized version',
  '联系管理员': 'Contact the Administrator',
  '告警详情': 'Alarm Details',
  '告警信息': 'Alarm Information',
  '问题分析': 'Problem Analysis',
  '问题原因': 'Cause',
  '处理办法': 'Solution',
  '涉及部件': 'Parts Involved',
  '暂无详细信息': 'No details availableNo detailed information yet',
  '格式不正确': 'Incorrect format',
  '域名': 'Domain Name',
  '中文网站名称': 'Chinese Website Name',
  '英文网站名称': 'English Website Name',
  '登录logo': 'Login logo',
  '导航栏logo': 'Navigation bar logo',
  '中文大屏标题': 'Chinese large screen title',
  '英文大屏标题': 'English large screen title',
  '宽度': 'Width',
  '高度': 'Height',
  '中文大屏logo': 'Chinese large screen logo',
  '英文大屏logo': 'English large screen logo',
  '添加域名': 'Add domain name',
  '修改域名': 'Modify domain name',
  '请输入域名': 'Please enter the domain name',
  '请输入网站名称': 'Please enter the website name',
  '请输入大屏标题': 'Please enter the large screen title',
  '应用名称': 'Application Name',
  '强制升级': 'Force Upgrade',
  '平台': 'Platform',
  '包大小(bytes)': 'Package Size(bytes)',
  '包地址': 'Package Address',
  '增量升级': 'Incremental Upgrade',
  '增量包地址': 'Incremental Package Address',
  '升级说明': 'Upgrade Description',
  '添加包': 'Add Package',
  '修改包': 'Modify Package',
  '二级角色部门': 'Secondary Role Department',
  '中文logo': 'Chinese logo',
  '英文logo': 'English logo',
  '缩放logo': 'Zoom logo',
  '主题': 'Theme',
  '主题颜色': 'Theme Color',
  '侧边栏活跃背景色': 'Sidebar Active Background Color',
  '侧边栏背景颜色': 'Sidebar Background Color',
  '侧边栏logo背景颜色': 'Sidebar Logo Background Color',
  '侧边栏文字颜色': 'Sidebar Text Color',
  '侧边栏文字活跃颜色': 'Sidebar Active Text Color',
  '页面文字颜色': 'Text Color',
  '认证失败，无法访问系统资源': 'Authentication failed, unable to access system resources.',
  '当前操作没有权限': 'No permission for the current operation.',
  '访问资源不存在': 'Access resource does not exist.',
  '系统未知错误，请反馈给管理员': 'Unknown system error, please report to the administrator.',
  '不允许有中文字符': 'Chinese characters are not allowed',
  'MDC直流源': 'MDC DC Source',
  '正向电量': 'Forward Power',
  '反向电量': 'Reverse Power',
  '直流源功率': 'DC source power',
  '暂无数据': 'No Data.',
  '通知公告': 'Notice Announcement',
  '公告标题': 'Announcement Title',
  '公告类型': 'Announcement Type',
  '内容': 'Content',
  '添加公告': 'Add Announcement',
  '修改公告': 'Modify Announcement',
  '确定全部已读吗？': 'Are you sure you have read all?',
  '确定已读吗？': 'Are you sure you have read it?',
  '已读成功': 'Read successfully',
  '已读': 'Read',
  '全部已读': 'All Read',
  '通知的用户': 'Notified User',
  '没有未读消息': 'No unread messages',
  '邮箱地址不能为空': 'Email address cannot be empty',
  '手机号码不能为空': 'Mobile phone number cannot be empty',
  'BMS升级文件': 'BMS Upgrade File',
  'TAR升级文件': 'TAR Upgrade File',
  '选择TAR文件': 'Select TAR File',
  'TAR版本文件': 'TAR Version File',
  'INI版本文件': 'INI Version File',
  '选择INI文件': 'Select INI File',
  '电表取值类型': 'Meter value type',
  '正向': 'Forward',
  '反向': 'Reverse',
  '请到微信公众号进行充值，微信公众号为：': 'Please recharge via WeChat official account:',
}
