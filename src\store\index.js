/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-02-23 15:20:11
 * @FilePath: \elecloud_platform-main\src\store\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import dict from './modules/dict'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import monitor from './modules/monitor'
import settings from './modules/settings'
import common from './modules/common'
import param from './modules/param'
import getters from './getters'
import notice from './modules/notice'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    dict,
    user,
    tagsView,
    permission,
    settings,
    monitor,
    common,
    param,
    notice
  },
  getters
})

const useStore = () => {
  return store.dispatch('common/getIpInfo')
}

export {
  useStore
}

export default store
