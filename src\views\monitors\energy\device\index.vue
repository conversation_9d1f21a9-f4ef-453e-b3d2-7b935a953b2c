<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:01
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-03-12 15:11:13
 * @FilePath: \elecloud_platform-main\src\views\monitors\energy\device\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="position: relative;height: 100%">
    <el-tabs tab-position="top" class="tab home" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="$t('monitor.deviceInfo')" name="control">
        <controller></controller>
      </el-tab-pane>
      <!-- <el-tab-pane :label="$t('monitor.baseInfo')"><information></information></el-tab-pane> -->
      <el-tab-pane :label="$route.query.type == 4 ? 'PV': 'PCS'" v-if="ac.length || dc.length || sts.length" name="pcs">
        <pcs></pcs>
      </el-tab-pane>
      <el-tab-pane label="BMS" v-if="bms.length" name="bms">
        <bms type="bms"></bms>
      </el-tab-pane>
      <el-tab-pane label="BMS-BAU" v-if="bmsBau.length" name="bmsBau">
        <bms type="bmsBau"></bms>
      </el-tab-pane>
      <el-tab-pane label="CELL" v-if="showCell" name="cell">
        <cell></cell>
      </el-tab-pane>
      <el-tab-pane :label="$t('monitor.amTitle')" v-if="ele.length" name="ele">
        <ammeter></ammeter>
      </el-tab-pane>
      <el-tab-pane :label="$t('外设')" v-if="io.length" name="io">
        <peripherals></peripherals>
      </el-tab-pane>
      <el-tab-pane :label="$t('充电桩')" v-if="cp.length" name="cp">
        <chargingPile></chargingPile>
      </el-tab-pane>
      <el-tab-pane :label="$t('消防')" v-if="stsIo.length" name="stsIo">
        <firefighting></firefighting>
      </el-tab-pane>
      <!-- <el-tab-pane label="空调"><refrigeration></refrigeration></el-tab-pane> -->
      <!-- <el-tab-pane :label="$t('monitor.cell')"><cell></cell></el-tab-pane> -->
    </el-tabs>
  </div>
</template>
<script>
import ammeter from "./ammeter.vue";
import bms from "./bms.vue";
import cell from "./cell.vue";
import controller from "./controller.vue";
import pcs from "./pcs.vue";
import information from "./information.vue";
import peripherals from './peripherals.vue'
import chargingPile from "./chargingPile.vue";
import firefighting from "./firefighting.vue";
import { isEmpty } from 'lodash'
export default {
  name: "",
  components: {
    ammeter,
    bms,
    cell,
    controller,
    pcs,
    information,
    peripherals,
    chargingPile,
    firefighting
  },
  data() {
    return {
      activeName: 'control'
    }
  },
  computed: {
    bms() {
      let data = this.$store.state.monitor.pcs_bms
      return data
    },
    ac() {
      let data = this.$store.state.monitor.pcs_ac
      data.forEach(item => {
        item.name = `${parseInt(item.dc) - 131000 + 1}#Monet-AC`
      })
      return data
    },
    dc() {
      let data = this.$store.state.monitor.pcs_dc
      data.forEach(item => {
        item.name = `${parseInt(item.dc) - 141000 + 1}#Monet-DC`
      })
      return data
    },
    sts() {
      let data = this.$store.state.monitor.pcs_sts
      data.forEach(item => {
        item.name = `${parseInt(item.dc) - 151000 + 1}#Monet-STS`
      })
      return data
    },
    ele() {
      let data = this.$store.state.monitor.pcs_ele
      return data
    },
    io() {
      let data = this.$store.state.monitor.pcs_io
      return data
    },
    cp() {
      let data = this.$store.state.monitor.pcs_cp
      return data
    },
    showCell() {
      let data = this.$store.state.monitor.pcs_cell
      return data?.findIndex(item => !isEmpty(item.bms_7101_7612) || !isEmpty(item.bms_7613_8124) || !isEmpty(item.bms_8125_8637) || !isEmpty(item.bms_8638_9149) || !isEmpty(item.bms_9150_9661)) != -1
    },
    stsIo() {
      let data = this.$store.state.monitor.pcs_stsIo
      return data
    },
    bmsBau() {
      let data = this.$store.state.monitor.pcs_bmsBau
      return data
    }
  },
  methods: {
    handleClick(tab) {
      if (tab.name == 'control') {
      } else if (tab.name == 'pcs') {
      } else if (tab.name == 'bms') {
      } else if (tab.name == 'ele') {
        this.$store.dispatch('bindAliasQueryFn', {
          ac: this.$route.query.id,
          enable: 1,
          type: 1,
          mode: 0
        })
      } else if (tab.name == 'io') {
        this.$store.dispatch('bindAliasQueryFn', {
          ac: this.$route.query.id,
          enable: 1,
          type: 0,
          mode: 0
        })
      } else if (tab.name == 'stsIo') {
        this.$store.dispatch('bindAliasQueryFn', {
          ac: this.$route.query.id,
          enable: 1,
          type: 0,
          mode: 1
        })
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.tab {
  padding: 10px 20px 0 20px;
  height: 100%;
}

::v-deep .el-tabs__content {
  height: calc(100% - 80px);
  overflow: auto;
  overflow-x: hidden;
}
</style>
