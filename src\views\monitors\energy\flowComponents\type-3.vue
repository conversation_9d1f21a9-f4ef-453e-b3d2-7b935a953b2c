<template>
  <div class="position_box">
    <div class="flow_box">
      <!-- 直流母线 -->
      <div class="flow">
        <div class="dianzhan">
          <svg-icon icon-class="flow_dc" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_dc }" />
          <div class="flow-detail" style="margin-top: 10px;">
            <div>
              {{ $t('monitor.flowItem5') }}：<span>{{ flowData.bus ? flowData.bus : 0 }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <div class="dianchi">
          <svg-icon icon-class="flow_de" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_de }" />
        </div>
        <svg width="300px" height="240px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')" d="m267,125 l-203,0  0,1"
            v-if="flowData.bus > 1" />
          <!-- 反 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')" d="m63,125 l203,1  0,1"
            v-else-if="flowData.bus < -1" />
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')" d="m267,125 l-210,0  0,1 "
            v-else />

          <defs>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stop-color="#436EE3" />
              <stop offset="100%" stop-color="#F1A121" />
            </linearGradient>
          </defs>
          <!-- 空心圆 -->
          <circle r="10" class="circle_1" v-if="showCircle('bus')">
            <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('bus')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
    <div class="flow_box">
      <!-- 电池 -->
      <div class="flow">
        <div class="transformer">
          <Bms :value="soc"></Bms>
          <div class="flow-detail" style="margin-top: 10px;">
            <div>
              {{ $t('monitor.flowItem4') }}：<span>{{ flowData.cell }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="240px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m236,125 l-210,0  0,1"
            v-if="flowData.cell > 1" />
          <!-- 反  -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m25,125 l210,1  0,1"
            v-else-if="flowData.cell < -1" />
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m25,125 l210,1  0,1" v-else />

          <defs>
            <linearGradient id="grad4" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#F76655" />
              <stop offset="100%" stop-color="#F7A11A" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('cell')">
            <animate attributeName="fill" values="#F7A11A;#F76655" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('cell')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import Bms from './bms.vue'

export default {
  components: { Bms },
  computed: {
    flowData() {
      return this.$store.state.monitor.flowData
    },
    // 流动拓补图展示圆
    showCircle() {
      let control = this.$store.state.monitor.control
      let flowData = this.$store.state.monitor.flowData
      return (name) => {
        if (!control) return false
        if (control.onLineState == '离线') return false
        if (-1 <= flowData[name] && flowData[name] <= 1) {
          return false
        } else if (flowData[name] == 0) {
          return false
        } else {
          return true
        }
      }
    },
    soc() {
      let bms = this.$store.state.monitor.pcs_bms
      let bmsBau = this.$store.state.monitor.pcs_bmsBau
      if (bms.length) return (bms.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bms.length).toFixed(1)
      else if (bmsBau.length) return (bmsBau.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bmsBau.length).toFixed(1)
      return 0
    }
  }
}
</script>

<style lang="scss" scoped>
.position_box {
  position: absolute;
  height: 240px;
  width: 600px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;


  .flow_box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .flow {
    flex: 1;
    position: relative;

    .dianzhan {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      left: -58px;
      top: 78px;
      padding: 8px;
      display: flex;
      align-items: center;
      flex-direction: column;
    }

    .flow-detail {
      margin-left: 10px;
      padding: 0 10px;
      border: 1px solid #d6d6d6;
      border-radius: 10px;
      /* height: 35px; */
      display: flex;
      font-size: 14px;
      line-height: 35px;

      span {
        font-weight: 600;
      }
    }

    .dianchi {
      position: absolute;
      right: -38px;
      top: 81px;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      z-index: 1000;
    }

    .transformer {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      right: 0;
      top: 85px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .img {
      width: 70px;
    }
  }
}
.svg-img {
  width: 70px;
  height: 70px;
}
</style>
