/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-02-28 09:40:56
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-02-28 10:26:48
 * @FilePath: \elecloud_platform-main\src\api\system\logo.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 查询域名对应不同logo列表
export function logoList(queryInfo) {
  return request({
    url: '/system/logo/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addLogo(data) {
  return request({
    url: '/system/logo',
    method: 'post',
    data
  })
}

// 修改
export function editLogo(data) {
  return request({
    url: '/system/logo',
    method: 'put',
    data
  })
}

// 删除
export function deleteLogo(queryInfo) {
  return request({
    url: `/system/logo/${queryInfo.ids}`,
    method: 'delete'
  })
}

// 根据域名获取不同logo信息
export function detail(queryInfo) {
  return request({
    url: `/system/logo/getDomainNameByInfo/${queryInfo.domainName}`,
    method: 'get'
  })
}
