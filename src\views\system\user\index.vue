<template>
  <div class="box">
    <!--部门数据-->
    <div class="box-tree elevation-4">
      <div class="head-container">
        <el-input v-model="deptName" :placeholder="$t(`dept['Please enter department name']`)" clearable size="small"
          prefix-icon="el-icon-search" style="margin-bottom: 20px" />
      </div>
      <div class="head-container" style="height: 95%;overflow: auto;">
        <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
          :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
          @node-click="handleNodeClick" />
      </div>
    </div>
    <div class="box-content elevation-4">
      <!--用户数据-->
      <div class="input_box">
        <div class="header-title">{{ $route.meta.title }}</div>
        <div>
          <div class="input_ment">
            <el-input v-model="queryParams.userName" :placeholder="$t(`user['Please enter user name']`)" clearable
              style="width: 240px" @keyup.enter.native="handleQuery" />
          </div>
          <div class="input_ment">
            <el-select v-model="queryParams.status" :placeholder="$t('user.userState')" clearable style="width: 120px">
              <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </div>
          <div class="input_ment">
            <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" :start-placeholder="$t('date.start')"
              :end-placeholder="$t('date.end')"></el-date-picker>
          </div>
          <div class="input_ment">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
          </div>

          <div class="input_ment">
            <el-button type="primary" icon="el-icon-plus" @click="handleAdd" v-hasPermi="['system:user:add']">
              {{ $t('common.add') }}</el-button>
          </div>
          <!-- <div  class="input_ment">
            <el-button
              type="success"
              icon="el-icon-edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:user:edit']"
            >{{ $t('common.edit') }}</el-button>
          </div> -->
          <!-- <div class="input_ment">
            <el-button
              type="danger"
              icon="el-icon-delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:user:remove']"
            >删除</el-button>
          </div> -->
          <!-- <div class="input_ment">
            <el-button
              type="info"
              icon="el-icon-upload2"
              @click="handleImport"
              v-hasPermi="['system:user:import']"
            >导入</el-button>
          </div> -->
          <!-- <div class="input_ment">
            <el-button
              type="warning"
              icon="el-icon-download"
              @click="handleExport"
              v-hasPermi="['system:user:export']"
            >导出</el-button>
          </div> -->
        </div>
      </div>

      <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column :label="$t('user.id')" align="center" key="userId" prop="userId" v-if="columns[0].visible"
          width="120" />
        <el-table-column :label="$t('user.name')" align="center" key="userName" prop="userName"
          v-if="columns[1].visible" show-overflow-tooltip width="180">
          <template slot-scope="scope">
            {{ scope.row.userName }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.userName"
              v-clipboard:success="copySuccess"></i>
          </template>
        </el-table-column>
        <el-table-column :label="$t('user.email')" align="center" key="email" prop="email" v-if="columns[1].visible"
          show-overflow-tooltip width="120" />
        <!-- <el-table-column :label="$t('user.nickname')" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" show-overflow-tooltip width="140" /> -->
        <el-table-column :label="$t('user.dept')" align="center" key="deptName" prop="dept.deptName"
          v-if="columns[3].visible" show-overflow-tooltip width="130" />
        <el-table-column :label="$t('user.phone')" align="center" key="phonenumber" prop="phonenumber"
          v-if="columns[4].visible" width="120" />
        <el-table-column :label="$t('common.status')" align="center" key="status" v-if="columns[5].visible" width="120">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
              @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" v-if="columns[6].visible"
          show-overflow-tooltip width="200">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.handle')" align="center" class-name="small-padding fixed-width"
          fixed="right" width="400px">
          <template slot-scope="scope" v-if="scope.row.userId !== 1">
            <el-button type="text" @click="handleUpdate(scope.row)" v-hasPermi="['system:user:edit']"
              style="padding: 0;">{{
                $t('common.edit') }}</el-button>
            <el-button type="text" @click="handleDelete(scope.row)" v-hasPermi="['system:user:remove']"
              style="padding: 0;">{{
                $t('common.delete') }}</el-button>
            <el-button type="text" @click="handleResetPwd(scope.row)" v-hasPermi="['system:user:resetPwd']"
              style="padding: 0;">{{
                $t('user.handlePwd') }}</el-button>
            <el-button type="text" @click="handleAuthRole(scope.row)" v-if="$auth.hasRole('admin')"
              style="padding: 0;">{{
                $t('user.handleRole') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" center append-to-body :modal-append-to-body="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('user.nickname')" prop="nickName" v-if="$auth.hasRole('admin')">
              <el-input v-model="form.nickName" :placeholder="$t(`user['Please enter user nickname']`)"
                maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="$auth.hasRole('admin')">
            <el-form-item :label="$t('user.belongDept')" prop="deptId">
              <treeselect v-model="form.deptId" :options="deptOptions" :show-count="true"
                :placeholder="$t(`user['Please select belonging department']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('user.phone')" prop="phonenumber">
              <el-input v-model="form.phonenumber" :placeholder="$t(`user['Please enter phone number']`)"
                maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('user.email')" prop="email" :rules="form.type == 1 ? [
              { required: true, message: $t(`common['Please enter']`), trigger: 'blur' }, {
                type: 'email', message:
                  $t(`user['Please enter a valid email address']`), trigger: ['blur', 'change']
              }] : [{
                type: 'email',
                message: $t(`user['Please enter a valid email address']`), trigger: ['blur', 'change']
              }]">
              <el-input v-model="form.email" :placeholder="$t(`user['Please enter email']`)" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('user.name')" prop="userName">
              <el-input v-model="form.userName" :placeholder="$t(`user['Please enter user name']`)" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="$auth.hasRole('admin')" :label="$t('user.pwd')" prop="password">
              <el-input v-model="form.password" :placeholder="$t(`user['Please enter user password']`)" type="password"
                maxlength="20" show-password />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('common.status')">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                  dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="$auth.hasRole('admin')">
            <el-form-item :label="$t('user.post')">
              <el-select v-model="form.postIds" multiple :placeholder="$t(`user['Please select post']`)">
                <el-option v-for="item in postOptions" :key="item.postId" :label="item.postName" :value="item.postId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="$auth.hasRole('admin')">
            <el-form-item :label="$t('user.role')">
              <el-select v-model="form.roleIds" :placeholder="$t(`user['Please select role']`)">
                <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="$auth.hasRole('admin')">
            <el-form-item :label="$t('2FA验证')">
              <el-radio-group v-model="form.type">
                <el-radio :label="1">{{ $t('是') }}</el-radio>
                <el-radio :label="0">{{ $t('否') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('user.remark')">
              <el-input v-model="form.remark" type="textarea"
                :placeholder="$t(`user['Please enter content']`)"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body
      :modal-append-to-body="false">
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">{{ $t('user.drag') }}{{ $t('user.or') }}<em>{{ $t('user.click') }}</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> {{ $t('user.update') }}
          </div>
          <span>{{ $t('user.limit') }}</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">{{ $t('user.template') }}</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">{{ $t('common.confirm') }}</el-button>
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from "@/api/system/user";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        userName: [
          { required: true, message: this.$t(`user['Username cannot be empty']`), trigger: "blur" },
          { min: 2, max: 20, message: this.$t(`user['Username length must be between 2 and 20']`), trigger: 'blur' },
          {
            pattern: /^[^\u4e00-\u9fa5]+$/,
            message: this.$t('不允许有中文字符'),
            trigger: 'blur'
          }
        ],
        nickName: [
          { required: true, message: this.$t(`user['Nickname cannot be empty']`), trigger: "blur" },
          {
            pattern: /^[^\u4e00-\u9fa5]+$/,
            message: this.$t('不允许有中文字符'),
            trigger: 'blur'
          }
        ],
        // password: [
        //   { required: true, message: this.$t(`user['User password cannot be empty']`), trigger: "blur" },
        //   { min: 5, max: 20, message: this.$t(`user['User password length must be between 5 and 20']`), trigger: 'blur' }
        // ],
        phonenumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t(`user['Please enter a valid mobile phone number']`),
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
    this.getConfigKey("sys.user.initPassword").then(response => {
      this.initPassword = response.msg;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? this.$t(`menu['Are you sure you want to enable it?']`) : this.$t(`menu['Are you sure you want to deactivate it?']`);
      this.$modal.confirm(text).then(function () {
        return changeUserStatus(row.userId, row.status);
      }).then(() => {
        let message = row.status === "0" ? this.$t(`menu['Activated successfully!']`) : this.$t(`menu['Deactivation successfully!']`)
        this.$modal.msgSuccess(message);
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getUser().then(response => {
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.open = true;
        this.title = this.$t('menu.addUser');
        this.form.password = this.initPassword;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids;
      getUser(userId).then(response => {
        this.form = response.data;
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.$set(this.form, "postIds", response.postIds);
        this.$set(this.form, "roleIds", response.roleIds ? response.roleIds[0] : undefined);
        this.open = true;
        this.title = this.$t('menu.editUser');
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt(this.$t(`user['Please enter your new password']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: this.$t(`user['User password length must be between 5 and 20']`)
      }).then(({ value }) => {
        resetUserPwd(row.userId, value).then(response => {
          this.$modal.msgSuccess(this.$t(`common['Modify successfully']`));
        });
      }).catch(() => { });
    },
    /** 分配角色操作 */
    handleAuthRole: function (row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateUser({
              ...this.form,
              roleIds: [this.form.roleIds]
            }).then(response => {
              this.$modal.msgSuccess(this.$t(`common['Modify successfully']`));
              this.open = false;
              this.getList();
            });
          } else {
            addUser({
              ...this.form,
              roleIds: [this.form.roleIds]
            }).then(response => {
              this.$modal.msgSuccess(this.$t(`common['Added successfully']`));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal.confirm(this.$t(`menu['Are you sure to delete the data item?']`)).then(function () {
        return delUser(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t(`common['Deleted successfully']`));
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = this.$t('user.importUser');
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", this.$t('user.importResult'), { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  display: flex;
  width: 100%;
  height: 100%;
}

.box-tree {
  width: 320px;
  background-color: #fff;
  margin-right: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  padding: 20px;
}

.box-content {
  background: #fff;
  border-radius: 8px;
  flex: 1;
  overflow: auto;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}
</style>
