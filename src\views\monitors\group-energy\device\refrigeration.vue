
<template>
    <div class="home">
        <div class="el_box">
            <el-row>
                <el-col :span="24">
                    <div class="grid-content bg-purple-dark">空调</div>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right">环境温度（电池仓）</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left">{{ vuele_1 }}</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right">环境湿度（电池仓）</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left">{{ vuele_2 }}</div>
                </el-col>

            </el-row>
            <el-row>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right">空调状态</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left">{{ vuele_3 }}</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right">空调制冷点</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left">{{ vuele_4 }}</div>
                </el-col>

            </el-row>
            <el-row>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right">空调制冷回差 </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left">{{ vuele_5 }}</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right">空调加热点</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left">{{ vuele_7 }}</div>
                </el-col>


            </el-row>
            <el-row>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right">空调加热回差 </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left">{{ vuele_8 }}</div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark right"> </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-dark left"></div>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script>

export default {
    name: "device",
    data() {
        return {
            vuele_1: '34℃',
            vuele_2: '35℃',
            vuele_3: '运行',
            vuele_4: '36℃',
            vuele_5: '36℃',
            vuele_6: '36℃',
            vuele_7: '36℃',
            vuele_8: '36℃',
        };
    },

    methods: {

    }
};
</script>
<style lang="scss" scoped>
.home {
    padding-right: 15px;
    text-align: center;
    height: 100%;

    .el-button {
        float: right;
        margin-bottom: 20px;
    }

    .el_box {
        border-top: 1px solid #BFBFBF;
        border-left: 1px solid #BFBFBF;
        margin-bottom: 30px;
        .el-col {
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid #BFBFBF;
            border-right: 1px solid #BFBFBF;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .el-col::after {
            content: attr(data-label);
            display: none;
        }

        .el-col:hover {
            overflow: visible;
            text-overflow: clip;
        }

        .left {
            text-align: left;
            padding: 0 10px 0 10px;
        }

        .right {
            text-align: right;
            padding: 0 10px 0 10px;
        }
    }

}
</style>
