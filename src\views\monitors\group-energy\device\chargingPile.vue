<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-09-09 15:16:16
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <div v-for="(cpItem, index) in cp" :key="cpItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ cpItem.name }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot">
        <el-descriptions-item :label="$t('充电桩通信状态')">
          <span>{{ getStatus(index) }}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('充电枪个数')">
          <span v-if="cpItem['chargingPile_19002']">{{ cpItem['chargingPile_19002'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 0：停机 1：开机 2故障 -->
        <el-descriptions-item :label="$t('充电桩状态')">
          <span v-if="cpItem['chargingPile_19001']">{{ get19001(cpItem['chargingPile_19001']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪输出电压')">
          <span v-if="cpItem['chargingPile_19004']">{{ cpItem['chargingPile_19004'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪输出电流')">
          <span v-if="cpItem['chargingPile_19005']">{{ cpItem['chargingPile_19005'] }} A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪输出功率')">
          <span v-if="cpItem['chargingPile_19006']">{{ cpItem['chargingPile_19006'] }} kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪当前SOC')">
          <span v-if="cpItem['chargingPile_19007']">{{ cpItem['chargingPile_19007'] }} %</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪充电电量')">
          <span v-if="cpItem['chargingPile_19008']">{{ cpItem['chargingPile_19008'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪充电金额')">
          <span v-if="cpItem['chargingPile_19009']">{{ cpItem['chargingPile_19009'] }} CNY</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪电池最高温度')">
          <span v-if="cpItem['chargingPile_19010']">{{ cpItem['chargingPile_19010'] }} ℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪充电时长')">
          <span v-if="cpItem['chargingPile_19012']">{{ cpItem['chargingPile_19012'] }} Min</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('1#枪状态')">
          <span v-if="cpItem['chargingPile_19011']">{{ get19011(cpItem['chargingPile_19011']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 2枪 -->
        <el-descriptions-item :label="$t('2#枪输出电压')">
          <span v-if="cpItem['chargingPile_19015']">{{ cpItem['chargingPile_19015'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪输出电流')">
          <span v-if="cpItem['chargingPile_19016']">{{ cpItem['chargingPile_19016'] }} A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪输出功率')">
          <span v-if="cpItem['chargingPile_19017']">{{ cpItem['chargingPile_19017'] }} kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪当前SOC')">
          <span v-if="cpItem['chargingPile_19018']">{{ cpItem['chargingPile_19018'] }} %</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪充电电量')">
          <span v-if="cpItem['chargingPile_19019']">{{ cpItem['chargingPile_19019'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪充电金额')">
          <span v-if="cpItem['chargingPile_19020']">{{ cpItem['chargingPile_19020'] }} CNY</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪电池最高温度')">
          <span v-if="cpItem['chargingPile_19021']">{{ cpItem['chargingPile_19021'] }} ℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪充电时长')">
          <span v-if="cpItem['chargingPile_19023']">{{ cpItem['chargingPile_19023'] }} Min</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('2#枪状态')">
          <span v-if="cpItem['chargingPile_19022']">{{ get19011(cpItem['chargingPile_19022']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('总输出功率')">
          <span v-if="cpItem['chargingPile_19003']">{{ cpItem['chargingPile_19003'] }} kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
export default {
  name: "device",
  data() {
    return {
    };
  },
  computed: {
    cp() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let data = this.$store.state.monitor.groupList[currentNodeKey]
      if (!data || Object.keys(data).length != 11) return []
      data.cp.forEach(item => {
        item.name = `${parseInt(item.dc) - 191000 + 1}#${this.$t('充电桩')}`
      })
      return data.cp
    },
    getStatus() {
      return (index) => {
        if (this.cp[index].isAnalysis == 0) {
          return this.cp.onLineState == '在线' ? this.$t('common.online') : this.$t('common.offline')
        } else if (this.cp[index].isAnalysis == 1) {
          return this.cp[index]['chargingPile_19000'] == '1' ? this.$t('common.online') : this.$t('common.offline')
        } else {
          return '--'
        }
      }
    },
    get19001() {
      return (num) => {
        switch (num) {
          case '0':
            return this.$t('common.Shutdown')
          case '1':
            return this.$t(`param['开机']`)
          case '2':
            return this.$t('common.fault')
        }
      }
    },
    get19011() {
      return (num) => this.getGunStatus(num)
    }
  },
  methods: {
    getGunStatus(num) {
      switch (num) {
        case '0':
          return this.$t('空闲')
        case '1':
          return this.$t('插枪')
        case '2':
          return this.$t('充电等待')
        case '3':
          return this.$t('启动中')
        case '4':
          return this.$t('充电中')
        case '5':
          return this.$t('重连')
        case '6':
          return '--'
        case '7':
          return this.$t('结算状态')
        case '8':
          return this.$t('故障状态')
        case '9':
          return this.$t('放电中')
        case '10':
          return '--'
        case '11':
          return this.$t('预约状态')
        case '12':
          return this.$t('后台预约状态')
        case '13':
          return '--'
        case '14':
          return this.$t('充电完成状态')
        case '15':
          return this.$t('APP，预约状态')
        case '16':
          return this.$t('试用期到，停止服务状态')
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
  overflow: auto;
}
</style>
