<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-30 16:25:17
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-01-22 12:04:35
 * @FilePath: \办公文档\代码\新建文件夹\src\views\dashboard\PieChart_2.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="position: relative;width: 100%;height: 100%;">
    <div ref="echartRef" :style="{ height: height, width: width }" />
    <div class="echarts-total">
      <div class="total-num">{{ sumData.sum }}</div>
      <div>{{ $t('home.pieRadioText2') }}</div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import resize from './mixins/resize'

import { getDeviceSum } from '@/api/system/home'

export default {
  mixins: [resize],
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '280px'
    },
  },
  data() {
    return {
      chart: null,
      sumData: {}
    }
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.initChart()
    // })
    this.getDeviceSumFn()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getDeviceSumFn() {
      getDeviceSum().then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        this.sumData = res.data
        this.initChart()
      })
    },
    initChart() {
      if (this.chart) this.chart.dispose()
      this.chart = echarts.init(this.$refs.echartRef)
      this.chart.clear();
      let options = {
        tooltip: {
          // trigger: 'pie',
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
          trigger: 'item'
        },
        legend: {
          left: 'center',
          // bottom: '10',
          data: [this.$t('common.normal'), this.$t('common.fault')],
          // show: false
        },
        series: [
          {
            name: this.$t('home.pieRadio2'),
            type: 'pie',
            radius: ['60%', '80%'],
            label: {
              show: false,
              position: 'center'
            },
            top: '10%',
            emphasis: {
              // label: {
              //   show: true,
              //   fontSize: 20,
              //   fontWeight: 'bold',
              //   formatter: '{b}:{c}: ({d}%)',
              // }
            },
            avoidLabelOverlap: false,
            data: [
              { value: this.sumData.normal, name: this.$t('common.normal') },
              { value: this.sumData.malfunction, name: this.$t('common.fault') },
            ],
            animationEasing: 'cubicInOut',
            animationDuration: 2600,
            itemStyle: {
              normal: {
                color: function (colors) {
                  var colorList = [
                    '#79B5FF',
                    '#FA8558',
                    '#5FC5F6',
                    '#3BA272',
                  ];
                  return colorList[colors.dataIndex];
                }
              },
            }
          }
        ]
      }
      options && this.chart.setOption(options)
    }
  }
}
</script>

<style lang="scss" scoped>
.echarts-total {
  position: absolute;
  top: 46%;
  width: 100%;
  /* left: 47%; */
  text-align: center;
  font-size: 14px;

  .total-num {
    font-size: 25px;
    font-weight: 600;
  }
}
</style>
