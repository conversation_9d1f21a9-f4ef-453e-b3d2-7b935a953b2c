{"name": "elecloud", "version": "2.16.1", "description": "elecloud云平台", "author": "elecloud", "license": "MIT", "scripts": {"serve": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "build": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "eslint --ext .js,.vue src&&vue-cli-service lint", "select:mode": "node build/editMode.js", "release": "standard-version"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["elecloud", "云平台"], "dependencies": {"@riophae/vue-treeselect": "0.4.0", "@vuemap/vue-amap": "^0.1.17", "animejs": "^3.2.1", "autofit.js": "^3.0.7", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "echarts": "^5.4.3", "element-china-area-data": "^6.1.0", "element-ui": "2.15.13", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "inquirer": "^8.2.6", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "json2xls": "^0.1.2", "markdown-it": "^14.1.0", "moment": "^2.29.4", "nprogress": "0.2.0", "postcss-pxtorem": "^5.1.1", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "swiper": "^6.8.4", "vue": "^2.7.0", "vue-baidu-map": "^0.21.22", "vue-count-to": "^1.0.13", "vue-cropper": "0.5.5", "vue-i18n": "7.3.2", "vue-jsonp": "^2.0.0", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue2-google-maps": "^0.10.7", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.5.18", "@vue/cli-plugin-eslint": "4.5.18", "@vue/cli-service": "4.5.18", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "^6.8.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "speed-measure-webpack-plugin": "^1.5.0", "standard-version": "^9.5.0", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "webpack-bundle-analyzer": "^4.10.1"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}