<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-01-03 17:56:01
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-30 09:53:55
 * @FilePath: \elecloud_platform-main\src\views\screen\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="screen-home">
    <InnerLink ref="screenIframe" :iframeId="'iframe' + 'Screen'" :src="src"></InnerLink>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import { getToken } from '@/utils/auth'
import Cookies from 'js-cookie'

import InnerLink from "@/layout/components/InnerLink/index"

const { proxy } = getCurrentInstance()
const origin = window.location.origin
const srcOptions =
{
  test: 'https://test.elecod-cloud.com',
  zh: 'https://screencn.elecod-cloud.com',
  en: 'https://screenen.elecod-cloud.com',
  dev: 'http://************:8080',
}
const src = ref(srcOptions[process.env.VUE_APP_SCREEN])
const time = ref(null)

console.log(src.value);

const params = ref({
  type: 'setToken',
  token: getToken() ? 'Bearer ' + getToken() : ''
})
onMounted(() => {
  /**
   * 给子窗口传递消息
   */
  time.value = setInterval(() => {
    if (proxy.$refs.screenIframe) {
      if (proxy.$refs.screenIframe.$refs['iframeScreen']) {
        proxy.$refs.screenIframe.$refs['iframeScreen'].contentWindow.postMessage({ ...params.value, lang: Cookies.get('language'), domainName: window.location.hostname }, src.value)
      }
    }
  }, 3000)
})

window.onmessage = (e) => {
  if (e.origin == src.value) {
    if (e.data.projectName) proxy.$router.push('../../monitors/opticalstorage?name=' + e.data.projectName)
    clearInterval(time.value)
  }
}

onUnmounted(() => {
  clearInterval(time.value)
})
</script>

<style scoped lang="scss">
.screen-home {
  width: 100%;
  height: 100%;
}
</style>
