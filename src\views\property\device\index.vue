<template>
  <div class="page-two-box">
    <div style="position: absolute;left: 0;top: 45%;" @click="handleTreeExpand">
      <svg-icon icon-class="tree-expand" class-name="icon" style="height: 50px;"
        :style="{ transform: treeExpand ? 'rotate(0deg)' : 'rotate(180deg)' }" />
    </div>
    <DeptTree @nodeClick="nodeClick" v-show="treeExpand" />
    <div class="page-two-box-content">
      <div class="input_box">
        <div class="header-title">
          {{ $route.meta.title }}
        </div>
        <div>
          <div class="input_ment">
            <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
              <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
                <el-option :label="$t('project.name')" value="projectName"></el-option>
                <el-option :label="$t('国家')" value="country"></el-option>
                <el-option :label="$t('创建人员')" value="nickName"></el-option>
              </el-select>
            </el-input>
          </div>
          <div class="input_ment">
            <el-button type="primary" @click="handleSearchClick()" icon="el-icon-search">{{ $t('common.search')
            }}</el-button>
          </div>
          <div class="input_ment">
            <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus"
              v-hasPermi="['system:project:add']">{{
                $t(`project['Add item']`) }}</el-button>
          </div>
        </div>
      </div>
      <div class="table_box">
        <!-- table -->
        <el-table v-loading="loading" :data="tableData" style="width: 100%;">
          <el-table-column type="index" label="#" width="60" align="center" />
          <el-table-column prop="projectName" :label="$t('project.name')" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span @click="handleEditClick(scope.row, 1)" class="primary pointer">{{ scope.row.projectName
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="projectAddress" :label="$t('project.address')" show-overflow-tooltip align="center" />
          <el-table-column prop="country" :label="$t('国家')" show-overflow-tooltip align="center" />
          <el-table-column prop="projectState" :label="$t('common.status')" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.projectState == 1">{{ $t('common.Enable') }}</el-tag>
              <el-tag v-else type="danger">{{ $t('common.Deactivate') }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nickName" :label="$t('创建人员')" show-overflow-tooltip align="center" />
          <el-table-column prop="timeZone" :label="$t(`common['时区']`)" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              {{ `UTC${scope.row.timeZone}` }}
            </template>
          </el-table-column>
          <el-table-column prop="countryCurrencyId" :label="$t('货币')" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              {{ getCurrencyText(scope.row.countryCurrencyId) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip align="center" />
          <el-table-column fixed="right" :label="$t('common.handle')" width="200" align="center">
            <template slot-scope="scope">
              <!-- <el-button  type="text" size="small">查看设备</el-button> -->
              <el-button @click="handleEditClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:project:edit']" style="padding: 0;">{{ $t('common.edit') }}</el-button>
              <el-button @click="handleDeleteClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:project:remove']" style="padding: 0;">{{ $t('common.delete') }}</el-button>
              <el-button @click="handleAllotClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:project:allotDevice']" style="padding: 0;">{{ $t('分配项目')
                }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
          @pagination="getList" style="margin-top: 20px;text-align: right;" />
      </div>
      <!-- 新建项目弹窗 -->
      <el-dialog :visible.sync="dialogVisible" :close-on-click-modal="true" :modal-append-to-body="false"
        :title="dialogName" center>
        <div class="dialog-add-wrapper">
          <el-form label-width="auto" :rules="rules" ref="formLabelAlign" :model="formLabelAlign">
            <div class="map_box" style="margin-bottom:20px">
              <div v-if="isInChina == 1" class="map-container">
                <div style="width: 100%;height: 100%">
                  <AMP :center="center" :markers="markers" :zoom="zoom" @search="ampSearch"></AMP>
                </div>
              </div>
              <GMP :isSearch="true" @search="googleSearch" :xy="{
                lat: formLabelAlign.projectLatitudey,
                lng: formLabelAlign.projectLatitudex
              }" v-if="isInChina == 2"></GMP>
            </div>
            <el-form-item :label="`${$t('project.name')}`" prop="projectName">
              <el-input v-model="formLabelAlign.projectName"
                :placeholder="$t(`project['Please enter project name']`)" />
            </el-form-item>
            <el-form-item :label="`${$t('project.area')}`" prop="address">
              <span slot="label">
                {{ $t('project.area') }}
                <el-tooltip class="item" effect="dark" :content="$t('提示：手动选择或者输入搜索选择')" placement="bottom">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <el-row :gutter="20" style="display: flex">
                <el-col :span="12">
                  <el-cascader size="large" :options="CityData" filterable v-model="formLabelAlign.country"
                    style="width: 100%;" />
                </el-col>
                <el-col :span="12">
                  <el-cascader size="large" :options="getAreaData" filterable v-model="formLabelAlign.address"
                    style="width: 100%" />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="`${$t('project.address')}`" prop="projectAddress">
              <span slot="label">
                {{ $t('project.address') }}
                <el-tooltip class="item" effect="dark" :content="$t('提示：手动输入或者点击上方地图自动生成地址')" placement="bottom">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <el-input v-model="formLabelAlign.projectAddress" :placeholder="$t('project.address')" />
            </el-form-item>
            <el-form-item :label="`${$t('project.lonAndLat')}`" prop="lonAndLat">
              <span slot="label">
                {{ $t('project.lonAndLat') }}
                <el-tooltip class="item" effect="dark"
                  :content="$t('提示：点击上方地图自动生成地址或者手动输入，格式为：经度：113.94876， 纬度：22.636858')" placement="bottom">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <el-row :gutter="20" style="display: flex">
                <el-col :span="12">
                  <el-form-item prop="projectLatitudex" :rules="[
                    { pattern: /^(\-|\+)?\d+(\.\d+)?$/, message: this.$t('请输入正确的经度坐标(数字)'), trigger: 'blur' }
                  ]">
                    <el-input v-model="formLabelAlign.projectLatitudex" :placeholder="$t('请输入经度或点击地图自动生成')" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="projectLatitudey" :rules="[
                    { pattern: /^(\-|\+)?\d+(\.\d+)?$/, message: this.$t('请输入正确的纬度坐标(数字)'), trigger: 'blur' }
                  ]">
                    <el-input v-model="formLabelAlign.projectLatitudey" :placeholder="$t('请输入纬度或点击地图自动生成')" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="$t(`common['时区']`)" prop="timeZoneId">
              <el-select v-model="formLabelAlign.timeZoneId" :placeholder="$t('common.select')" filterable
                style="width: 100%;">
                <el-option v-for="item in timeOptions" :key="item.id" :label="item[getPropFn]" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('货币')" prop="countryCurrencyId">
              <el-select v-model="formLabelAlign.countryCurrencyId" :placeholder="$t('common.select')" filterable
                style="width: 100%;">
                <el-option v-for="item in currencyOptions" :key="item.id" :label="`${item.country} - ${item.currency}`"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="`${$t('project.sn')}`" prop="deviceIds">
              <el-table :data="checkList" stripe :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
                :cell-style="{ 'text-align': 'center', 'font-size': '14px' }" style="width: 100%;">
                <el-table-column prop="deviceName" :label="$t('device.name')" show-overflow-tooltip width="140" />
                <el-table-column prop="deviceSerialNumber" :label="$t('device.screenId')" show-overflow-tooltip
                  width="200" />
                <el-table-column prop="deviceModel" :label="$t('device.model')" show-overflow-tooltip width="120" />
                <el-table-column prop="deviceType" :label="$t('device.type')" show-overflow-tooltip width="320">
                  <template slot-scope="scope">
                    <el-tag type="primary">{{ getDeviceTypeFn(scope.row.deviceType, true) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="deviceRatedPower" :label="`${$t('monitor.topItem2')}（kW）`" show-overflow-tooltip
                  width="200" />
                <el-table-column prop="photovoltaicInstalledCapacity" :label="`${$t('monitor.topItem6')}（kWp）`"
                  show-overflow-tooltip width="300" />
                <el-table-column :label="$t('common.handle')" fixed="right">
                  <template slot-scope="props">
                    <el-button type="text" @click="handleSnDeleteClick(props.row, props.$index)">{{ $t('common.delete')
                    }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="line"></div>
              <el-button style="width: 100%" @click="handleAddSnClick" icon="el-icon-plus">{{ $t('添加要绑定项目的设备')
              }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCancelClick('formLabelAlign')">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" v-if="dialogName == $t(`project['Add item']`)"
            @click="handleAddConfirm('formLabelAlign')">{{ $t('common.confirm') }}</el-button>
          <el-button type="primary" v-if="dialogName == $t(`project['Modify Project']`)"
            @click="handleEditConfirm('formLabelAlign')">{{ $t('common.save') }}</el-button>
        </span>
      </el-dialog>
      <!-- 添加设备弹窗 -->
      <el-dialog :visible.sync="dialogSn" :close-on-click-modal="false" :modal-append-to-body="false"
        :title="$t('device.selectDeviceTitle')" center>
        <el-table :data="bandingList" style="width: 100%;" @selection-change="handleSelectionChange" ref="multipleTable"
          row-key="deviceId">
          <el-table-column type="selection" width="55" :reserve-selection="true" align="center" />
          <el-table-column prop="deviceName" :label="$t('device.name')" show-overflow-tooltip width="120"
            align="center" />
          <el-table-column prop="deviceSerialNumber" :label="$t('device.screenId')" show-overflow-tooltip width="200"
            align="center">
            <template slot-scope="scope">
              {{ scope.row.deviceSerialNumber }}<i class="el-icon-copy-document copy"
                v-clipboard:copy="scope.row.deviceSerialNumber" v-clipboard:success="copySuccess"></i>
            </template>
          </el-table-column>
          <el-table-column prop="deviceModel" :label="$t('device.model')" show-overflow-tooltip width="120"
            align="center" />
          <el-table-column prop="deviceType" :label="$t('device.type')" show-overflow-tooltip width="320"
            align="center">
            <template slot-scope="scope">
              <el-tag type="primary">{{ getDeviceTypeFn(scope.row.deviceType, true) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deviceRatedPower" :label="`${$t('monitor.topItem2')}（kW）`" show-overflow-tooltip
            width="200" align="center" />
          <el-table-column prop="photovoltaicInstalledCapacity" :label="`${$t('monitor.topItem6')}（kWp）`"
            show-overflow-tooltip width="300" align="center" />
        </el-table>
        <pagination v-show="bindingTotal > 0" :total="bindingTotal" :page.sync="bindingQueryInfo.pageNum"
          :limit.sync="bindingQueryInfo.pageSize" @pagination="getBandingListFn"
          style="margin-top: 20px;text-align: right;" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleSnCancel">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleSnConfirm()">{{ $t('common.confirm') }}</el-button>
        </span>
      </el-dialog>
      <!-- 查看设备弹窗 -->
      <el-dialog :visible.sync="dialogView" :close-on-click-modal="false" :modal-append-to-body="false"
        :title="$t('home.pieRadio2')" center>
        <el-table :data="formLabelAlign.ylkDevices" stripe
          :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
          :cell-style="{ 'text-align': 'center', 'font-size': '14px' }" style="width: 100%;" row-key="deviceId">
          <el-table-column prop="deviceName" :label="$t('device.name')" show-overflow-tooltip width="120" />
          <el-table-column prop="deviceSerialNumber" :label="$t('device.screenId')" show-overflow-tooltip width="200">
            <template slot-scope="scope">
              {{ scope.row.deviceSerialNumber }}<i class="el-icon-copy-document copy"
                v-clipboard:copy="scope.row.deviceSerialNumber" v-clipboard:success="copySuccess"></i>
            </template>
          </el-table-column>
          <el-table-column prop="deviceModel" :label="$t('device.model')" show-overflow-tooltip width="120" />
          <el-table-column prop="deviceType" :label="$t('device.type')" show-overflow-tooltip width="320">
            <template slot-scope="scope">
              <el-tag type="primary">{{ getDeviceTypeFn(scope.row.deviceType, true) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deviceRatedPower" :label="`${$t('monitor.topItem2')}（kW）`" show-overflow-tooltip
            width="200" />
          <el-table-column prop="photovoltaicInstalledCapacity" :label="`${$t('monitor.topItem6')}（kWp）`"
            show-overflow-tooltip width="300" />
        </el-table>
        <pagination v-show="bindingTotal > 0" :total="bindingTotal" :page.sync="bindingQueryInfo.pageNum"
          :limit.sync="bindingQueryInfo.pageSize" @pagination="getBandingListFn"
          style="margin-top: 20px;text-align: right;" />
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dialogView = false">{{ $t('common.Closure') }}</el-button>
        </span>
      </el-dialog>
      <!-- 分配设备 -->
      <el-dialog :visible.sync="dialogAllotVisible" center :modal-append-to-body="false" width="600px"
        :title="$t('分配项目')">
        <div style="height: 100px">
          <div style="margin-bottom: 20px">{{ $t('提示：手动选择或者输入搜索选择') }}</div>
          <el-form :model="allotForm" :rules="allotRules" ref="allotFormRef" label-width="auto">
            <el-form-item :label="`${$t('所要分配的用户')}`" prop="userId">
              <el-select v-model="allotForm.userId" :placeholder="$t('common.select')" filterable style="width: 100%;">
                <el-option v-for="item in userList" :key="item.userId" :label="item.userName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-form>
          <span>{{ $t('注：分配项目，会把项目所绑定的设备也一起被分配给用户。') }}</span>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCancelAllotClick()">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleConfirmAllot('allotFormRef')">{{ $t('common.confirm') }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { BaiduMap, BmMapType } from 'vue-baidu-map'
import { pcaTextArr } from "element-china-area-data";
import Cookies from 'js-cookie'
import GMP from '@/components/Gmp/index.vue'
import AMP from '@/components/Amp/index.vue'
import { elRectification } from 'autofit.js'
import { mapGetters } from 'vuex'
import worldData from '../../../../public/world.json'

import { projectList, addProject, editProject, getProjectInfo, deleteProject } from '@/api/property/device'
import { getBandingList } from '@/api/property/sn'
import _ from 'lodash'
import { timeList } from '@/api/operation/time'
import { allCurrency } from '@/api/operation/currency'
import { listUser } from '@/api/system/user'
import { allotDevice } from '@/api/monitors/opticalstorage'
import { getDeviceType } from '@/hook/useDeviceType'

import DeptTree from '@/components/DeptTree'

export default {
  data() {
    return {
      projectName: '',
      dialogName: '',
      dialogVisible: false,
      total: 12,
      tableData: [],
      formLabelAlign: {
        projectName: '',
        address: [],
        projectAddress: '',
        projectLatitudex: '',
        projectLatitudey: '',
        country: ['中国'],
        lonAndLat: undefined
      },
      rules: {
        // address: [
        //   { required: true, message: '请选择区域或点击地图自动生成', trigger: 'change' }
        // ],
        projectAddress: [
          { required: true, message: this.$t('请输入地址或点击地图自动生成'), trigger: 'change' }
        ],
        lonAndLat: [
          { required: true, message: this.$t('请输入经纬度坐标或点击地图自动生成'), trigger: 'change' }
        ],
        // projectLatitudex: [
        //   { required: true, message: this.$t('请输入经度或点击地图自动生成'), trigger: 'change' },
        //   { pattern: /^(\-|\+)?\d+(\.\d+)?$/, message: this.$t('请输入正确的经度坐标(数字)') }
        // ],
        // projectLatitudey: [
        //   { required: true, message: this.$t('请输入纬度或点击地图自动生成'), trigger: 'change' },
        //   { pattern: /^(\-|\+)?\d+(\.\d+)?$/, message: this.$t('请输入正确的纬度坐标(数字)') }
        // ],
        projectName: [
          { required: true, message: this.$t(`project['Please enter project name']`), trigger: 'change' }
        ],
        timeZoneId: [
          { required: true, message: this.$t('common.select'), trigger: 'change' }
        ],
        countryCurrencyId: [
          { required: true, message: this.$t('common.select'), trigger: 'change' }
        ],
      },
      CityData: [{
        value: '中国',
        label: '中国'
      }],
      pcaTextArr,
      // center: { lng: 0, lat: 0 },// 经纬度
      center: [113.933142, 22.636963],// 经纬度
      markers: [],
      initCenter: { lng: 0, lat: 0 },// 经纬度
      zoom: 10,    // 地图展示级别
      address: '',
      form: {
        projectAddress: '',
        addrPoint: '',
      },
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      loading: false,
      bandingList: [],
      bindingTotal: 10,
      bindingQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      dialogSn: false,
      multipleSelection: [],
      checkList: [],
      dialogView: false,
      isInChina: 0, // 默认设置为非国内
      timeOptions: [],
      currencyOptions: [],
      /**
       * 分配设备
       */
      userList: [],
      allotForm: {
        deviceId: '',
        projectId: '',
        userId: '',
      },
      dialogAllotVisible: false,
      // 表单
      allotRules: {
        userId: [
          { required: true, message: this.$t('请选择要分配的用户'), trigger: 'blur' }
        ],
      },
      // 搜索
      searchKey: 'projectName',
      searchValue: '',
      treeExpand: false
    };
  },
  components: {
    BaiduMap,
    GMP,
    BmMapType,
    AMP,
    DeptTree
  },
  computed: {
    getAreaData() {
      let isZh = Cookies.get('language') == 'zh' ? true : false
      if (this.formLabelAlign.country[0] == '中国') return pcaTextArr
      let country = ''
      // if (isZh) {
      //   country = worldData.Earth.Country.find(item => item.CountryName == this.formLabelAlign.country[0])
      // } else {
      country = worldData.Earth.Country.find(item => item.CountryID == this.formLabelAlign.country[0])
      // }
      if (!country) return []
      if (!country.Station || !country.Station.length) {
        // this.$message({
        //   type: 'error',
        //   message: '暂时没有这个国家的数据哦~请联系管理员添加'
        // })
        return []
      } else {
        let arr = []
        country.Station.forEach(item => {
          let obj = {
            label: isZh ? item.StationName : item.StationID,
            value: item.StationID,
            children: []
          }
          if (!_.isArray(item.City)) {
            obj.children.push({
              label: isZh ? item.City.CityName : item.City.CityID,
              value: item.City.CityID
            })
          } else {
            item.City.forEach(city => {
              obj.children.push({
                label: isZh ? city.CityName : city.CityID,
                value: city.CityID
              })
            })
          }
          arr.push(obj)
        })
        return arr
      }
    },
    getCurrencyText() {
      return (id) => {
        let value = this.currencyOptions.find(item => item.id == id)
        return `${value?.currency}`
      }
    },
    ...mapGetters([
      'userInfo',
    ]),
    getPropFn() {
      let lang = this.$store.getters.language
      switch (lang) {
        case 'zh':
          return 'timeZoneAddress'
        case 'en':
          return 'timeZoneAddressUs'
        case 'it':
          return 'timeZoneAddressIt'
      }
    }
  },
  watch: {
    'formLabelAlign.projectLatitudex': {
      handler(val) {
        this.formLabelAlign.lonAndLat = val == '' ? undefined : this.formLabelAlign.projectLatitudex
      }
    },
    'formLabelAlign.projectLatitudey': {
      handler(val) {
        this.formLabelAlign.lonAndLat = val == '' ? undefined : this.formLabelAlign.projectLatitudey
      }
    },
    'formLabelAlign.lonAndLat': {
      handler(val) {
        if (this.formLabelAlign.projectLatitudey == '' || this.formLabelAlign.projectLatitudex == '') {
          this.formLabelAlign.lonAndLat = undefined
        } else {
          this.formLabelAlign.lonAndLat = '有值'
        }
      }
    }
  },
  mounted() {
    this.getList()
    this.getBandingListFn()
    let isZh = Cookies.get('language') == 'zh' ? true : false
    worldData.Earth.Country.forEach(item => {
      this.CityData.push({
        label: isZh ? item.CountryName : item.CountryID,
        value: item.CountryID
      })
    })
    this.checkLocation()
    this.getTimeOptions()
    this.getCurrencyOptions()
    this.listUserFn()
    if (process.env.VUE_APP_SCREEN != 'zh') this.rules.projectName.push({
      pattern: /^[^\u4e00-\u9fa5]+$/,
      message: this.$t('不允许有中文字符'),
      trigger: 'blur'
    })
  },
  methods: {
    checkLocation() {
      this.$store.dispatch('common/getIpInfo').then(res => {
        if (res.countryCode === 'CN') {
          this.isInChina = 1;
        } else {
          this.isInChina = 2;
        }
      })
    },
    // 获取项目列表
    getList() {
      this.loading = true
      projectList({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        [this.searchKey]: this.searchValue,
        deptId: this.queryInfo.deptId
      }).then(res => {
        this.tableData = res.rows
        this.total = res.total
        this.loading = false
      }
      );
    },
    // 地图搜索
    querySearchAsync(str, cb) {
      var options = {
        onSearchComplete: function (res) { //检索完成后的回调函数
          var s = [];
          if (local.getStatus() == BMAP_STATUS_SUCCESS) {
            for (var i = 0; i < res.getCurrentNumPois(); i++) {
              s.push(res.getPoi(i));
            }
            cb(s) //获取到数据时，通过回调函数cb返回到<el-autocomplete>组件中进行显示
          } else {
            cb(s)
          }
        }
      }
      var local = new BMap.LocalSearch(this.map, options) //创建LocalSearch构造函数
      local.search(str) //调用search方法，根据检索词str发起检索
    },
    // 地图搜索完选择结果
    handleSelect(item) {
      this.formLabelAlign.projectAddress = item.address
      this.formLabelAlign.projectLatitudex = item.point.lng
      this.formLabelAlign.projectLatitudey = item.point.lat
      // item.address详细地址
      var add = item.address.replace(/(?<=[省市区])/g, "$&,")
        .split(",");
      this.formLabelAlign.address = [add[0], add[1], add[2]]
      this.mk = new BMap.Marker(item.point)
      this.map.addOverlay(this.mk)
      this.center = item.point
    },
    //新建按钮
    handleAddClick() {
      this.dialogName = this.$t(`project['Add item']`)
      this.center = [113.933142, 22.636963]
      this.markers = []
      this.zoom = 14
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.formLabelAlign = {
          address: [],
          deviceIds: [],
          projectAddress: '',
          projectLatitudex: undefined,
          projectLatitudey: undefined,
          projectName: '',
          country: ['中国'],
          timeZoneId: undefined,
          lonAndLat: undefined,
          countryCurrencyId: undefined
        }
        this.multipleSelection = []
        this.checkList = []
        this.resetForm('formLabelAlign')
        // elRectification('.map-container')
        elRectification('.el-form')
      })
    },
    // 点击地图获取地址、经纬度、区域
    handleMapClick(e) {
      // 此时已经可以获取到BMap类
      if (this.BMap) {
        // Geocoder() 类进行地址解析
        // 创建地址解析器的实例
        const geoCoder = new this.BMap.Geocoder()
        // getLocation() 类--利用坐标获取地址的详细信息
        // getPoint() 类--获取位置对应的坐标
        geoCoder.getLocation(e.point, (res) => {
          // console.log('获取经纬度', e.point, '获取详细地址', res)
          // 表单具体地址
          let isOverseas = res.address.indexOf(', ') == -1
          if (isOverseas) {
            this.formLabelAlign.country = ['中国']
          } else {
            let arr = res.address.split(', ')
            this.formLabelAlign.country = [arr[arr.length - 1]]
          }
          this.formLabelAlign.projectAddress = res.address
          // 表单经纬度
          this.formLabelAlign.projectLatitudex = e.point.lng
          this.formLabelAlign.projectLatitudey = e.point.lat
          const addrComponent = res.addressComponents
          const surroundingPois = res.surroundingPois
          // 省份
          const province = addrComponent.province
          // 城市
          const city = addrComponent.city
          // 区域
          const district = addrComponent.district
          // 具体地址
          let addr = addrComponent.street
          if (surroundingPois.length > 0 && surroundingPois[0].title) {
            if (addr) {
              addr += `-${surroundingPois[0].title}`
            } else {
              addr += `${surroundingPois[0].title}`
            }
          } else {
            addr += addrComponent.streetNumber
          }
          this.choosedLocation = { province, city, district, addr, ...this.center }
          // 表单区域
          // this.formLabelAlign.address = [province, city, district]
          // console.log(!province, city, district);
          this.formLabelAlign.address = []
          if (province) this.formLabelAlign.address.push(province)
          if (city) this.formLabelAlign.address.push(city)
          if (district) this.formLabelAlign.address.push(district)
          this.formatAreaFn()
        })
        this.map.clearOverlays()
        this.mk = new BMap.Marker(e.point)
        this.map.addOverlay(this.mk)
      }
    },
    //项目添加
    handleAddConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          addProject({
            projectAddress: this.formLabelAlign.projectAddress,
            projectArea: this.formLabelAlign.address.join(','),
            projectLatitudex: this.formLabelAlign.projectLatitudex,
            projectLatitudey: this.formLabelAlign.projectLatitudey,
            projectName: this.formLabelAlign.projectName,
            deviceIds: this.checkList.map((item) => item.deviceId),
            country: this.formLabelAlign.country.join(','),
            timeZoneId: this.formLabelAlign.timeZoneId,
            countryCurrencyId: this.formLabelAlign.countryCurrencyId,
          }).then(res => {
            if (res.code !== 200) return this.$message({
              type: 'error',
              message: res.msg
            });
            this.getList()
            this.getBandingListFn()
            this.$message({
              type: 'success',
              message: this.$t(`common['Added successfully']`)
            });
            this.dialogVisible = false
          })
        } else {
          return false;
        }
      });
    },
    //项目修改
    handleEditConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          editProject({
            projectAddress: this.formLabelAlign.projectAddress,
            projectArea: this.formLabelAlign.address.join(','),
            projectLatitudex: this.formLabelAlign.projectLatitudex,
            projectLatitudey: this.formLabelAlign.projectLatitudey,
            projectName: this.formLabelAlign.projectName,
            deviceIds: this.checkList.map((item) => item.deviceId),
            projectId: this.formLabelAlign.projectId,
            country: this.formLabelAlign.country.join(','),
            timeZoneId: this.formLabelAlign.timeZoneId,
            countryCurrencyId: this.formLabelAlign.countryCurrencyId
          }).then(res => {
            if (res.code !== 200) return this.$message({
              type: 'error',
              message: this.$t(`common['Change failed]`)
            });
            this.$message({
              type: 'success',
              message: this.$t(`common['Modify successfully']`)
            });
            this.dialogVisible = false
            this.getList()
            this.getBandingListFn()
          })
        } else {
          return false;
        }
      });
    },
    // 搜索
    handleSearchClick() {
      this.getList()
    },
    // 地图加载
    initMap({ BMap, map }) {
      this.BMap = BMap;
      this.map = map
      this.zoom = 16;
      this.map.removeEventListener("click"); // 移除原有的点击事件监听器，如果有的话
      //  使用ipapi.co获取用户IP地址并发送请求获取经纬度数据
      // this.geolocate()
      let ip = JSON.parse(Cookies.get('ip'))
      if (ip.ip) {
        this.center = {
          lat: ip.latitude,
          lng: ip.longitude,
        };
        this.initCenter.lng = ip.longitude
        this.initCenter.lat = ip.latitude
        this.map.clearOverlays()
        this.mk = new BMap.Marker(this.initCenter)
        this.map.addOverlay(this.mk)
      }
      this.map.addEventListener('click', (event) => {
        const latLng = event.Dg // 获取点击位置的经纬度坐标
        this.formLabelAlign.projectLatitudex = latLng.lng
        this.formLabelAlign.projectLatitudey = latLng.lat
      });
    },
    geolocate() {
      let vm = this

      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position) => {
          if (position && position.coords && position.coords.latitude) {
            // alert("获取地理位置："+position.coords.latitude+","+position.coords.longitude)
            vm.hasSetPin = true
            vm.zoom = 10
            vm.center = {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            };
            vm.initCenter.lng = longitude
            vm.initCenter.lat = latitude
            vm.center_ = vm.center
            vm.markers.push({ position: vm.center });
          }
        }, (error) => {  // html5 默认调用的谷歌的接口，会有安全限制
          switch (error.code) {
            case error.PERMISSION_DENIED: // 许可拒绝,用户选了不允许
              alert("您拒绝对获取地理位置的请求")
              alert(error.message);
              break;
            case error.POSITION_UNAVAILABLE: // 连不上GPS卫星，或者网络断了
              alert("位置信息是不可用的");
              alert(error.message);
              break;
            case error.TIMEOUT:  // /超时了
              alert("请求您的地理位置超时");
              alert(error.message);
              break;
            case error.UNKNOWN_ERROR:
              alert("未知错误");
              alert(error.message);
              break;
          }
        });
      } else {
        alert("未获取获取到地理位置");
        vm.markers.push({ position: vm.center });
      }
    },
    //列表注销按键
    handleDeleteClick(row) {
      this.$confirm(this.$t('device.deleteHint'), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteProject({
          projectIds: row.projectId
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.getBandingListFn()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    //列表里修改按键
    handleEditClick(row, type) {
      getProjectInfo({ projectId: row.projectId }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        });
        this.formLabelAlign = {
          ...res.data,
          address: res.data.projectArea?.split(','),
          country: res.data.country ? res.data.country.split(',') : ["中国"],
          ylkDevices: res.data.ylkDevices ?? []
        }
        // 如果省市区第三个为空，就删除
        if (!this.formLabelAlign.address[2]) this.formLabelAlign.address.splice(2, 1)
        // 对没有的国家及省市区进行重新添加
        this.formatAreaFn()
        this.checkList = []
        if (res.data.ylkDevices) this.checkList = [...res.data.ylkDevices]
        // this.center = { lng: res.data.projectLatitudex, lat: res.data.projectLatitudey }
        this.center = [res.data.projectLatitudex, res.data.projectLatitudey]
        this.markers = [
          {
            ...row,
            position: this.center,
            id: row.projectId
          }
        ]
        this.zoom = row.country !== '中国' ? 3 : 14
        if (!this.map) return
        this.map.clearOverlays()
        this.mk = new BMap.Marker(this.center)
        this.map.addOverlay(this.mk)
      })
      if (type) return this.dialogView = true
      this.add_new = true
      this.dialogName = this.$t(`project['Modify Project']`)
      this.dialogVisible = true;
    },
    //项目取消
    handleCancelClick() {
      this.dialogVisible = false
    },
    // 获取未绑定的设备
    getBandingListFn() {
      getBandingList(this.bindingQueryInfo).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        this.bandingList = res.rows
        this.bindingTotal = res.total
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSnConfirm() {
      this.checkList = [...this.checkList, ...this.multipleSelection]
      // 去重
      let arr = new Set(this.checkList)
      this.checkList = Array.from(arr)
      this.dialogSn = false
    },
    handleAddSnClick() {
      this.dialogSn = true
      this.$nextTick(() => {
        if (!this.checkList.length) {
          this.bandingList.forEach(item => {
            this.$refs.multipleTable.toggleRowSelection(item, false)
          })
          this.$refs.multipleTable.clearSelection()
        } else {
          this.bandingList.forEach(item => {
            let index = this.checkList.findIndex(item1 => item1.deviceId == item.deviceId)
            if (index == -1) this.$refs.multipleTable.toggleRowSelection(item, false)
          })
        }
      })
    },
    handleSnCancel() {
      this.multipleSelection = []
      this.dialogSn = false
    },
    // 删除设备
    handleSnDeleteClick(row, index) {
      this.checkList.splice(index, 1)
      let selectIndex = this.multipleSelection.findIndex(select => select.deviceId == row.deviceId)
      if (selectIndex == -1) return
      this.$refs.multipleTable.toggleRowSelection(this.multipleSelection[selectIndex], false)
    },
    // 谷歌地图
    googleSearch(country, state, city, district, formatted_address, center) {
      this.formLabelAlign.country = [country]
      this.formLabelAlign.projectAddress = formatted_address
      this.formLabelAlign.projectLatitudex = center.lng
      this.formLabelAlign.projectLatitudey = center.lat
      this.formLabelAlign.address = []
      if (state) this.formLabelAlign.address.push(state)
      if (city) this.formLabelAlign.address.push(city)
      if (district) this.formLabelAlign.address.push(district)
      this.formatAreaFn()
    },
    formatAreaFn() {
      // 国家
      let isExistCountry = this.CityData.findIndex(item => this.formLabelAlign.country[0] == item.value)
      if (isExistCountry == -1) {
        this.CityData.push({
          label: this.formLabelAlign.country[0],
          value: this.formLabelAlign.country[0]
        })
      }

      // 省市区
      let isExistProvince = this.getAreaData.findIndex(item => this.formLabelAlign.address[0] == item.label)
      if (isExistProvince !== -1) {
        if (!this.formLabelAlign.address[1]) return
        let isExistCity = this.getAreaData[isExistProvince].children.findIndex(item => this.formLabelAlign.address[1] == item.label)
        if (isExistCity !== -1) {
          if (!this.formLabelAlign.address[2]) return
          let isExistDistrict = this.getAreaData[isExistProvince].children[isExistCity].children.findIndex(item => this.formLabelAlign.address[2] == item.label)
          if (isExistDistrict == -1) {
            this.getAreaData[isExistProvince].children[isExistCity].children.push({
              value: this.formLabelAlign.address[2],
              label: this.formLabelAlign.address[2],
            })
          }
        } else {
          this.getAreaData[isExistProvince].children.push({
            label: this.formLabelAlign.address[1],
            value: this.formLabelAlign.address[1],
            children: this.formLabelAlign.address[2] ? [{
              value: this.formLabelAlign.address[2],
              label: this.formLabelAlign.address[2],
            }] : undefined
          })
        }
      } else {
        this.getAreaData.push({
          value: this.formLabelAlign.address[0],
          label: this.formLabelAlign.address[0],
          children: this.formLabelAlign.address[1] ? [{
            value: this.formLabelAlign.address[1],
            label: this.formLabelAlign.address[1],
            children: this.formLabelAlign.address[2] ? [{
              value: this.formLabelAlign.address[2],
              label: this.formLabelAlign.address[2],
            }] : undefined
          }] : undefined
        })
      }
    },
    // 获取时区
    getTimeOptions() {
      timeList({
        pageNum: 1,
        pageSize: 100
      }).then(res => {
        let data = res.rows
        this.timeOptions = data
      })
    },
    // 高德地图搜索
    ampSearch(e) {
      this.formLabelAlign.country = ['中国']
      this.formLabelAlign.projectLatitudex = e.position[0]
      this.formLabelAlign.projectLatitudey = e.position[1]
      this.formLabelAlign.address = []
      this.zoom = 14
      this.center = e.position
      new AMap.Geocoder().getAddress(e.position, (status, result) => {
        // console.log(result, e.address, this.formLabelAlign.address);
        if (status === 'complete' && result.regeocode) {
          this.formLabelAlign.projectAddress = e.address ? e.address : result.regeocode.formattedAddress
          this.formLabelAlign.address = [result.regeocode.addressComponent.province, result.regeocode.addressComponent.city, result.regeocode.addressComponent.district]
        } else {
          console.log.error('根据经纬度查询地址失败')
        }
      })
    },
    // 获取货币
    getCurrencyOptions() {
      allCurrency().then(res => {
        this.currencyOptions = res.data
      })
    },
    /**
     * 分配设备
     */
    handleAllotClick(row) {
      this.allotForm.projectId = row.projectId
      this.dialogAllotVisible = true
    },
    handleCancelAllotClick() {
      this.dialogAllotVisible = false
      this.allotForm.userId = ''
    },
    handleConfirmAllot(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          allotDevice({
            projectId: this.allotForm.projectId,
            userId: this.allotForm.userId
          }).then(response => {
            if (response.code !== 200) return this.$message({
              type: 'error',
              message: this.$t('分配失败')
            });
            this.$message({
              type: 'success',
              message: this.$t('分配成功')
            })
            this.getList()
            this.dialogAllotVisible = false
          })
        }
      });
    },
    // 获取用户
    async listUserFn() {
      const res = await listUser({
        pageNum: 1,
        pageSize: 9999,
        deptId: this.userInfo.deptId
      })
      this.userList = res.rows.filter(item => item.deptId !== this.userInfo.deptId)
    },
    getDeviceTypeFn(type, needGroup) {
      return getDeviceType(type, needGroup)
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    },
    nodeClick(id) {
      this.queryInfo.deptId = id
      this.getList()
    },
    handleTreeExpand() {
      this.treeExpand = !this.treeExpand
    }
  },
}
</script>

<style lang="scss" scoped>
.el-tag:nth-child(1) {
  margin-right: 10px !important;
}

::v-deep .el-dialog__body {
  padding-right: 10px !important;
}

.dialog-add-wrapper {
  height: 700px;
  overflow: auto;
  padding-right: 20px;

  .map_box {
    position: relative;
    height: 300px;
    width: 100%;

    .map-search {
      position: absolute;
      top: 0;
      left: 0;
      width: 200px;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      border-radius: 4px;
      /* border: 1px solid #e4e7ec; */
      overflow: hidden;
      margin-right: 10px;

      input {
        width: 162px;
        height: 36px;
        padding-left: 15px;
        box-sizing: border-box;
        outline: none;
        border: none;
      }
    }

    .bm_local {
      position: absolute;
      top: 56px;
      left: 0;
      z-index: 9999;
    }
  }

  .line {
    height: 1px;
    width: 100%;
    margin: 24px 0;
    position: relative;
    border-bottom: 1px dashed #DCDFE6;
  }

  .coordinate_box {
    display: flex;
    width: 100%;

    .longitude,
    .latitude {
      flex: 1;

      .el-input {
        width: calc(100% - 50px)
      }

      span {
        width: 50px;
        display: inline-block;
        text-align: center;
      }

      el-input {
        background-color: red;
      }
    }
  }

  .divider {
    border: 1px solid #DCDFE6;
    height: 44px;

    span {
      line-height: 44px;
      font-size: 16px;
      color: #1890ff;
      padding-left: 15px;
    }
  }

  .map-container {
    height: 300px;
    width: 100%;

    .map {
      height: 300px;
      width: 100%;
    }
  }

}

::v-deep .anchorBL {
  display: none;
}
</style>
