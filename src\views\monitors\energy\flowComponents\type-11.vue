<template>
  <div class="position_box" style="width: 675px;">
    <div class="flow_box">
      <!-- 电网 -->
      <div class="flow">
        <div class="dianzhan">
          <svg-icon icon-class="flow_ac" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_ac }" />
          <div class="flow-detail">
            <!-- <div>电网</div> -->
            <div>
              {{ $t('monitor.flowItem1') }}：<span>{{ flowData.power ? flowData.power : 0 }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <div class="dianchi">
          <svg-icon icon-class="flow_de" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_de }" />
        </div>
        <svg width="330px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 40 56 L40 100 Q40 110 50 110 L302 110" v-if="flowData.power < -1" />
          <!-- 反 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 302 110 L50 110 Q40 110 40 100  L40 56 " v-else-if="flowData.power > 1" />
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 302 110 L50 110 Q40 110 40 100  L40 56 " v-else />

          <defs>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stop-color="#436EE3" />
              <stop offset="100%" stop-color="#F1A121" />
            </linearGradient>
          </defs>
          <!-- 空心圆 -->
          <circle r="10" class="circle_1" v-if="showCircle('power')">
            <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('power')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
        </svg>
      </div>
      <!-- 负载 -->
      <div class="flow">
        <div class="diandeng">
           <svg-icon icon-class="flow_pe" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_pe }" />
          <div class="flow-detail" style="line-height: 22px;padding-top: 7px;padding-bottom: 4px;"
            v-if="flowData?.chargingPiles && flowData?.chargingPiles.length">
            <template v-if="flowData?.chargingPiles.length > 1">
              <div class="swiper-container" ref="swiperRef">
                <div class="swiper-wrapper">
                  <div class="swiper-slide" v-for="item in flowData?.chargingPiles" :key="item.dc">
                    <div>
                      {{ item.name }}
                      <div>
                        {{ `1#${$t('枪功率')}` }}：<span>{{ item.chargingPile_19006 }}</span>&nbsp;kW
                      </div>
                      <div>
                        {{ `2#${$t('枪功率')}` }}：<span>{{ item.chargingPile_19017 }}</span>&nbsp;kW
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else-if="flowData?.chargingPiles.length == 1">
              <div>
                {{ flowData?.chargingPiles[0].name }}
                <div>
                  {{ `1#${$t('枪功率')}` }}：<span>{{ flowData?.chargingPiles[0].chargingPile_19006 }}</span>&nbsp;kW
                </div>
                <div>
                  {{ `2#${$t('枪功率')}` }}：<span>{{ flowData?.chargingPiles[0].chargingPile_19017 }}</span>&nbsp;kW
                </div>
              </div>
            </template>
            <template v-else>
              <div>
                {{ $t('充电桩功率') }}：--&nbsp;kW
              </div>
            </template>
          </div>
          <div class="flow-detail" v-else>
            <div>
              {{ $t('充电桩功率') }}：--&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="330px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M40,50 L40,20 Q40 10 50 10  L302,10" v-if="flowData.pile < 0" />
          <!-- 反 -->
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M302,10 L50,10 Q40 10 40 20  L40 50" v-else-if="flowData.pile > 0" />
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M302,10 L50,10 Q40 10 40 20  L40 50" v-else />
          <defs>
            <linearGradient id="grad2" x1="0%" y1="100%" x2="100%" y2="0%" gradientUnits="userSpaceOnUse">
              <stop offset="0%" stop-color="#34BE76" />
              <stop offset="100%" stop-color="#F1A31C" />
            </linearGradient>
          </defs>
          <!-- 空心圆 -->
          <circle r="10" class="circle_1" v-if="showCircle('pile')">
            <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('pile')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
    <div class="flow_box">
      <!-- 光伏 -->
      <div class="flow">
        <div class="guangfu" style="padding-left: 0;">
          <div class="flow-detail" style="margin: 0;margin-right: 10px;padding-right: 3px">
            <!-- <div>光伏</div> -->
            <div>
              {{ $t('monitor.flowItem2') }}：<span>{{ flowData.photovoltaic ? flowData.photovoltaic : 0 }}</span>&nbsp;kW
            </div>
          </div>
          <svg-icon icon-class="flow_pv" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_pv }" />
        </div>
        <svg width="330px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M285,70 L285,100 Q285 110 275 110  L25,110" v-if="flowData.photovoltaic > 1" />
          <!-- 反 -->
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M25,110 L275,110 Q285 110 285 100   L285,70" v-else-if="flowData.photovoltaic < -1" />
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M25,110 L275,110 Q285 110 285 100   L285,70" v-else />
          <defs>
            <linearGradient id="grad2" x1="1" y1="0" x2="0" y2="1">
              <stop offset="0" stop-color="#14B9AE" />
              <stop offset="1" stop-color="#F4A21B" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('photovoltaic')">
            <animate attributeName="fill" values="#F4A21B;#14B9AE" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path3" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('photovoltaic')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path3" />
            </animateMotion>
          </circle>
        </svg>
      </div>
      <!-- 电池 -->
      <div class="flow">
        <div class="transformer1">
          <Bms :value="soc"></Bms>
          <div class="flow-detail" style="margin-right: 10px">
            <!-- <div>电池</div> -->
            <div>
              {{ $t('monitor.flowItem4') }}：<span>{{ flowData.cell }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="330px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-left: 5px;">
          <!-- 正 -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')"
            d="M280,60 L280,20 Q280 10 270 10 L20 10" v-if="flowData.cell > 1" />
          <!-- 反  -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')"
            d="M 20 10 L 270 10 Q 280 10 280 20 L 280 60" v-else-if="flowData.cell < -1" />
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')"
            d="M 20 10 L 270 10 Q 280 10 280 20 L 280 60" v-else />

          <defs>
            <linearGradient id="grad4" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#F76655" />
              <stop offset="100%" stop-color="#F7A11A" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('cell')">
            <animate attributeName="fill" values="#F7A11A;#F76655" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('cell')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import Bms from './bms.vue'
// import Swiper JS
import Swiper from 'swiper';
import 'swiper/swiper-bundle.css';
import SwiperCore, { Autoplay } from 'swiper/core';
// configure Swiper to use modules
SwiperCore.use([Autoplay]);

export default {
  components: { Bms },
  data() {
    return {
      swiper: null
    }
  },
  computed: {
    flowData() {
      let data = this.$store.state.monitor.flowData
      this.$nextTick(() => {
        this.initSwiper()
      })
      if (data.chargingPiles && data.chargingPiles.length) {
        data.pile = data.chargingPiles.reduce((pre, current) => pre + Number(current.chargingPile_19003), 0)
      } else {
        data.pile = null
      }
      return data
    },
    // 流动拓补图展示圆
    showCircle() {
      let control = this.$store.state.monitor.control
      let flowData = this.flowData
      return (name) => {
        if (!control) return false
        if (control.onLineState == '离线') return false
        if (-1 <= flowData[name] && flowData[name] <= 1) {
          return false
        } else if (flowData[name] == 0) {
          return false
        } else {
          return true
        }
      }
    },
    soc() {
      let bms = this.$store.state.monitor.pcs_bms
      let bmsBau = this.$store.state.monitor.pcs_bmsBau
      if (bms.length) return (bms.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bms.length).toFixed(1)
      else if (bmsBau.length) return (bmsBau.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bmsBau.length).toFixed(1)
      return 0
    }
  },
  mounted() {

  },
  methods: {
    initSwiper() {
      this.swiper = new Swiper('.swiper-container', {
        // Optional parameters
        // direction: 'horizontal',
        direction: 'vertical',
        loop: true,
        slidesPerView: 1,
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        },

        // Navigation arrows
        // navigation: {
        //   nextEl: '.swiper-button-next',
        //   prevEl: '.swiper-button-prev',
        // },
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.position_box {
  position: absolute;
  height: 240px;
  width: 615px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;


  .flow_box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .flow {
    flex: 1;
    position: relative;

    .dianzhan {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      left: -4px;
      border-radius: 6px;
      padding: 8px;
      display: flex;
      align-items: center;
    }

    .flow-detail {
      margin-left: 10px;
      padding: 0 10px;
      border: 1px solid #d6d6d6;
      border-radius: 10px;
      /* height: 35px; */
      display: flex;
      font-size: 14px;
      line-height: 35px;

      span {
        font-weight: 600;
      }
    }

    .dianchi {
      position: absolute;
      right: -36px;
      top: 81px;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      z-index: 1000;
    }

    .diandeng {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      left: -2px;
      bottom: 0px;
      display: flex;
      align-items: center;
    }

    .transformer1 {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      right: 15px;
      top: 43px;
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
    }

    .guangfu {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      right: 10px;
      top: 0;
      display: flex;
      align-items: center;
    }

    .img {
      width: 70px;
    }
  }
}

.swiper-container {
  width: 100%;
  height: 70px;

  .swiper-slide {
    display: flex;
    align-items: center;
  }

  .swiper-button-prev {
    left: 0px !important;
    top: 60%;
  }

  .swiper-button-prev::after {
    font-size: 20px;
  }

  .swiper-button-next {
    right: 0px !important;
    top: 60%;
  }

  .swiper-button-next::after {
    font-size: 20px;
  }
}
.svg-img {
  width: 70px;
  height: 70px;
}
</style>
