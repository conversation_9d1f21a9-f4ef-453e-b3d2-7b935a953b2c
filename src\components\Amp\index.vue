<template>
  <div style="width: 100%;height: 100%;">
    <el-amap class="amap-box" :vid="'amap-vue'" @click="mapClick" @init="initMap" :center="center" :zoom="zoom">
      <!-- 放大缩小 -->
      <el-amap-control-tool-bar position="RT"></el-amap-control-tool-bar>
      <!-- 搜索 -->
      <el-amap-search-box :placeholder="$t(`project['Please enter full address']`)" :debounce="1000" @select="selectPoi"
        :visible="searchVisible"></el-amap-search-box>
      <!-- 定位 -->
      <el-amap-control-geolocation @complete="getLocation" :getCityWhenFail="true" :needAddress="true"
        :visible="searchVisible"></el-amap-control-geolocation>
      <!-- 点标记 -->
      <el-amap-marker v-for="(marker, index) in amapMarkers" :key="index" :position="marker.position"
        @click="(e) => { clickArrayMarker(marker, e) }">
        <svg-icon icon-class="marker" class-name="icon" :style="{ color: $store.state.common.theme['--primary-color'], width: '25px', height: '32px' }" />
      </el-amap-marker>
      <!-- 信息窗体 -->
      <el-amap-info-window v-if="windowVisible" :position="windowPosition" :visible.sync="windowVisible"
        :closeWhenClickMap="true" anchor="top-left" :autoMove="true">
        <div style="width: 300px">
          <div class="detail-row"><span class="detail-title">{{ $t('project.name') }}</span>：<el-button type="text"
              class="detail-row" @click="go2Detail">{{ windowContent.projectName }}</el-button></div>
          <div class="detail-row"><span class="detail-title">{{ $t('创建人员') }}</span>：{{ windowContent.nickName }}</div>
          <div class="detail-row"><span class="detail-title">{{ $t('home.pieRadioText2') }}</span>：<el-button
              type="text" class="detail-row" @click="go2Detail">{{ windowContent.countDevice }}</el-button> {{ $t('台')
            }}</div>
          <div class="detail-row"><span class="detail-title">{{ $t('所属国家') }}</span>：{{ windowContent.country }}</div>
          <div class="detail-row"><span class="detail-title">{{ $t('project.address') }}</span>：{{
            windowContent.projectAddress }}</div>
          <div class="detail-row"><span class="detail-title">{{ $t(`common['时区地址']`) }}</span>：{{
            windowContent[getPropFn] }}</div>
        </div>
      </el-amap-info-window>
    </el-amap>
  </div>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance } from 'vue'
import { projectAll } from '@/api/property/device'
import Cookies from 'js-cookie'

const { proxy } = getCurrentInstance()
const props = defineProps({
  center: {
    type: Array,
    default: () => {
      return [113.933142, 22.636963]
    }
  },
  zoom: {
    type: Number,
    default: 12
  },
  markers: {
    type: Array,
    default: () => {
      return [
        // {
        //   position: [121.5273285, 31.21515044],
        //   id: 1
        // }
      ]
    }
  },
  searchVisible: {
    type: Boolean,
    default: true
  }
})
const emits = defineEmits(['search'])

// const localMarkers = ref([...props.markers])
// watch(() => props.markers, () => localMarkers.value = props.markers)
// const amapMarkers = computed({
//   get() {
//     return localMarkers.value
//   },
//   set(value) {
//     localMarkers.value = value
//   }
// })

const amapMarkers = ref()
const projectAllFn = async () => {
  const res = await projectAll()
  amapMarkers.value = res.data.map((item => {
    item.position = [item.projectLatitudex, item.projectLatitudey]
    item.id = item.projectId
    return item
  }))
}
projectAllFn()


const mapInstance = ref(null)
const initMap = (map) => {
  let lang = Cookies.get('language')
  if (lang == 'zh') {
    map.setLang('zh')
  } else {
    map.setLang('en')
  }
  mapInstance.value = map
}
/**
 *
 * 点击地图
 */
const mapClick = (e) => {
  // console.log(e);
  if (!props.searchVisible) return
  amapMarkers.value = [{
    position: [e.lnglat.lng, e.lnglat.lat],
    id: 1
  }]
  emits('search', {
    position: [e.lnglat.lng, e.lnglat.lat]
  })
}
/**
 * 搜索
 */
const selectPoi = (e) => {
  // console.log('selectPoi: ', e);
  emits('search', {
    address: `${e.poi.district}${e.poi.address}${e.poi.name}`,
    position: [e.poi.location.lng, e.poi.location.lat]
  })
}
/**
 * 定位
 */
const getLocation = (e) => {
  // console.log('getLocation: ', e)
  amapMarkers.value = [{
    position: [e.position.lng, e.position.lat],
    id: 1
  }]
  emits('search', {
    position: [e.position.lng, e.position.lat]
  })
}
/**
 *
 * 点击标点
 */
const clickArrayMarker = (marker, e) => {
  // console.log(marker, e)
  emits('search', {
    position: [marker.position[0], marker.position[1]]
  })
  if (!props.searchVisible) {
    // mapInstance.value.setZoom(marker.country !== '中国' ? 3 : 14)
    windowContent.value = marker
    windowPosition.value = marker.position
    windowVisible.value = true
  }
}
/**
 * 信息窗体
 */
const windowVisible = ref(false)
const windowPosition = ref([])
const windowContent = ref()
const go2Detail = () => {
  proxy.$router.push({
    path: "../../monitors/opticalstorage?name=" + windowContent.value.projectName,
  });
}
const getPropFn = computed(() => {
  let lang = proxy.$store.getters.language
  switch (lang) {
    case 'zh':
      return 'timeZoneAddress'
    case 'en':
      return 'timeZoneAddressUs'
    case 'it':
      return 'timeZoneAddressIt'
  }
})
</script>

<style lang="scss" scoped>
.detail-row {
  padding: 3px 0;
}

.detail-title {
  font-weight: 600;
}

:deep(.amap-container) {
  border-radius: 10px !important;
}
</style>
