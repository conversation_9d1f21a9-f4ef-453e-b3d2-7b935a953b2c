<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-17 15:03:38
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-19 15:37:48
 * @FilePath: \办公文档\代码\新建文件夹\src\views\system\index\index_1.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="dashboard-editor-container">
    <panel-group />
    <div class="map_box">
      <div class="cente_box_1 elevation-4">
        <div class="map-container" v-if="isInChina == 1">
          <AMP :markers="markers" :center="center" :zoom="zoom" :searchVisible="false" @search="ampSearch"></AMP>
        </div>
        <GMP :isSearch="false" v-if="isInChina == 2"></GMP>
      </div>
      <div class="cente_box_2 elevation-4">
        <div class="table_box">
          <div class="echarts-header">
            <span class="text_class">{{ $t('home.tableTitle') }}</span>
            <div>
              <div class="input_ment">
                <el-input :placeholder="$t(`project['Please enter project name']`)" style="width: 210px;"
                  v-model="queryInfo.projectName" clearable></el-input>
              </div>
              <div class="input_ment">
                <el-button type="primary" @click="search()" icon="el-icon-search">{{ $t('common.search') }}</el-button>
              </div>
            </div>
          </div>
          <div class="table_div">
            <div style="height:calc(100% - 42px)">
              <el-table ref="table" :data="tableData" height="100%" max-height="100%" style="width: 100%;">
                <el-table-column type="index" label="#" width="60" align="center" />
                <el-table-column prop="projectName" :label="$t('project.name')" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span class="goDetail" @click="go2Detail(scope.row)">
                      {{ scope.row.projectName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="projectAddress" :label="$t('project.address')" show-overflow-tooltip
                  align="center" />
                <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip
                  align="center" />
                <el-table-column prop="projectState" :label="$t('common.status')" show-overflow-tooltip width="90"
                  align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.projectState == 1">{{ $t('common.Enable') }}</el-tag>
                    <el-tag v-else type="danger">{{ $t('common.Deactivate') }}</el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum"
              :limit.sync="queryInfo.pageSize" @pagination="getList" layout="prev, pager, next, jumper"
              style="margin-top: 10px;text-align: right;" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { BaiduMap, BmMapType } from 'vue-baidu-map'
import PanelGroup from '../../dashboard/PanelGroup'
import PieChart_2 from '../../dashboard/PieChart_2'
import GMP from '@/components/Gmp/index.vue'
import AMP from '@/components/Amp/index.vue'

import { projectList } from '@/api/property/device'
import Cookies from 'js-cookie'

export default {
  name: 'Index',
  data() {
    return {
      radio1: this.$t('home.pieRadio2'),
      total: 0,
      // center: { lng: 113.939775, lat: 22.6426 },
      center: [113.933142, 22.636963],
      zoom: 3,
      markers: [],
      searchText: '',
      form: {
        address: '',
        addrPoint: '',
      },
      table_key: 1,
      tableData: [],
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      isInChina: 0, // 默认设置为非国内
    }
  },
  components: {
    PanelGroup,
    PieChart_2,
    BaiduMap,
    GMP,
    BmMapType,
    AMP
  },
  mounted() {
    this.checkLocation();
  },
  methods: {
    checkLocation() {
      this.$store.dispatch('common/getIpInfo').then(res => {
        if (res.countryCode === 'CN') {
          this.isInChina = 1;
        } else {
          this.isInChina = 2;
        }
        this.getList()
      })
    },
    getList() {
      projectList(this.queryInfo).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        this.tableData = res.rows
        this.total = res.total

        this.markers = this.tableData.map((item => {
          item.position = [item.projectLatitudex, item.projectLatitudey]
          item.id = item.projectId
          return item
        }))

        // console.log(this.markers)
      })
    },
    querySearchAsync(str, cb) {
      var options = {
        onSearchComplete: function (res) {
          var s = [];
          if (local.getStatus() == BMAP_STATUS_SUCCESS) {
            for (var i = 0; i < res.getCurrentNumPois(); i++) {
              s.push(res.getPoi(i));
            }
            cb(s)
          } else {
            cb(s)
          }
        }
      }
      var local = new BMap.LocalSearch(this.map, options)
      local.search(str)
    },
    //点击搜索框地址
    handleSelect(item) {
      this.center = item.point
    },
    //点击项目名跳转
    go2Detail(row) {
      this.$router.push({
        path: "../../monitors/opticalstorage?name=" + row.projectName,
      });
    },
    initMap({ BMap, map }) {
      this.BMap = BMap;
      this.map = map
      this.zoom = 4;
      this.map.removeEventListener("click");
      const markers = [];
      projectList(this.queryInfo).then(res => {
        this.tableData = res.rows
        this.total = res.total

        if (this.tableData.length == 0) {
          // this.geolocate()
          let ip = JSON.parse(Cookies.get('ip'))
          if (ip.ip) {
            this.center = {
              lat: ip.latitude,
              lng: ip.longitude,
            };
          }
        } else {
          this.center.lng = this.tableData[0].projectLatitudex
          this.center.lat = this.tableData[0].projectLatitudey
        }
        this.tableData.forEach(marker => {
          var point = new BMap.Marker(new BMap.Point(marker.projectLatitudex, marker.projectLatitudey));
          // 在地图上添加点标记
          const markerObj = new BMap.Marker({ position: point, content: marker.projectAddress });
          var opts = {
            width: 250,
            height: 80,
            title: '',
            enableMessage: true
          }
          map.addOverlay(point);
          point.addEventListener('click', () => {
            var content = marker.projectAddress + "<div><a href='../../monitors/opticalstorage?name=" + marker.projectName + "' style='color:#1890FF'>详情</a></div>";
            var infoWindow = new BMap.InfoWindow(content, opts);
            map.openInfoWindow(infoWindow, point.getPosition());
          });
          markers.push(point);
        });
      })
    },
    geolocate() {
      let vm = this

      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position) => {
          if (position && position.coords && position.coords.latitude) {
            // alert("获取地理位置："+position.coords.latitude+","+position.coords.longitude)
            vm.hasSetPin = true
            vm.zoom = 10
            vm.center = {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            };
            vm.center_ = vm.center
            vm.markers.push({ position: vm.center });
          }
        }, (error) => {  // html5 默认调用的谷歌的接口，会有安全限制
          switch (error.code) {
            case error.PERMISSION_DENIED: // 许可拒绝,用户选了不允许
              alert("您拒绝对获取地理位置的请求")
              alert(error.message);
              break;
            case error.POSITION_UNAVAILABLE: // 连不上GPS卫星，或者网络断了
              alert("位置信息是不可用的");
              alert(error.message);
              break;
            case error.TIMEOUT:  // /超时了
              alert("请求您的地理位置超时");
              alert(error.message);
              break;
            case error.UNKNOWN_ERROR:
              alert("未知错误");
              alert(error.message);
              break;
          }
        });
      } else {
        alert("未获取获取到地理位置");
        vm.markers.push({ position: vm.center });
      }
    },
    search() {
      this.getList()
    },
    // 高德地图搜索
    ampSearch(e) {
      this.center = e.position
    }
  },


}
</script>

<style lang="scss" scoped>
.el-table {
  width: 99.9% !important;
}

.autoAddressClass {
  li {
    i.el-icon-search {
      margin-top: 11px;
    }

    .mgr10 {
      margin-right: 10px;
    }

    .title {
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .address {
      line-height: 1;
      font-size: 12px;
      color: #b4b4b4;
      margin-bottom: 5px;
    }
  }
}

.dashboard-editor-container {
  // padding: 12px;
  /* background-color: #f7f7f7; */
  position: relative;
  /* height: calc(100vh - 126px); */
  height: 100%;
  display: flex;
  flex-direction: column;
  /* padding: 20px; */

  .map-container {
    position: relative;
    height: 100%;

    .map-btn {
      position: absolute;
      top: 0;
      left: 0;

    }
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 20px;
    height: 330px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
@media (max-width: 1500px) {
  .dashboard-editor-container {
    height: auto !important
  }
  .map_box {
    height: 900px !important;
  }
}

.map_box {
  position: relative;
  display: flex;
  height: calc(100% - 170px);

  .cente_box_1 {
    width: 50%;
    border-radius: 10px;
    margin-right: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

    .map {
      height: 100%;
      width: 100%;
      border-radius: 10px;
      overflow: hidden;
    }

  }

  .cente_box_2 {
    width: 50%;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

    .text_class {
      font-weight: 600;
    }

    .echarts_box {
      /* flex: 1; */
      height: 50%;
      background: white;
      border-radius: 12px;
      padding: 12px;
      position: relative;
    }

    .echarts-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px
    }

    .table_box {
      /* flex: 1; */
      height: 100%;
      background-color: white;
      padding: 12px;
      border-radius: 12px;
      /* margin-top: 20px; */

      .goDetail {
        color: var(--primary-color);
        cursor: pointer;
        width: 100%;
        padding: 0 16px 0 16px;
        line-height: 40px;
        text-align: center;
      }

      .input_ment {
        display: inline-block;
        margin-right: 20px;
        font-size: 14px
      }

      .input_ment:last-child {
        margin: 0
      }

      .table_div {
        margin-top: 20px;
        height: calc(100% - 60px)
      }
    }
  }
}

::v-deep .anchorBL {
  display: none;
}

::v-deep .el-table__body-wrapper .is-scrolling-none {
  height: 200px
}
</style>