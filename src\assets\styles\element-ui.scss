// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  border-radius: 20px !important;
}

.el-dialog__header {
  text-align: left;
  border-top-right-radius: 20px !important;
  border-top-left-radius: 20px !important;
  border-bottom: 1px solid rgba(46, 38, 61, 0.12);
}

.el-dialog__footer {
  border-top: 1px solid rgba(46, 38, 61, 0.12);
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-submenu__icon-arrow {
  right: 10px !important;
}

.el-table th.el-table__cell>.cell {
  color: var(--base-color);
  font-size: 14px;
  font-weight: 500;
}

.el-table .el-table__cell {
  color: var(--base-color);
  font-size: 14px;
  // font-weight: 500;
}

.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
  font-size: 14px !important;
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
  // border-bottom: thin solid rgba(46, 38, 61, 0.12) !important;
  border-color: rgba(46, 38, 61, 0.12) !important
}

.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-bottom: thin solid rgba(46, 38, 61, 0.12) !important;

}

.desc-top {
  width: 0 !important;
  padding: 0 !important;
  border: 0 !important;
  // font-size: 16px !important;
}

.cont-top {
  border-bottom: none !important;
  text-align: center !important;
  // font-size: 16px !important;
  color: var(--base-color) !important;
  font-weight: bold !important;
}

.desc-bot {
  text-align: right !important;
  width: 16% !important;
  // font-size: 16px !important;
  background-color: #fbfbfb !important;
  color: var(--base-color) !important;
}

.cont-bot {
  width: 17.34% !important;
  // font-size: 16px !important;
  color: var(--base-color) !important;
}

.el-input-number {
  width: 80% !important;

  // .el-input-number__decrease {
  //   display: none
  // }

  // .el-input-number__increase {
  //   display: none
  // }

  // .el-input__inner {
  //   padding-left: 15px;
  //   padding-right: 15px;
  //   text-align: left;
  // }
}

.input-number {
  .el-input-number__decrease {
    display: none
  }

  .el-input-number__increase {
    display: none
  }

  .el-input__inner {
    padding-left: 15px !important;
    padding-right: 15px !important;
    text-align: left !important;
  }
}


.el-form-item__label {
  text-align:left;
  float: none;
  word-break: break-word;
}

.el-date-editor .el-range-separator {
  width: 15%;
}

:root {
  border-bottom-color: rgba(118, 118, 118, 0.6);
  border-left-color: rgba(118, 118, 118, 0.6);
  border-right-color: rgba(118, 118, 118, 0.6);
  border-top-color: rgba(118, 118, 118, 0.6);
}

.el-input, .el-button, .el-dialog, .el-input-number, .el-breadcrumb, .el-tag, .el-select, .el-dropdown-menu, .el-popper, .el-form, .el-tree {
  font-family: Open Sans, Inter, sans-serif, -apple-system, blinkmacsystemfont, Segoe UI, roboto, Helvetica Neue, arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol !important;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 针对整个表格的容器 */
.el-table ::-webkit-scrollbar {
  width: 10px; /* 滚动条宽度 */
  height: 10px; /* 滚动条高度 */
}
/* 滚动条轨道 */
.el-table ::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道颜色 */
}
/* 滚动条滑块 */
.el-table ::-webkit-scrollbar-thumb {
  background: #c0c0c0; /* 滑块颜色 */
  border-radius: 10px; /* 滑块圆角 */
}
/* 滑块悬停时的颜色 */
.el-table ::-webkit-scrollbar-thumb:hover {
  background: #c0c0c0; /* 滑块悬停颜色 */
}


.el-card {
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, .2), 0 6px 10px 0 rgba(0, 0, 0, .14), 0 1px 18px 0 rgba(0, 0, 0, .12) !important;
}
