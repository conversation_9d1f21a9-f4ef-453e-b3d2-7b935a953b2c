<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-16 09:37:31
 * @LastEditors: shengri1990 <EMAIL>
 * @LastEditTime: 2023-10-18 11:41:14
 * @FilePath: \办公文档\代码\新建文件夹\src\components\RuoYi\Git\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <svg-icon icon-class="github" @click="goto" />
  </div>
</template>

<script>
export default {
  name: 'RuoYiGit',
  data() {
    return {
      url: ''
    }
  },
  methods: {
    goto() {
      window.open(this.url)
    }
  }
}
</script>