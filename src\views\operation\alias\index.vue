<template>
  <div class="box">
    <div class="input_box">
      <!-- <div class="header-title">
        {{ '测点别名' }}
      </div> -->
      <div>
        <div class="input_ment">
          <el-select v-model="queryInfo.type" clearable :placeholder="$t('common.select')"
            @change="handleSearchClick()">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option :label="$t('外设')" :value="0" />
            <el-option :label="$t('电表')" :value="1" />
          </el-select>
        </div>
        <div class="input_ment">
          <el-input :placeholder="$t('测点编号/测点值编号')" style="width: 200px;" v-model="queryInfo.point"
            clearable></el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
          }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{ $t('common.add') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="type" :label="$t('测点类型')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag type="primary">{{ scope.row.type == 0 ? $t('外设') : $t('电表') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="mode" :label="$t('外设类型')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.type == 0">{{ scope.row.mode == 0 ? $t('默认') : 'STS' }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="point" :label="$t('测点编号')" show-overflow-tooltip align="center" />
        <el-table-column prop="alias" :label="$t('测点别名')" show-overflow-tooltip align="center" />
        <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleEditClick(scope.row)">{{
                $t('common.edit') }}</el-button>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleDeleteClick(scope.row)">{{
                $t('common.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>

    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" :width="$convertPx(600, 'rem')"
      :title="dialogTitle">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
        <el-form-item :label="$t('测点类型')" prop="type">
          <el-radio-group v-model="form.type" style="width: 100%" :disabled="dialogTitle == $t('修改别名')" @input="handleTypeChange">
            <el-radio :label="0">{{ $t('外设') }}</el-radio>
            <el-radio :label="1">{{ $t('电表') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('电表取值类型')" prop="electricQuantityPoint" v-if="form.type == 1">
          <el-radio-group v-model="form.electricQuantityPoint" style="width: 100%" :disabled="dialogTitle == $t('修改别名')" @input="handleTypeChange">
            <el-radio :label="0">{{ $t('正向') }}</el-radio>
            <el-radio :label="1">{{ $t('反向') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('外设类型')" prop="mode" v-if="form.type == 0">
          <el-radio-group v-model="form.mode" style="width: 100%">
            <el-radio :label="0">{{ $t('默认') }}</el-radio>
            <el-radio :label="1">{{ 'STS' }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="pointTitle" prop="point">
          <el-input v-model="form.point" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="aliasTitle" prop="alias">
          <el-input v-model="form.alias" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="`${aliasTitle}(EN)`" prop="aliasEn">
          <el-input v-model="form.aliasEn" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="`${aliasTitle}(IT)`" prop="aliasIt">
          <el-input v-model="form.aliasIt" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('formRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick } from 'vue'
import { aliasList, addAlias, editAlias, deleteAlias } from '@/api/operation/alias'

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const queryInfo = ref({
  pageNum: 1,
  pageSize: 10
})

// 获取列表
const isEdit = ref(false)
const getList = () => {
  // if (isEdit.value) queryInfo.value.pageNum = 1
  loading.value = true
  aliasList(queryInfo.value).then(res => {
    let data = res.rows
    data.forEach(item => {
      if (item.type == 0) {
        item.pointTitle = proxy.$t('测点编号')
        item.aliasTitle = proxy.$t('测点别名')
      } else if (item.type == 1) {
        item.pointTitle = proxy.$t('测点值')
        item.aliasTitle = proxy.$t('测点值别名')
      }
    })
    // if (isEdit.value) {
    tableData.value = data
    // } else {
    //   tableData.value = [...tableData.value, ...data]
    // }
    total.value = res.total
    loading.value = false
  });
}
getList()
//搜索按键
const handleSearchClick = () => {
  isEdit.value = true
  getList()
}

// 表单
const dialogVisible = ref(false)
const dialogTitle = ref(proxy.$t('添加别名'))
const form = ref({
  point: undefined,
  alias: undefined,
  aliasEn: undefined,
  aliasIt: undefined,
  type: 0,
  mode: 0,
  electricQuantityPoint: 0
})
const rules = ref({
  point: [
    { required: true, message: proxy.$t(`common['Please enter']`), trigger: 'blur' }
  ],
  alias: [
    { required: true, message: proxy.$t(`common['Please enter']`), trigger: 'blur' }
  ],
  aliasEn: [
    { required: true, message: proxy.$t(`common['Please enter']`), trigger: 'blur' }
  ],
  aliasIt: [
    { required: true, message: proxy.$t(`common['Please enter']`), trigger: 'blur' }
  ],
  type: [
    { required: true, message: proxy.$t('请选择测点类型'), trigger: 'blur' }
  ],
  mode: [
    { required: true, message: proxy.$t('请选择外设类型'), trigger: 'blur' }
  ],
  electricQuantityPoint: [
    { required: true, message: proxy.$t('common.select'), trigger: 'blur' }
  ]
})
const pointTitle = ref(proxy.$t('测点编号'))
const aliasTitle = ref(proxy.$t('测点别名'))
const handleTypeChange = (e) => {
  if (e == 0) {
    pointTitle.value = proxy.$t('测点编号')
    aliasTitle.value = proxy.$t('测点别名')
    form.value.mode = 0
  } else if (e == 1) {
    pointTitle.value = proxy.$t('测点值')
    aliasTitle.value = proxy.$t('测点值别名')
    form.value.mode = 0
  }
}
const handleConfirm = (formName) => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      if (dialogTitle.value == proxy.$t('添加别名')) { // 添加
        addAliasFn()
      } else { // 修改
        editAliasFn()
      }
    }
  });
}
// 添加
const handleAddClick = () => {
  dialogTitle.value = proxy.$t('添加别名')
  dialogVisible.value = true;
  form.value = {
    point: undefined,
    alias: undefined,
    aliasEn: undefined,
    aliasIt: undefined,
    type: 0,
    mode: 0,
    electricQuantityPoint: 0
  }
  if (form.value.type == 0) {
    pointTitle.value = proxy.$t('测点编号')
    aliasTitle.value = proxy.$t('测点别名')
  } else if (e == 1) {
    pointTitle.value = proxy.$t('测点值')
    aliasTitle.value = proxy.$t('测点值别名')
  }
  nextTick(() => {
    proxy.resetForm('form')
  })
}
// 修改
const handleEditClick = (row) => {
  dialogTitle.value = proxy.$t('修改别名')
  dialogVisible.value = true;
  form.value = {
    ...row
  }
}
const addAliasFn = () => {
  addAlias({
    point: form.value.point,
    alias: form.value.alias,
    aliasEn: form.value.aliasEn,
    aliasIt: form.value.aliasIt,
    type: form.value.type,
    mode: form.value.mode
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Addition Failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Added successfully']`)
    })
    isEdit.value = true
    getList()
    dialogVisible.value = false
  })
}
const editAliasFn = () => {
  editAlias({
    id: form.value.id,
    point: form.value.point,
    alias: form.value.alias,
    aliasEn: form.value.aliasEn,
    aliasIt: form.value.aliasIt,
    type: form.value.type,
    mode: form.value.mode
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Change failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Modify successfully']`)
    })
    isEdit.value = true
    getList()
    dialogVisible.value = false
  })
}
// 删除
const handleDeleteClick = (row) => {
  proxy.$confirm(proxy.$t(`menu['Are you sure to delete the data item?']`), proxy.$t('common.systemPrompt'), {
    confirmButtonText: proxy.$t('common.confirm'),
    cancelButtonText: proxy.$t('common.cancel'),
    type: 'warning'
  }).then(() => {
    deleteAlias({
      id: row.id
    }).then(res => {
      if (res.code !== 200) return proxy.$message({
        type: 'error',
        message: proxy.$t(`common['Deleted Failed']`)
      });
      isEdit.value = true
      getList()
      proxy.$message({
        type: 'success',
        message: proxy.$t(`common['Deleted successfully']`)
      });
    })
  }).catch(() => {
    proxy.$message({
      type: 'info',
      message: proxy.$t(`common['Deletion Cancelled']`)
    });
  });
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  /* max-height: 100%; */
  overflow-y: auto !important;
  overflow-x: hidden !important;

  .input_box {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }

  .card-cont {
    display: flex;
    flex-wrap: wrap;
  }

  .cont-item {
    margin-bottom: 20px;
    flex: 0 0 auto;
  }
}
</style>
