<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-11 10:19:07
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="param-box">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" style="overflow: auto;">
      <el-form-item :label="`${$t(`param['下发状态']`)}`" prop="status" style="width: 50%">
        <span slot="label">
          {{ $t(`param['下发状态']`) }}
          <el-tooltip class="item" effect="dark" placement="bottom">
            <div slot="content" style="line-height: 1.5">
              &emsp;{{ $t(`param['未下发']`) }}：{{ $t(`param['该类参数从未下发']`) }};
              <br />
              &emsp;{{ $t(`param['下发中']`) }}：{{ $t(`param['参数已成功下发至设备，执行未知，请等待']`) }};
              <br />
              {{ $t(`param['下发成功']`) }}：{{ $t(`param['参数已成功下发至设备并已执行成功']`) }};
              <br />
              {{ $t(`param['下发失败']`) }}：{{ $t(`param['参数已成功下发至设备，设备并未执行成功']`) }}
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-link :underline="false" v-if="form.status == 0">{{ $t(`param['未下发']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 1" type="success">{{ $t(`param['下发成功']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 2" type="primary">{{ $t(`param['下发中']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 3" type="danger">{{ $t(`param['下发失败']`) }}</el-link>
      </el-form-item>
      <el-form-item :label="$t(`param['运行模式']`)" prop="setting1900" style="width: 50%">
        <el-select ref="elSelect" v-model="form.setting1900" :placeholder="$t('common.select')"
          @change="handleOperationChange" style="width: 100%;">
          <el-option v-for="item in operationOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t(`param['备电方案']`)}`" prop="domainName" v-if="form.setting1900 == 2" style="width: 50%">
        <div style="display: flex">
          <el-select ref="elSelect" v-model="form.reservePatternId" :placeholder="$t('common.select')"
            style="width: 100%">
            <el-option v-for="item in backupOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-button v-if="form.reservePatternId" :style="{ marginLeft: $convertPx(10, 'rem') }"
            @click="handleLookClick('backup')">{{ $t('common.check') }}</el-button>
        </div>
      </el-form-item>
      <!-- <div style="width: 100%;display: flex;" v-if="form.setting1900 == 1 || isShowTestJfpg"> -->
      <el-form-item :label="`${$t(`执行周期`)}`" prop="setting1924" style="width: 50%"
        v-if="!_.isEmpty(form.setting1924) && form.setting1900 == 1">
        <span slot="label">
          {{ $t(`执行周期`) }}
          <el-tooltip class="item" effect="dark" placement="bottom">
            <div slot="content" style="line-height: 1.5">
              {{ $t(`date.day`) }}：{{ $t(`每天；`) }}
              <br />
              {{ $t(`date.week`) }}：{{ $t(`每周（1~7天）；`) }}
              <br />
              {{ $t(`date.month`) }}：{{ $t(`1~12个月。`) }}
              {{ $t(`注：该执行周期只适用于削峰填谷。`) }}
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-radio-group v-model="form.setting1924" style="width: 100%">
          <el-radio label="0">{{ $t(`common.day`) }}</el-radio>
          <el-radio label="1">{{ $t(`common.week`) }}</el-radio>
          <el-radio label="2">{{ $t(`common.month`) }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <div style="width: 100%;display: flex;" v-if="form.setting1900 == 1">
        <el-form-item :label="`${$t(`param['削峰填谷']`)}`" prop="cutTopId" style="width: 50%">
          <span slot="label">
            {{ $t(`param['削峰填谷']`) }}
            <el-tooltip class="item" effect="dark" :content="$t('按照设置时间段给系统充放电。')" placement="bottom">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <div style="display: flex" v-if="isJFPGType == 0">
            <el-select ref="elSelect" v-model="form.cutTopId" :placeholder="$t('common.select')" style="width: 100%">
              <el-option v-for="item in jfpgOptions" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
            <el-button v-if="form.cutTopId" :style="{ marginLeft: $convertPx(10, 'rem') }"
              @click="handleLookClick('jfpg')">{{ $t('common.check') }}</el-button>
          </div>
          <template v-else-if="isJFPGType == 1">
            <el-form-item :label="item.label" :prop="'weeks.' + index + '.cutTopId'" v-for="(item, index) in form.weeks"
              :key="item.label" label-width="auto" style="margin-bottom: 18px;" :rules="{
                required: true,
                message: $t(`param['必选']`)
              }">
              <span slot="label" style="margin-right: 20px">{{ item.label }}</span>
              <div style="display: flex">
                <el-select ref="elSelect" v-model="item.cutTopId" :placeholder="$t('common.select')"
                  style="width: 100%;">
                  <el-option v-for="item in jfpgOptions" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
                <el-button v-if="item.cutTopId" :style="{ marginLeft: $convertPx(10, 'rem') }"
                  @click="handleMoreLookClick('jfpg', item)">{{ $t('common.check') }}</el-button>
              </div>
            </el-form-item>
          </template>
          <template v-else-if="isJFPGType == 2">
            <el-form-item :label="item.label" :prop="'month.' + index + '.cutTopId'" v-for="(item, index) in form.month"
              :key="item.label" label-width="auto" style="margin-bottom: 18px;" :rules="{
                required: true,
                message: $t(`param['必选']`)
              }">
              <span slot="label" style="margin-right: 20px">{{ item.label }}</span>
              <div style="display: flex">
                <el-select ref="elSelect" v-model="item.cutTopId" :placeholder="$t('common.select')"
                  style="width: 100%;">
                  <el-option v-for="item in jfpgOptions" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
                <el-button v-if="item.cutTopId" :style="{ marginLeft: $convertPx(10, 'rem') }"
                  @click="handleMoreLookClick('jfpg', item)">{{ $t('common.check') }}</el-button>
              </div>
            </el-form-item>
          </template>
        </el-form-item>
        <!-- 召测 -->
        <div style="margin-bottom: 22px;display: flex;" v-if="isShowTestFn">
          <el-button v-if="isShowTest" :style="{ marginLeft: $convertPx(10, 'rem') }" @click="handleTestClick()"
            :disabled="isSend ? isSend : isShowTestDisabled">
            <el-tooltip class="item" :content="$t('获取本地设备削峰填谷方案')" effect="dark" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
            {{ $t('召测') }}
          </el-button>
          <div v-if="isShowTestSuccess" style="display: flex;align-items: center;margin-left: 20px;">
            <el-button type="success" icon="el-icon-check" circle size="mini" style="margin-right: 8px;"></el-button>
            <span>{{ $t('已召测') }}</span>
          </div>
          <div v-if="isShowTestFail" style="display: flex;align-items: center;margin-left: 20px;">
            <el-button type="danger" icon="el-icon-close" circle size="mini" style="margin-right: 8px;"></el-button>
            <span>{{ $t('召测失败') }}</span>
          </div>
        </div>
      </div>
      <el-form-item :label="`${$t(`param['分时电价']`)}`" prop="electricPriceId" style="width: 50%">
        <span slot="label">
          {{ $t(`param['分时电价']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('用于电网计算收益。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <div style="display: flex" v-if="isJFPGType == 0">
          <el-select ref="elSelect" v-model="form.electricPriceId" :placeholder="$t('common.select')"
            style="width: 100%;">
            <el-option v-for="item in priceOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <el-button v-if="form.electricPriceId" :style="{ marginLeft: $convertPx(10, 'rem') }"
            @click="handleLookClick('price')">{{ $t('common.check') }}</el-button>
        </div>

        <template v-else-if="isJFPGType == 1">
          <el-form-item :label="item.label" :prop="'weeks.' + index + '.electricPriceId'"
            v-for="(item, index) in form.weeks" :key="item.label" label-width="auto" style="margin-bottom: 18px;"
            :rules="{
              required: true,
              message: $t(`param['必选']`)
            }">
            <span slot="label" style="margin-right: 20px">{{ item.label }}</span>
            <div style="display: flex">
              <el-select ref="elSelect" v-model="item.electricPriceId" :placeholder="$t('common.select')"
                style="width: 100%;">
                <el-option v-for="item in priceOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
              <el-button v-if="item.electricPriceId" :style="{ marginLeft: $convertPx(10, 'rem') }"
                @click="handleMoreLookClick('price', item)">{{ $t('common.check') }}</el-button>
            </div>
          </el-form-item>
        </template>
        <template v-else-if="isJFPGType == 2">
          <el-form-item :label="item.label" :prop="'month.' + index + '.electricPriceId'"
            v-for="(item, index) in form.month" :key="item.label" label-width="auto" style="margin-bottom: 18px;"
            :rules="{
              required: true,
              message: $t(`param['必选']`)
            }">
            <span slot="label" style="margin-right: 20px">{{ item.label }}</span>
            <div style="display: flex">
              <el-select ref="elSelect" v-model="item.electricPriceId" :placeholder="$t('common.select')"
                style="width: 100%;">
                <el-option v-for="item in priceOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
              <el-button v-if="item.electricPriceId" :style="{ marginLeft: $convertPx(10, 'rem') }"
                @click="handleMoreLookClick('price', item)">{{ $t('common.check') }}</el-button>
            </div>
          </el-form-item>
        </template>
      </el-form-item>
      <el-form-item :label="`${$t(`param['防逆流使能']`)}`" prop="setting1917" style="width: 50%"
        v-if="form.setting1900 == 0 || form.setting1900 == 1 || form.setting1900 == 2">
        <span slot="label">
          {{ $t(`param['防逆流使能']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('防止系统放电馈入电网。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-radio-group v-model="form.setting1917" style="width: 100%">
          <el-radio label="1">{{ $t(`backup['使能']`) }}</el-radio>
          <el-radio label="0">{{ $t(`backup['不使能']`) }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="`${$t('变压器容量')}`" prop="setting1898" style="width: 50%" v-if="form.setting1900 == 3">
        <span slot="label">
          {{ $t('变压器容量') }}
        </span>
        <el-input-number v-model="form.setting1898" :precision="1" :step="0.1" :max="5000" :min="0" :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（0~5000）kVA</span>
      </el-form-item>
    </el-form>
    <div class="box-footer">
      <el-divider></el-divider>
      <!-- <el-button :style="{ width: $convertPx(100, 'rem') }" @click="handleSaveClick()">保存</el-button> -->
      <el-button :style="{ width: $convertPx(100, 'rem') }" @click="getInfo">{{
        $t(`tagsView.refresh`) }}</el-button>
      <el-button type="primary" :style="{ width: $convertPx(100, 'rem') }" @click="handleSendClick"
        :disabled="isSend">{{
          $t(`param['下发']`) }}</el-button>
    </div>

    <!-- 备电方案 -->
    <el-dialog :visible.sync="dialogLookVisible" center :modal-append-to-body="false" :width="dialogLookWidth"
      :title="dialogLookTitle">
      <el-form :model="backupForm" ref="backupFormRef" label-width="auto" v-if="lookType == 'backup'"
        :disabled="!isEdit">
        <el-form-item :label="$t(`backup['方案名称']`)" prop="name">
          <el-input v-model="backupForm.name" :placeholder="$t(`backup['请输入方案名称']`)" />
        </el-form-item>
        <el-form-item :label="$t(`backup['电网充电']`)" prop="setting1901">
          <span slot="label">
            {{ $t(`backup['电网充电']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['使用电网给电池充电']`)" placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-radio-group v-model="backupForm.setting1901" style="width: 100%">
            <el-radio label="1">{{ $t(`backup['使能']`) }}</el-radio>
            <el-radio label="0">{{ $t(`backup['不使能']`) }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`backup['发电机']`)" prop="setting1902">
          <span slot="label">
            {{ $t(`backup['发电机']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['电池没电时使用发电机']`)" placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-radio-group v-model="backupForm.setting1902" style="width: 100%">
            <el-radio label="1">{{ $t(`backup['使能']`) }}</el-radio>
            <el-radio label="0">{{ $t(`backup['不使能']`) }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`backup['发电机充电']`)" prop="setting1903">
          <span slot="label">
            {{ $t(`backup['发电机充电']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['在发电机工作时给电池充电']`)" placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-radio-group v-model="backupForm.setting1903" style="width: 100%">
            <el-radio label="1">{{ $t(`backup['使能']`) }}</el-radio>
            <el-radio label="0">{{ $t(`backup['不使能']`) }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`backup['电池充电功率']`)" prop="setting1904">
          <span slot="label">
            {{ $t(`backup['电池充电功率']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['使用电网、油机给电池充电的功率（不限制光伏功率）']`)"
              placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input-number v-model="backupForm.setting1904" :precision="0" :max="500" :min="0"
            :placeholder="$t(`common['Please enter']`)" style="width: 90%"></el-input-number>
          <span class="suffix">kW</span>
        </el-form-item>
        <el-form-item :label="$t(`backup['备电保持SOC']`)" prop="setting1905">
          <span slot="label">
            {{ $t(`backup['备电保持SOC']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['有电网时，电池保留SOC电量，SOC之上电量可给负载使用']`)"
              placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input-number v-model="backupForm.setting1905" :precision="0" :max="100" :min="0"
            :placeholder="$t(`common['Please enter']`)" style="width: 90%"></el-input-number>
          <span class="suffix">%</span>
        </el-form-item>
      </el-form>
      <el-form :model="jfpgForm" :rules="rules" ref="jfpgFormRef" label-width="auto"
        v-if="lookType == 'jfpg' || lookType == 'price'" :disabled="!isEdit">
        <el-form-item :label="$t(`backup['方案名称']`)" :prop="lookType == 'price' ? 'name' : 'title'" v-if="isEdit">
          <el-input v-if="lookType == 'price'" v-model="jfpgForm.name" :placeholder="$t(`backup['请输入方案名称']`)" />
          <el-input v-else v-model="jfpgForm.title" :placeholder="$t(`backup['请输入方案名称']`)" />
        </el-form-item>
        <el-form-item :label="$t('电价信息')" prop="countryCurrencyId" v-if="lookType == 'price'">
          <!-- <el-select v-model="jfpgForm.countryCurrencyId" :placeholder="$t('common.select')" filterable
              style="width: 100%;">
              <el-option v-for="item in currencyOptions" :key="item.id" :label="`${item.country} - ${item.currency}`"
                :value="item.id">
              </el-option>
            </el-select> -->
          <span>{{ getCurrencyText($route.query.currency) }} / kWh</span>
        </el-form-item>
        <el-form-item :label="$t('费率')" v-if="lookType == 'price'">
          <el-row :gutter="10" type="flex" justify="space-between">
            <template v-for="item in jfpgForm.rateList">
              <el-col :span="6">
                <el-input-number v-model="item.value" :key="item.value" :placeholder="item.label" :precision="2"
                  @change="handleRateChange" class="input-number"></el-input-number>
              </el-col>
            </template>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t(`price['买卖电是否同价']`)" prop="buySell" v-if="lookType == 'price'">
          <el-radio-group v-model="jfpgForm.buySell" style="width: 100%">
            <el-radio :label="0">{{ $t('menu.yes') }}</el-radio>
            <el-radio :label="1">{{ $t('menu.no') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="dialog-add-wrapper">
          <template v-if="lookType == 'jfpg'">
            <el-form-item :label="`${$t(`price['时段']`)}${index + 1}：`" v-for="(item, index) in jfpgForm.pointList"
              :key="`${index}-form`">
              <span slot="label">
                {{ `${$t(`price['时段']`)}${index + 1}` }}
                <el-tooltip class="item" effect="dark" placement="top" :content="$t('功率：分正负，正为放电，负为充电')">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <el-row :gutter="10" type="flex" justify="space-between">
                <el-col :span="6" style="padding-left: 0">
                  <el-form-item :prop="'pointList.' + index + '.startTime'" :rules="{
                    required: true,
                    message: $t('sim.startTime')
                  }" label-width="0">
                    <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                      :picker-options="limitTimeComJfpg(index, 'startTime')" :placeholder="$t('sim.startTime')"
                      style="width: 100%;"></el-time-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="0.5" class="icon-box">~</el-col>
                <el-col :span="6">
                  <el-form-item :prop="'pointList.' + index + '.endTime'" :rules="{
                    required: true,
                    message: $t('sim.endTime')
                  }" label-width="0">
                    <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                      :picker-options="limitTimeComJfpg(index, 'endTime')" :placeholder="$t('sim.endTime')"
                      style="width: 100%;"></el-time-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="lookType == 'jfpg'">
                  <el-form-item :prop="'pointList.' + index + '.power'" :rules="{
                    required: true,
                    message: $t(`price['功率']`)
                  }" label-width="0">
                    <el-input-number v-model="item.power" :precision="1" :placeholder="$t(`price['功率']`)"
                      class="input-number" style="width: 100%;"></el-input-number>
                    <span class="suffix">kW</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="lookType == 'price'">
                  <el-form-item :prop="'pointList.' + index + '.price'" :rules="{
                    required: true,
                    message: $t(`price['电价']`)
                  }" label-width="0">
                    <el-input-number v-model="item.price" :precision="2"></el-input-number>
                    <span class="suffix">{{ $t(`price['元']`) }} / kWh</span>
                  </el-form-item>
                </el-col>
                <el-col :span="2">
                  <el-form-item :prop="'pointList.' + index + '.enable'" :rules="{
                    required: true,
                    message: $t(`price['请选择使能']`)
                  }" label-width="0">
                    <el-switch v-model="item.enable" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                </el-col>
                <el-col :span="1" v-if="isEdit">
                  <el-tooltip :content="$t('price.add')" placement="top">
                    <div class="icon-box" @click="handleTimeAddJfpg"><i class="el-icon-circle-plus-outline icon"></i>
                    </div>
                  </el-tooltip>
                </el-col>
                <el-col :span="1" v-if="isEdit">
                  <el-tooltip :content="$t('common.delete')" placement="top">
                    <div class="icon-box" @click="handleDeleteTimeJfpg(index)"><i class="el-icon-circle-close icon"></i>
                    </div>
                  </el-tooltip>
                </el-col>
              </el-row>
            </el-form-item>
          </template>
          <template v-else>
            <template v-if="jfpgForm.buySell == 0">
              <el-form-item :label="`${$t(`price['时段']`)}${index + 1}：`" v-for="(item, index) in jfpgForm.pointList1"
                :key="`${index}-form`" prop="pointList1">
                <el-row :gutter="10" type="flex" justify="space-between">
                  <el-col :span="5" style="padding-left: 0">
                    <el-form-item :prop="'pointList1.' + index + '.startTime'" :rules="{
                      required: true,
                      message: $t('sim.startTime')
                    }" label-width="0">
                      <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                        :picker-options="limitTimeCom1(index, 'startTime')" :placeholder="$t('sim.startTime')"
                        style="width: 100%;"></el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="0.5" class="icon-box">~</el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'pointList1.' + index + '.endTime'" :rules="{
                      required: true,
                      message: $t('sim.endTime')
                    }" label-width="0">
                      <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                        :picker-options="limitTimeCom1(index, 'endTime')" :placeholder="$t('sim.endTime')"
                        style="width: 100%;"></el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'pointList1.' + index + '.type'" :rules="{
                      required: true,
                      message: $t('费率')
                    }" label-width="0">
                      <el-select v-model="item.type" :placeholder="$t('费率')" style="width: 100%;"
                        @change="handleTypeChange(item.type, index)">
                        <template v-for="item in jfpgForm.rateList">
                          <el-option :key="item.num" :label="item.label" :value="item.num" v-if="item.value">
                          </el-option>
                        </template>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'pointList1.' + index + '.price'" :rules="{
                      required: true,
                      message: $t(`price['电价']`)
                    }" label-width="0">
                      <el-input v-model="item.price" type="text" :placeholder="$t(`price['电价']`)" readonly></el-input>
                      <!-- <span class="suffix">{{ getCurrencyText(jfpgForm.countryCurrencyId) }} / kWh</span> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="1" v-if="isEdit">
                    <el-tooltip :content="$t('price.add')" placement="top">
                      <div class="icon-box" @click="handleTimeAdd1()"><i class="el-icon-circle-plus-outline icon"></i>
                      </div>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="1" v-if="isEdit">
                    <el-tooltip :content="$t('common.delete')" placement="top">
                      <div class="icon-box" @click="handleDeleteTime1(index)"><i class="el-icon-circle-close icon"></i>
                      </div>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </el-form-item>
            </template>
            <template v-else>
              <el-form-item :label="$t(`price['买电']`)" prop="pointList">
                <el-form-item label=" " v-for="(item, index) in jfpgForm.pointList" :key="`pointList-${index}-form`"
                  label-width="0px" class="sell-form-item">
                  <el-row :gutter="10" type="flex" justify="space-between">
                    <el-col :span="5" style="padding-left: 0">
                      <el-form-item :prop="'pointList.' + index + '.startTime'" :rules="{
                        required: true,
                        message: $t('sim.startTime')
                      }" label-width="0">
                        <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                          :picker-options="limitTimeCom(index, 'startTime', 'pointList')"
                          :placeholder="$t('sim.startTime')" style="width: 100%;"></el-time-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="0.5" class="icon-box">~</el-col>
                    <el-col :span="5">
                      <el-form-item :prop="'pointList.' + index + '.endTime'" :rules="{
                        required: true,
                        message: $t('sim.endTime')
                      }" label-width="0">
                        <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                          :picker-options="limitTimeCom(index, 'endTime', 'pointList')" :placeholder="$t('sim.endTime')"
                          style="width: 100%;"></el-time-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item :prop="'pointList.' + index + '.type'" :rules="{
                        required: true,
                        message: $t('费率')
                      }" label-width="0">
                        <el-select v-model="item.type" :placeholder="$t('费率')" style="width: 100%;"
                          @change="handleTypeChange(item.type, index, 'pointList')">
                          <template v-for="item in jfpgForm.rateList">
                            <el-option :key="item.num" :label="item.label" :value="item.num" v-if="item.value">
                            </el-option>
                          </template>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item :prop="'pointList.' + index + '.price'" :rules="{
                        required: true,
                        message: $t(`price['电价']`)
                      }" label-width="0">
                        <el-input v-model="item.price" type="text" :placeholder="$t(`price['电价']`)" readonly></el-input>
                        <!-- <span class="suffix">{{ getCurrencyText(jfpgForm.countryCurrencyId) }} / kWh</span> -->
                      </el-form-item>
                    </el-col>
                    <el-col :span="1" v-if="isEdit">
                      <el-tooltip :content="$t('price.add')" placement="top">
                        <div class="icon-box" @click="handleTimeAdd('pointList')"><i
                            class="el-icon-circle-plus-outline icon"></i>
                        </div>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="1" v-if="isEdit">
                      <el-tooltip :content="$t('common.delete')" placement="top">
                        <div class="icon-box" @click="handleDeleteTime(index, 'pointList')"><i
                            class="el-icon-circle-close icon"></i>
                        </div>
                      </el-tooltip>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-form-item>
              <el-form-item :label="$t(`price['卖电']`)" prop="sellPointList">
                <el-form-item label=" " v-for="(item, index) in jfpgForm.sellPointList"
                  :key="`sellPointList-${index}-form`" label-width="0px" class="sell-form-item">
                  <el-row :gutter="10" type="flex" justify="space-between">
                    <el-col :span="5" style="padding-left: 0">
                      <el-form-item :prop="'sellPointList.' + index + '.startTime'" :rules="{
                        required: true,
                        message: $t('sim.startTime')
                      }" label-width="0">
                        <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                          :picker-options="limitTimeCom(index, 'startTime', 'sellPointList')"
                          :placeholder="$t('sim.startTime')" style="width: 100%;"></el-time-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="0.5" class="icon-box">~</el-col>
                    <el-col :span="5">
                      <el-form-item :prop="'sellPointList.' + index + '.endTime'" :rules="{
                        required: true,
                        message: $t('sim.endTime')
                      }" label-width="0">
                        <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                          :picker-options="limitTimeCom(index, 'endTime', 'sellPointList')"
                          :placeholder="$t('sim.endTime')" style="width: 100%;"></el-time-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item :prop="'sellPointList.' + index + '.type'" :rules="{
                        required: true,
                        message: $t('费率')
                      }" label-width="0">
                        <el-select v-model="item.type" :placeholder="$t('费率')" style="width: 100%;"
                          @change="handleTypeChange(item.type, index, 'sellPointList')">
                          <template v-for="item in jfpgForm.rateList">
                            <el-option :key="item.num" :label="item.label" :value="item.num" v-if="item.value">
                            </el-option>
                          </template>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item :prop="'sellPointList.' + index + '.price'" :rules="{
                        required: true,
                        message: $t(`price['电价']`)
                      }" label-width="0">
                        <el-input v-model="item.price" type="text" :placeholder="$t(`price['电价']`)" readonly></el-input>
                        <!-- <span class="suffix">{{ getCurrencyText(jfpgForm.countryCurrencyId) }} / kWh</span> -->
                      </el-form-item>
                    </el-col>
                    <el-col :span="1" v-if="isEdit">
                      <el-tooltip :content="$t('price.add')" placement="top">
                        <div class="icon-box" @click="handleTimeAdd('sellPointList')"><i
                            class="el-icon-circle-plus-outline icon"></i></div>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="1" v-if="isEdit">
                      <el-tooltip :content="$t('common.delete')" placement="top">
                        <div class="icon-box" @click="handleDeleteTime(index, 'sellPointList')"><i
                            class="el-icon-circle-close icon"></i></div>
                      </el-tooltip>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-form-item>
            </template>
          </template>
        </div>
        <span v-if="lookType == 'jfpg'">{{ $t('注：') }}{{ $t('功率：分正负，正为放电，负为充电') }}</span>
        <span v-if="lookType == 'price'">{{ $t('注：') }}{{ $t('电价：电价货币为项目绑定的货币为准，如项目绑定货币为美元，那么电价为0.2美元') }}</span>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogLookVisible = false">{{ $t('common.Closure') }}</el-button>
        <el-button type="primary" @click="handleMoreClick">{{ $t(`param['查看更多方案']`) }}</el-button>
        <el-button type="primary" @click="handleEditClick" v-if="!isEdit">{{ $t('common.edit') }}</el-button>
        <el-button type="primary" @click="handleConfirmClick" v-else>{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>


    <el-dialog :visible.sync="dialogVisible" width="20%" custom-class="result-dialog" :modal-append-to-body="false">
      <span slot="title"></span>
      <el-result icon="success" :subTitle="$t(`param['参数已下发至设备']`)">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="handleResultClick">{{ $t(`param['查看执行结果']`) }}</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed, watch, onUnmounted } from 'vue'
import { useStore, useRoute, useRouter } from '@/utils/vueApi.js'
import { editBackup } from '@/api/operation/backupScheme'
import { editJfpg } from '@/api/operation/jfpg'
import { editPrice } from '@/api/operation/price'
import _ from 'lodash'
import { checkVersion } from '@/utils/version'
import { allCurrency } from '@/api/operation/currency'

const { proxy } = getCurrentInstance()

const store = useStore()
const route = useRoute()
const router = useRouter()

const form = ref({
  setting1898: undefined,
  setting1900: '',
  setting1917: "1",
  cutTopId: '',
  electricPriceId: '',
  reservePatternId: undefined,
  ac: route.query.id,
  status: 0,
  weeks: [
    { label: proxy.$t(`param['星期一']`), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t(`param['星期二']`), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t(`param['星期三']`), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t(`param['星期四']`), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t(`param['星期五']`), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t(`param['星期六']`), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t(`param['星期天']`), electricPriceId: undefined, cutTopId: undefined }
  ],
  setting1924: '',
  month: [
    { label: proxy.$t('一月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('二月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('三月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('四月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('五月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('六月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('七月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('八月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('九月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('十月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('十一月'), electricPriceId: undefined, cutTopId: undefined },
    { label: proxy.$t('十二月'), electricPriceId: undefined, cutTopId: undefined },
  ]
})
const rules = ref({})
const operationOptions = ref([
  {
    value: "0",
    label: proxy.$t(`param['手动模式']`)
  },
  {
    value: "1",
    label: proxy.$t(`param['削峰填谷']`)
  },
  {
    value: "2",
    label: proxy.$t(`param['后备模式']`)
  },
  {
    value: "3",
    label: proxy.$t('动态扩容')
  },
  // {
  //   value: "4",
  //   label: proxy.$t('光伏消纳')
  // },
])
const jfpgOptions = computed(() => store.state.param.jfpgOptions)
const priceOptions = computed(() => store.state.param.priceOptions)
const backupOptions = computed(() => store.state.param.backupOptions)
const baseInfo = computed(() => store.state.monitor.baseInfo)

/**
 * 保存
 */
const handleSaveClick = () => {
  // 协议版本 BV04
  let electricPriceIds = null
  let cutTopIds = null
  if (baseInfo.value.deviceUpdateFactoryAgreement == 'BV04') {
    electricPriceIds = form.value.weeks.map(item => item.electricPriceId).join(',')
    cutTopIds = form.value.weeks.map(item => item.cutTopId).join(',')
  }
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      const api = store.state.param.systemInfo ? 'editSystemFn' : 'addSystemFn'
      store.dispatch(`param/${api}`, {
        ...form.value,
        cutTopId: baseInfo.value.deviceUpdateFactoryAgreement == 'BV04' ? cutTopIds : form.value.cutTopId,
        electricPriceId: baseInfo.value.deviceUpdateFactoryAgreement == 'BV04' ? electricPriceIds : form.value.electricPriceId,
      }).then(async (res) => {
        proxy.$message({
          type: 'success',
          message: proxy.$t(`param['保存成功']`)
        })
        await getInfo()
      })
    }
  });
}

/**
 * 查看
 */
const dialogLookVisible = ref(false)
const dialogLookTitle = ref('')
const dialogLookWidth = ref('')
const backupForm = ref({})
const jfpgForm = ref({})
const lookType = ref('')
const pointListExample = ref({
  enable: 0,
  endTime: '',
  price: undefined,
  startTime: '',
  type: undefined
})
const rateListExample = ref([
  {
    label: proxy.$t('费率一'),
    num: 0,
    value: undefined
  },
  {
    label: proxy.$t('费率二'),
    num: 1,
    value: undefined
  },
  {
    label: proxy.$t('费率三'),
    num: 2,
    value: undefined
  },
  {
    label: proxy.$t('费率四'),
    num: 3,
    value: undefined
  },
  {
    label: proxy.$t('费率五'),
    num: 4,
    value: undefined
  },
])
const handleLookClick = (type) => {
  if (type == 'backup') {
    let data = _.cloneDeep(backupOptions.value.find(item => item.id == form.value.reservePatternId))
    backupForm.value = data
    dialogLookTitle.value = data.name
    dialogLookWidth.value = proxy.$convertPx(600, 'rem')
  } else if (type == 'jfpg') {
    let data = _.cloneDeep(jfpgOptions.value.find(item => item.id == form.value.cutTopId))
    jfpgForm.value = data
    dialogLookTitle.value = data.title
    dialogLookWidth.value = proxy.$convertPx(900, 'rem')
  } else if (type == 'price') {
    let data = _.cloneDeep(priceOptions.value.find(item => item.id == form.value.electricPriceId))
    if (!data) return proxy.$message(proxy.$t('该方案的货币已修改，请重新添加一个方案'))
    if (data.buySell == 0) {
      data.pointList1 = data.pointList
      data.sellPointList = [{ ...pointListExample.value }]
    } else {
      data.pointList1 = [{ ...pointListExample.value }]
      data.sellPointList = data.sellPointList ? data.sellPointList : [{ ...pointListExample.value }]
    }
    data.rateList = data.rateList ? data.rateList.map(item => {
      item.label = rateTextList.value.find((text, index) => index == item.num)
      return item
    }) : _.cloneDeep(rateListExample.value)
    jfpgForm.value = data
    dialogLookTitle.value = data.name
    dialogLookWidth.value = proxy.$convertPx(1000, 'rem')
  }
  lookType.value = type
  dialogLookVisible.value = true
}
const rateTextList = ref([proxy.$t('费率一'), proxy.$t('费率二'), proxy.$t('费率三'), proxy.$t('费率四'), proxy.$t('费率五')])
const handleMoreLookClick = (type, items) => {
  if (type == 'jfpg') {
    let data = _.cloneDeep(jfpgOptions.value.find(item => item.id == items.cutTopId))
    jfpgForm.value = data
    dialogLookTitle.value = data.title
    dialogLookWidth.value = proxy.$convertPx(900, 'rem')
  } else if (type == 'price') {
    let data = _.cloneDeep(priceOptions.value.find(item => item.id == items.electricPriceId))
    if (!data) return proxy.$message(proxy.$t('该方案的货币已修改，请重新添加一个方案'))
    if (data.buySell == 0) {
      data.pointList1 = data.pointList
      data.sellPointList = [{ ...pointListExample.value }]
    } else {
      data.pointList1 = [{ ...pointListExample.value }]
      data.sellPointList = data.sellPointList ? data.sellPointList : [{ ...pointListExample.value }]
    }
    data.rateList = data.rateList ? data.rateList.map(item => {
      item.label = rateTextList.value.find((text, index) => index == item.num)
      return item
    }) : _.cloneDeep(rateListExample.value)
    jfpgForm.value = data
    dialogLookTitle.value = data.name
    dialogLookWidth.value = proxy.$convertPx(900, 'rem')
  }
  lookType.value = type
  dialogLookVisible.value = true
}

/**
 * 修改
 */
const isEdit = ref(false)
const handleEditClick = () => {
  isEdit.value = true
}
const handleConfirmClick = () => {
  let formName = ''
  if (lookType.value == 'backup') {
    formName = 'backupFormRef'
  } else if (lookType.value == 'jfpg' || lookType.value == 'price') {
    formName = 'jfpgFormRef'
  }
  proxy.$refs[formName].validate((valid) => {
    if (valid) {
      if (lookType.value == 'backup') {
        editBackupFn()
      } else if (lookType.value == 'jfpg' || lookType.value == 'price') {
        editJfpgFn()
      }
    }
  });
}
watch(dialogLookVisible, () => {
  if (dialogLookVisible.value == false) isEdit.value = false
})
/**
 * 查看更多方案
 */
const handleMoreClick = () => {
  if (lookType.value == 'backup') {
    let routeData = router.resolve({
      path: '/operation/pattern',
    });
    window.open(routeData.href, '_blank');
  } else if (lookType.value == 'jfpg') {
    let routeData = router.resolve({
      path: '/operation/jfpg',
    });
    window.open(routeData.href, '_blank');
  } else if (lookType.value == 'price') {
    let routeData = router.resolve({
      path: '/operation/price',
    });
    window.open(routeData.href, '_blank');
  }
}
/**
 * 削峰平谷、分时电价
 */
const limitTimeComJfpg = computed(() => {
  return (index, type) => {
    let options = {
      start: '00:00',
      step: '01:00',
      end: '24:00',
      minTime: undefined,
      selectableRange: '00:00:00 - 23:59:00'
    }
    if (index) {
      if (type == 'startTime') {
        options.minTime = jfpgForm.value.pointList[index - 1].endTime
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
      if (type == 'endTime') {
        options.minTime = jfpgForm.value.pointList[index].startTime
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
    } else {
      if (type == 'startTime') options.minTime = undefined
      if (type == 'endTime') {
        options.minTime = jfpgForm.value.pointList[0].startTime || '00:00'
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
    }
    return options
  }
})
const limitTimeCom = computed(() => {
  return (index, type, listType) => {
    let options = {
      start: '00:00',
      step: '01:00',
      end: '24:00',
      minTime: undefined,
      selectableRange: '00:00:00 - 23:59:00'
    }
    if (index) {
      if (type == 'startTime') {
        options.minTime = listType == 'pointList' ? jfpgForm.value.pointList[index - 1].endTime : jfpgForm.value.sellPointList[index - 1].endTime
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
      if (type == 'endTime') {
        options.minTime = listType == 'pointList' ? jfpgForm.value.pointList[index].startTime : jfpgForm.value.sellPointList[index].startTime
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
    } else {
      if (type == 'startTime') options.minTime = undefined
      if (type == 'endTime') {
        options.minTime = listType == 'pointList' ? jfpgForm.value.pointList[0].startTime : jfpgForm.value.sellPointList[0].startTime
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
    }
    return options
  }
})
const limitTimeCom1 = computed(() => {
  return (index, type) => {
    let options = {
      start: '00:00',
      step: '01:00',
      end: '24:00',
      minTime: undefined,
      selectableRange: '00:00:00 - 23:59:00'
    }
    if (index) {
      if (type == 'startTime') {
        options.minTime = jfpgForm.value.pointList1[index - 1].endTime
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
      if (type == 'endTime') {
        options.minTime = jfpgForm.value.pointList1[index].startTime
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
    } else {
      if (type == 'startTime') options.minTime = undefined
      if (type == 'endTime') {
        options.minTime = jfpgForm.value.pointList1[0].startTime || '00:00'
        options.selectableRange = `${options.minTime}:00 - 23:59:00`
      }
    }
    return options
  }
})
const handleTimeAddJfpg = () => {
  if (jfpgForm.value.pointList.length == 12) return proxy.$message({
    type: 'danger',
    message: `${proxy.$t(`price['最多只能添加12条哦']`)}~`
  })
  jfpgForm.value.pointList.push({ ...pointListExample.value })
}
const handleDeleteTimeJfpg = (index) => {
  if (jfpgForm.value.pointList.length == 0) return proxy.$message({
    type: 'error',
    message: `${proxy.$t(`price['至少要有一条哦']`)}~`
  })
  jfpgForm.value.pointList.splice(index, 1)
}
const handleTimeAdd = (type) => {
  let length = type == 'sellPointList' ? jfpgForm.value.sellPointList.length : jfpgForm.value.pointList.length
  if (length == 12) return proxy.$message({
    type: 'error',
    message: `${proxy.$t(`price['最多只能添加12条哦']`)}~`
  })
  let list = type == 'sellPointList' ? jfpgForm.value.sellPointList : jfpgForm.value.pointList
  list.push({ ...pointListExample.value })
}
const handleTimeAdd1 = () => {
  if (jfpgForm.value.pointList1.length == 12) return proxy.$message({
    type: 'error',
    message: `${proxy.$t(`price['最多只能添加12条哦']`)}~`
  })
  jfpgForm.value.pointList1.push({ ...pointListExample.value })
}
const handleDeleteTime = (index, type) => {
  let length = type == 'sellPointList' ? jfpgForm.value.sellPointList.length : jfpgForm.value.pointList.length
  if (length == 1) return proxy.$message({
    type: 'error',
    message: `${proxy.$t(`price['至少要有一条哦']`)}~`
  })
  let list = type == 'sellPointList' ? jfpgForm.value.sellPointList : jfpgForm.value.pointList
  list.splice(index, 1)
}
const handleDeleteTime1 = (index) => {
  if (jfpgForm.value.pointList1.length == 1) return proxy.$message({
    type: 'error',
    message: `${proxy.$t(`price['至少要有一条哦']`)}~`
  })
  jfpgForm.value.pointList1.splice(index, 1)
}

// 获取货币
const currencyOptions = ref([])
const getCurrencyText = computed(() => {
  return (id) => {
    return currencyOptions.value.find(item => item.id == id)?.currency
  }
})
const getPriceText = computed(() => {
  return (num) => {
    return jfpgForm.value.rateList.find(item => item.num == num)?.value
  }
})
const getCurrencyOptions = () => {
  allCurrency().then(res => {
    currencyOptions.value = res.data
  })
}
getCurrencyOptions()
const handleTypeChange = (type, index, form) => {
  if (jfpgForm.value.buySell == 0) {
    jfpgForm.value.pointList1[index].price = getPriceText.value(type)
  } else {
    jfpgForm.value[form][index].price = getPriceText.value(type)
  }
}
const handleRateChange = () => {
  if (jfpgForm.value.buySell == 0) {
    jfpgForm.value.pointList1.forEach(item => {
      item.price = getPriceText.value(item.type)
    })
  } else {
    jfpgForm.value.pointList.forEach(item => {
      item.price = getPriceText.value(item.type)
    })
    jfpgForm.value.sellPointList.forEach(item => {
      item.price = getPriceText.value(item.type)
    })
  }
}


const editBackupFn = () => {
  editBackup({
    name: backupForm.value.name,
    id: backupForm.value.id,
    setting1901: backupForm.value.setting1901,
    setting1902: backupForm.value.setting1902,
    setting1903: backupForm.value.setting1903,
    setting1904: backupForm.value.setting1904,
    setting1905: backupForm.value.setting1905,
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Change failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Modify successfully']`)
    })
    dialogLookTitle.value = backupForm.value.name
    isEdit.value = false
    store.dispatch('param/allBackUpFn')
  })
}
const editJfpgFn = () => {
  let api = null
  let pointList = null
  let sellPointList = null
  if (lookType.value == 'price') {
    pointList = jfpgForm.value.buySell == 0 ? jfpgForm.value.pointList1 : jfpgForm.value.pointList
    sellPointList = jfpgForm.value.buySell == 0 ? [] : jfpgForm.value.sellPointList
    api = editPrice
  } else {
    pointList = jfpgForm.value.pointList
    api = editJfpg
  }
  api({
    title: jfpgForm.value.title,
    name: jfpgForm.value.name,
    buySell: jfpgForm.value.buySell,
    pointList,
    sellPointList,
    id: jfpgForm.value.id
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Change failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Modify successfully']`)
    })
    dialogLookTitle.value = lookType.value == 'price' ? jfpgForm.value.name : jfpgForm.value.title
    isEdit.value = false
    lookType.value == 'price' ? store.dispatch('param/allPriceFn') : store.dispatch('param/allJfpgFn')
  })
}

// 下发
const sendTime = ref(null)
const handleSendClick = () => {
  if (form.value.status == 2) return proxy.$message({
    type: 'warning',
    message: proxy.$t(`param['正在下发中，请稍后再下发']`)
  })
  // 协议版本 BV04
  let electricPriceIds = null
  let cutTopIds = null
  if (isJFPGType.value == 0) {
    electricPriceIds = form.value.electricPriceId
    cutTopIds = form.value.cutTopId
  } else if (isJFPGType.value == 1) {
    electricPriceIds = form.value.weeks.map(item => item.electricPriceId).join(',')
    cutTopIds = form.value.weeks.map(item => item.cutTopId).join(',')
  } else if (isJFPGType.value == 2) {
    electricPriceIds = form.value.month.map(item => item.electricPriceId).join(',')
    cutTopIds = form.value.month.map(item => item.cutTopId).join(',')
  }
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      proxy.$modal.loading(`${proxy.$t(`param['正在下发中']`)}...`);
      store.dispatch('param/sendParamSystemFn', {
        ac: form.value.ac,
        id: form.value.id,
        setting1900: form.value.setting1900,
        setting1917: form.value.setting1917,
        setting1898: form.value.setting1898,
        cutTopId: cutTopIds,
        electricPriceId: electricPriceIds,
        reservePatternId: form.value.reservePatternId,
        setting1924: form.value.setting1924
      }).then(async res => {
        // dialogVisible.value = true
        getInfo()
        // sendTime.value = setInterval(() => {
        //   getInfo()
        // }, 100000)
        proxy.$modal.closeLoading()
      }).catch(() => {
        proxy.$modal.closeLoading()
      })
    }
  })
}

const dialogVisible = ref(false)

/**
 * 查看结果
 */
const handleResultClick = () => {
  let routeData = router.resolve({
    path: '/operation/log/instruct',
  });
  window.open(routeData.href, '_blank');
}
/**
 * 削峰填谷周期
 */
const isJFPGType = computed(() => {
  if (!_.isEmpty(form.value.setting1924)) {
    if (form.value.setting1924 == '1') return 1
    else if (form.value.setting1924 == '0') return 0
    else if (form.value.setting1924 == '2') return 2
  } else {
    if (baseInfo.value.deviceUpdateFactoryAgreement == 'BV04') return 1
    else return 0
  }
})
watch(() => form.value.setting1900, () => {
  if (form.value.setting1900 != '1') {
    if (_.isEmpty(form.value.setting1924)) form.value.setting1924 = null
    else form.value.setting1924 = '0'
  }
})
const getInfo = async (type) => {
  const res = await store.dispatch('param/systemInfoFn', { ac: route.query.id })
  if (!res) return
  let electricPriceId = null
  let cutTopId = null
  if (!_.isEmpty(res.setting1924)) {
    if (res.setting1924 == '1') {
      let electricPriceIds = res.electricPriceId ? res.electricPriceId.split(',') : []
      electricPriceIds.forEach((item, index) => {
        form.value.weeks[index].electricPriceId = item ? Number(item) : undefined
      })
      let cutTopIds = res.cutTopId ? res.cutTopId.split(',') : []
      cutTopIds.forEach((item, index) => {
        form.value.weeks[index].cutTopId = item ? Number(item) : undefined
      })
    } else if (res.setting1924 == '0') {
      electricPriceId = res.electricPriceId ? Number(res.electricPriceId) : null
      cutTopId = res.cutTopId ? Number(res.cutTopId) : null
    } else if (res.setting1924 == '2') {
      let electricPriceIds = res.electricPriceId ? res.electricPriceId.split(',') : []
      electricPriceIds.forEach((item, index) => {
        form.value.month[index].electricPriceId = item ? Number(item) : undefined
      })
      let cutTopIds = res.cutTopId ? res.cutTopId.split(',') : []
      cutTopIds.forEach((item, index) => {
        form.value.month[index].cutTopId = item ? Number(item) : undefined
      })
    }
  } else {
    if (baseInfo.value.deviceUpdateFactoryAgreement == 'BV04') {
      let electricPriceIds = res.electricPriceId ? res.electricPriceId.split(',') : []
      electricPriceIds.forEach((item, index) => {
        form.value.weeks[index].electricPriceId = item ? Number(item) : undefined
      })
      let cutTopIds = res.cutTopId ? res.cutTopId.split(',') : []
      cutTopIds.forEach((item, index) => {
        form.value.weeks[index].cutTopId = item ? Number(item) : undefined
      })
    } else {
      electricPriceId = res.electricPriceId ? Number(res.electricPriceId) : null
      cutTopId = res.cutTopId ? Number(res.cutTopId) : null
    }
  }
  /**
   * 召测
   */
  // if (res.setting1900 == "1") {
  //   if (!isSend.value && isFirst.value && isShowTestFn.value) getJsonDataFn()
  // }
  let data = _.cloneDeep(form.value)
  if (res.status == 1 && data.status == 2) {
    proxy.$message({
      type: 'success',
      message: proxy.$t(`param['下发成功']`)
    })
  } else if (res.status == 3 && data.status == 2) {
    proxy.$message({
      type: 'error',
      message: proxy.$t(`param['下发失败']`)
    })
  }
  form.value = {
    ...form.value,
    ...res,
    electricPriceId,
    cutTopId
  }
}

/**
 * 召测
 */
const isShowTest = ref(false)
const isShowTestDisabled = ref(false)
const isShowTestSuccess = ref(false)
const isShowTestFail = ref(false)
const isFirst = ref(true)
const getJsonDataFn = () => {
  isShowTestFail.value = false
  isShowTestSuccess.value = false
  proxy.$modal.loading(`${proxy.$t('正在召测中，请稍后')}...`);
  store.dispatch('param/getJsonDataFn', { ac: form.value.ac, cutTop: 1 }).then(() => {
    store.dispatch('param/allJfpgFn').then(() => {
      getInfo()
    })
    isShowTestSuccess.value = true
    isShowTestDisabled.value = true
    setTimeout(() => {
      isShowTestSuccess.value = false
      isShowTestDisabled.value = false
    }, 300000)
    proxy.$modal.closeLoading()
  }).catch(() => {
    isShowTestFail.value = true
    // isShowTestDisabled.value = true
    // setTimeout(() => {
    //   isShowTestFail.value = false
    //   isShowTestDisabled.value = false
    // }, 300000)
    proxy.$modal.closeLoading()
  })
}
const handleOperationChange = (e) => {
  if (isFirst.value) return
  isShowTest.value = e === '1' ? true : false
}
const isShowTestJfpg = ref(false)
const handleTestClick = () => {
  if (isSend.value) return
  // isShowTestJfpg.value = true
  getJsonDataFn()
}
// 根据指定版本号控制是否召测
const isShowTestFn = computed(() => {
  let control = store.state.monitor.control
  if (_.isEmpty(control) && !control.jk_1000) return false
  return true
})

/**
 * 是否可以下发
 */
const isSend = computed(() => {
  if (_.isEmpty(store.state.monitor.control)) return true
  return store.state.monitor.control['onLineState'] == '离线'
})

/**
 * 设置才有范围限制
 */
const isShowExceed = ref(false)
const isExceedCom = computed(() => {
  return (min) => {
    if (isShowExceed.value) {
      return min
    } else {
      return 0
    }
  }
})
const handleInputFocus = () => {
  isShowExceed.value = true
}
const handleInputBlur = () => {
  isShowExceed.value = false
}

const time = ref(null)
onMounted(() => {
  isFirst.value = true
  getInfo().then(() => {
    isFirst.value = false
    let testResult = store.state.param.testResult
    let testResultFn = () => {
      if (testResult == 1) {
        isShowTestSuccess.value = true
        setTimeout(() => {
          isShowTest.value = true
        }, 300000)
      } else if (testResult == 2) {
        isShowTestFail.value = true
        isShowTest.value = true
      }
    }
    if (testResult == 0) {
      setTimeout(() => {
        testResult = store.state.param.testResult
        testResultFn()
      }, 60000)
    } else {
      testResultFn()
    }
  })

  store.dispatch('param/allJfpgFn')
  store.dispatch('param/allPriceFn')
  store.dispatch('param/allBackUpFn')

  // time.value = setInterval(() => {
  //   getInfo()
  // }, 100000)
})
onUnmounted(() => {
  clearInterval(time.value)
  clearInterval(sendTime.value)
})
</script>

<style lang="scss" scoped>
.icon-box {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon {
  font-size: 28px;
  display: block;
}

:deep(.result-dialog .el-dialog__header) {
  border-bottom: none;
}

::v-deep .el-dialog__body {
  padding-right: 10px !important;
}

.el-form-item .el-form-item {
  margin-bottom: 10px;
}

.dialog-add-wrapper {
  max-height: 400px;
  overflow: auto;
  padding-right: 20px;
  margin-bottom: 20px;
}

.sell-form-item {
  :deep(.el-form-item__label) {
    display: none;
  }
}
</style>
