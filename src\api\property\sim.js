import request from '@/utils/request'

// 获取sim列表
export function simList(queryInfo) {
  return request({
    url: '/system/sim/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增sim
export function addSim(data) {
  return request({
    url: '/system/sim',
    method: 'post',
    data
  })
}

// sim卡销户
export function deleteSim(data) {
  return request({
    url: '/system/sim/accountCancel',
    method: 'post',
    data
  })
}

// sim充值
export function rechargeSim(queryInfo) {
  return request({
    url: '/system/sim/recharge',
    method: 'get',
    params: queryInfo
  })
}

// 解绑设备与sim的关系
export function unbindSim(data) {
  return request({
    url: '/system/sim/unbind',
    method: 'post',
    data
  })
}

// 分配sim
export function allotSIM(data) {
  return request({
    url: '/system/sim/allotSim',
    method: 'post',
    data
  })
}

// 删除sim
export function deleteSIM(queryInfo) {
  return request({
    url: `/system/sim/${queryInfo.id}`,
    method: 'delete'
  })
}
