<template>
  <!-- SIM列表 -->
  <div class="box">
    <div class="input_box elevation-4">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="$t(`backup['方案名称']`)" value="name"></el-option>
              <el-option :label="$t('创建人员')" value="createBy"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
          }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{ $t('添加方案') }}</el-button>
        </div>
      </div>
    </div>
    <el-row :gutter="12" class="card-cont" v-loading="loading" v-if="tableData.length">
      <el-col :span="6" v-for="item in tableData" :key="item.id" class="cont-item">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="card-header">
            <div class="card-header-title">{{ item.name }}</div>
            <div>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleEditClick(item)"
                :disabled="item.id == 1 && !$auth.hasRole('admin')">{{
                  $t('common.edit') }}</el-button>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleDeleteClick(item)"
                :disabled="item.id == 1 && !$auth.hasRole('admin')">{{
                  $t('common.delete') }}</el-button>
            </div>
          </div>

          <el-form :model="item" label-width="auto" style="width: 100%" disabled>
            <el-form-item :label="$t('创建人员')" prop="createBy">
              {{ item.createBy }}
            </el-form-item>
            <el-form-item :label="$t(`backup['电网充电']`)" prop="setting1901">
              {{ item.setting1901 == '1' ? $t(`backup['使能']`) : $t(`backup['不使能']`) }}
            </el-form-item>
            <el-form-item :label="$t(`backup['发电机']`)" prop="setting1902">
              {{ item.setting1902 == '1' ? $t(`backup['使能']`) : $t(`backup['不使能']`) }}
            </el-form-item>
            <el-form-item :label="$t(`backup['发电机充电']`)" prop="setting1903">
              {{ item.setting1903 == '1' ? $t(`backup['使能']`) : $t(`backup['不使能']`) }}
            </el-form-item>
            <el-form-item :label="$t(`backup['电池充电功率']`)" prop="setting1904">
              {{ item.setting1904 }} kW
            </el-form-item>
            <el-form-item :label="$t(`backup['备电保持SOC']`)" prop="setting1905">
              {{ item.setting1905 }} %
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <el-empty :image-size="200" v-else></el-empty>
    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
      @pagination="getList" style="margin-top: 20px;text-align: right;" />

    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" :width="$convertPx(600, 'rem')"
      :title="dialogTitle">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
        <el-form-item :label="$t(`backup['方案名称']`)" prop="name">
          <el-input v-model="form.name" :placeholder="$t(`backup['请输入方案名称']`)" />
        </el-form-item>
        <el-form-item :label="$t(`backup['电网充电']`)" prop="setting1901">
          <span slot="label">
            {{ $t(`backup['电网充电']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['使用电网给电池充电']`)" placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-radio-group v-model="form.setting1901" style="width: 100%">
            <el-radio label="1">{{ $t(`backup['使能']`) }}</el-radio>
            <el-radio label="0">{{ $t(`backup['不使能']`) }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`backup['发电机']`)" prop="setting1902">
          <span slot="label">
            {{ $t(`backup['发电机']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['电池没电时使用发电机']`)" placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-radio-group v-model="form.setting1902" style="width: 100%">
            <el-radio label="1">{{ $t(`backup['使能']`) }}</el-radio>
            <el-radio label="0">{{ $t(`backup['不使能']`) }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`backup['发电机充电']`)" prop="setting1903">
          <span slot="label">
            {{ $t(`backup['发电机充电']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['在发电机工作时给电池充电']`)" placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-radio-group v-model="form.setting1903" style="width: 100%">
            <el-radio label="1">{{ $t(`backup['使能']`) }}</el-radio>
            <el-radio label="0">{{ $t(`backup['不使能']`) }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`backup['电池充电功率']`)" prop="setting1904">
          <span slot="label">
            {{ $t(`backup['电池充电功率']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['使用电网、油机给电池充电的功率（不限制光伏功率）']`)"
              placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input-number v-model="form.setting1904" :precision="0" :max="500" :min="0"
            :placeholder="$t(`common['Please enter']`)" style="width: 70%"></el-input-number>
          <span class="suffix">（0~500）kW</span>
        </el-form-item>
        <el-form-item :label="$t(`backup['备电保持SOC']`)" prop="setting1905">
          <span slot="label">
            {{ $t(`backup['备电保持SOC']`) }}
            <el-tooltip class="item" effect="dark" :content="$t(`backup['有电网时，电池保留SOC电量，SOC之上电量可给负载使用']`)"
              placement="right">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input-number v-model="form.setting1905" :precision="0" :max="100" :min="0"
            :placeholder="$t(`common['Please enter']`)" style="width: 70%"></el-input-number>
          <span class="suffix">（0~100）%</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('formRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick } from 'vue'
import { backupList, addBackup, editBackup, deleteBackup } from '@/api/operation/backupScheme'

const { proxy } = getCurrentInstance()

const dialogVisible = ref(false)
const dialogTitle = ref(proxy.$t(`backup['添加方案']`))
const tableData = ref([])
const loading = ref(false)
const total = ref(10)
const queryInfo = ref({
  pageNum: 1,
  pageSize: 10
})
// 搜索
const searchKey = ref('name')
const searchValue = ref('')
// 表单
const form = ref({
  name: '',
  setting1901: '0',
  setting1902: '0',
  setting1903: '0',
  setting1904: undefined,
  setting1905: undefined,
})
const rules = ref({
  name: [
    { required: true, message: proxy.$t(`backup['请输入方案名称']`), trigger: 'blur' }
  ],
  setting1901: [
    { required: true, message: proxy.$t(`backup['请选择电网充电']`), trigger: 'blur' }
  ],
  setting1902: [
    { required: true, message: proxy.$t(`backup['请选择发电机']`), trigger: 'blur' }
  ],
  setting1903: [
    { required: true, message: proxy.$t(`backup['请选择发电机充电']`), trigger: 'blur' }
  ],
  setting1904: [
    { required: true, message: proxy.$t(`backup['请输入电池充电功率']`), trigger: 'blur' }
  ],
  setting1905: [
    { required: true, message: proxy.$t(`backup['请输入备电保持SOC']`), trigger: 'blur' }
  ],
})

//搜索按键
const handleSearchClick = () => {
  getList()
}
// 获取列表
const getList = () => {
  loading.value = true
  backupList({
    pageSize: queryInfo.value.pageSize,
    pageNum: queryInfo.value.pageNum,
    [searchKey.value]: searchValue.value
  }).then(res => {
    let data = res.rows
    tableData.value = data
    total.value = res.total
    loading.value = false
  });
}
getList()
const handleCancelClick = () => {
  dialogVisible.value = false;
}
const handleConfirm = (formName) => {
  proxy.$refs[formName].validate((valid) => {
    if (valid) {
      if (dialogTitle.value == proxy.$t(`backup['添加方案']`)) { // 添加
        addBackupFn()
      } else { // 修改
        editBackupFn()
      }
    }
  });
}
// 添加
const handleAddClick = () => {
  dialogTitle.value = proxy.$t(`backup['添加方案']`)
  dialogVisible.value = true;
  form.value = {
    name: '',
    setting1901: '0',
    setting1902: '0',
    setting1903: '0',
    setting1904: undefined,
    setting1905: undefined,
  }
  nextTick(() => {
    proxy.resetForm('form')
  })
}
// 修改
const handleEditClick = (row) => {
  dialogTitle.value = proxy.$t(`backup['修改方案']`)
  dialogVisible.value = true;
  form.value = {
    ...row
  }
}
const addBackupFn = () => {
  addBackup({
    name: form.value.name,
    setting1901: form.value.setting1901,
    setting1902: form.value.setting1902,
    setting1903: form.value.setting1903,
    setting1904: form.value.setting1904,
    setting1905: form.value.setting1905,
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Addition Failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Added successfully']`)
    })
    getList()
    dialogVisible.value = false
  })
}
const editBackupFn = () => {
  editBackup({
    name: form.value.name,
    id: form.value.id,
    setting1901: form.value.setting1901,
    setting1902: form.value.setting1902,
    setting1903: form.value.setting1903,
    setting1904: form.value.setting1904,
    setting1905: form.value.setting1905,
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Change failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Modify successfully']`)
    })
    getList()
    dialogVisible.value = false
  })
}
// 删除
const handleDeleteClick = (row) => {
  proxy.$confirm(proxy.$t(`menu['Are you sure to delete the data item?']`), proxy.$t('common.systemPrompt'), {
    confirmButtonText: proxy.$t('common.confirm'),
    cancelButtonText: proxy.$t('common.cancel'),
    type: 'warning'
  }).then(() => {
    deleteBackup({
      ids: row.id
    }).then(res => {
      if (res.code !== 200) return proxy.$message({
        type: 'error',
        message: proxy.$t(`common['Deleted Failed']`)
      });
      getList()
      proxy.$message({
        type: 'success',
        message: proxy.$t(`common['Deleted successfully']`)
      });
    })
  }).catch(() => {
    proxy.$message({
      type: 'info',
      message: proxy.$t(`common['Deletion Cancelled']`)
    });
  });
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;

  .header-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
  }

  .input_box {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    &-title {
      width: 70%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .card-cont {
    /* max-height: 840px; */
    height: calc(100% - 80px);
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
  }

  .cont-item {
    margin-bottom: 20px;
    flex: 0 0 auto;
  }
}

::v-deep .el-divider {
  border-top: 1px #dcdfe6 dashed;
  background-color: #fff;
}

:deep(.el-card) {
  border: thin solid rgb(204, 204, 204);
  box-shadow: none !important;
  .el-card__header {
    border-bottom: thin solid rgb(204, 204, 204) !important;
  }
  .el-card__body, .el-main {
    padding-bottom: 0 !important;
  }
}
</style>
