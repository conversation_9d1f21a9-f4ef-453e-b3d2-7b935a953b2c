<!--
故障报警
-->
<template>
  <div class="fault_box">
    <div class="box-left">
      <el-tree :data="dataCom" @node-click="handleNodeClick" node-key="id" class="tree"
        :current-node-key="currentNodeKey">
        <div class="tree-title" slot-scope="{ node }">
          <div>{{ node.label }}</div>
        </div>
      </el-tree>
    </div>
    <div class="box-right">
      <div class="input_box">
        <div>
          <el-radio-group v-model="dateType" size="medium" class="input_ment" @input="changeDateType">
            <el-radio-button :label="$t('date.day')"></el-radio-button>
            <el-radio-button :label="$t('date.month')"></el-radio-button>
            <el-radio-button :label="$t('date.year')"></el-radio-button>
          </el-radio-group>
          <div class="input_ment">
            <span>{{ $t('alarm.time') }}：</span>
            <el-date-picker v-model="date" type="daterange" valueFormat='yyyy-MM-dd' :range-separator="$t('date.to')"
              :start-placeholder="$t('date.start')" :picker-options="pickerOptions" :end-placeholder="$t('date.end')"
              @change="handleDateChange">
            </el-date-picker>
          </div>
          <div class="input_ment">
            <span>{{ $t('alarm.status') }}：</span><el-select v-model="queryInfo.state"
              :placeholder="$t('common.select')" style="width: 120px">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="input_ment">
            <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="$t(`告警代码`)" value="alarmPoint"></el-option>
              <el-option :label="$t('device.name')" value="deviceName"></el-option>
              <el-option :label="$t('alarm.belong')" value="projectName"></el-option>
            </el-select>
          </el-input>
          </div>
          <div class="input_ment">
            <el-button type="primary" @click="handleSearchClick()" icon="el-icon-search">{{ $t('common.search')
              }}</el-button>
          </div>
        </div>
      </div>

      <div class="table_box">
        <el-table v-loading="loading" :data="tableData" class="table" stripe style="width: 100%;"
          :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
          :cell-style="{ 'text-align': 'center', 'font-size': '14px' }">
          <el-table-column type="index" label="#" width="60">
          </el-table-column>
          <el-table-column prop="projectName" :label="$t('alarm.belong')" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="deviceName" :label="$t('device.name')" show-overflow-tooltip />
          <el-table-column prop="name" :label="$t('alarm.object')" show-overflow-tooltip />
          <el-table-column prop="alarmName" :label="$t('alarm.name')" show-overflow-tooltip />
          <el-table-column prop="alarmPoint" :label="$t('告警代码')" show-overflow-tooltip />
          <el-table-column prop="alarmLevel" :label="$t('alarm.level')">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.alarmLevel == 1" type="danger">{{ $t('alarm.level1') }}</el-tag>
              <el-tag v-if="scope.row.alarmLevel == 2" type="warning">{{ $t('alarm.level2') }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ac" :label="$t('alarm.sn')" width="240" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.ac }}<i class="el-icon-copy-document copy"
                v-clipboard:copy="scope.row.ac" v-clipboard:success="copySuccess"></i>
            </template>
          </el-table-column>
          <el-table-column prop="state" :label="$t('alarm.status')" show-overflow-tooltip class-name="alarm-state">
            <template slot-scope="scope">
              <div class="solve" v-if="scope.row.state == 1">
                <svg-icon icon-class="solve" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t('alarm.prStatus') }}</span>
              </div>
              <div class="solve" v-if="scope.row.state == 2">
                <svg-icon icon-class="solve-off" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t('alarm.peStatus') }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="timeZone" :label="$t(`common['时区']`)" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ `UTC${scope.row.timeZone}` }}
            </template>
          </el-table-column>
          <el-table-column prop="sdt" :label="$t('alarm.time')" show-overflow-tooltip />
          <el-table-column prop="createTime" :label="$t('alarm.reporting')" show-overflow-tooltip />
          <el-table-column :label="$t('common.handle')" width="150">
            <template slot-scope="scope">
              <el-button @click="handle(scope.row)" type="text" size="small" :disabled="scope.row.state == 1"
                v-hasPermi="['system:alarm:updateState']">
                {{ $t('alarm.dispose') }}
              </el-button>
              <el-button @click="handleDetailsClick(scope.row)" type="text" size="small">
                {{ $t('详情') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
          @pagination="getList" style="margin-top: 20px;text-align: right;" />
      </div>
    </div>

    <el-dialog :title="$t('告警详情')" :visible.sync="dialog" width="800px" center append-to-body
      :modal-append-to-body="false">
      <div class="detail_box">
        <div class="detail_box-title">{{ $t('告警信息') }}：</div>
        <div class="detail_box-content">
        <div>
          <span>{{ $t('alarm.belong') + '：' }}</span>
          {{ form.projectName }}
        </div>
        <div>
          <span>{{ $t('device.name') + '：' }}</span>
          {{ form.deviceName }}
        </div>
        <div>
          <span>{{ $t('alarm.object') + '：' }}</span>
          {{ form.name }}
        </div>
        <div>
          <span>{{ $t('告警代码') + '：' }}</span>
          <span class="primary">{{ form.alarmPoint }}</span><i class="el-icon-copy-document copy" v-clipboard:copy="form.alarmPoint"
            v-clipboard:success="copySuccess"></i>
        </div>
        <div>
          <span>{{ $t('alarm.name') + '：' }}</span>
          <span class="error">{{ form.alarmName }}</span>
        </div>
        <div>
          <span>{{ $t('alarm.level') + '：' }}</span>
          <el-tag v-if="form.alarmLevel == 1" type="danger">{{ $t('alarm.level1') }}</el-tag>
          <el-tag v-if="form.alarmLevel == 2" type="warning">{{ $t('alarm.level2') }}</el-tag>
        </div>
        <div>
          <span>{{ $t('alarm.sn') + '：' }}</span>
          <span class="primary">{{ form.ac }}</span><i class="el-icon-copy-document copy" v-clipboard:copy="form.ac"
            v-clipboard:success="copySuccess"></i>
        </div>
        <div>
          <span>{{ $t('alarm.status') + '：' }}</span>
          <div class="solve" v-if="form.state == 1">
            <svg-icon icon-class="solve" style="width: 1.3em;height: 1.3em;" />
            <span class="el-icon--right">{{ $t('alarm.prStatus') }}</span>
          </div>
          <div class="solve" v-if="form.state == 2">
            <svg-icon icon-class="solve-off" style="width: 1.3em;height: 1.3em;" />
            <span class="el-icon--right">{{ $t('alarm.peStatus') }}</span>
          </div>
        </div>
        <div>
          <span>{{ $t('alarm.time') + '：' }}</span>
          {{ form.sdt }}
        </div>
        <div>
          <span>{{ $t(`common['时区']`) + '：' }}</span>
          {{ `UTC${form.timeZone}` }}
        </div>
        <div>
          <span>{{ $t('alarm.reporting') + '：' }}</span>
          {{ form.createTime }}
        </div>
        </div>
      </div>
      <div class="detail_problem">
        <div class="detail_problem-big-title">{{ $t('问题分析') }}：</div>
        <div class="detail_problem-small-box">
          <template v-if="form[getPropFn('reasonsOfProblem')]">
            <div class="detail_problem-title">{{ $t('问题原因') }}：</div>
            <div class="detail_problem-content" v-html="form[getPropFn('reasonsOfProblem')]"></div>
          </template>
          <template v-if="form[getPropFn('alarmInvolvedComponents')]">
            <div class="detail_problem-title">{{ $t('涉及部件') }}：</div>
            <div class="detail_problem-content" v-html="form[getPropFn('alarmInvolvedComponents')]"></div>
          </template>
          <template v-if="form[getPropFn('treatingMethod')]">
            <div class="detail_problem-title">{{ $t('处理办法') }}：</div>
            <div class="detail_problem-content" v-html="form[getPropFn('treatingMethod')]"></div>
          </template>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialog = false">{{ $t('common.Closure') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { alarmList, updateStateByRecordNameIds } from '@/api/test/faultalarm'

export default {
  name: "fault",
  data() {
    return {
      stateOptions: [
        {
          label: this.$t('common.all'),
          value: undefined
        },
        {
          label: this.$t('alarm.peStatus'),
          value: 2
        },
        {
          label: this.$t('alarm.prStatus'),
          value: 1
        },
      ],
      tableData: [],
      total: 10,
      date: [], // 获取当前日期
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      loading: true,
      dateType: this.$t('date.month'),
      pickerOptions: {
        shortcuts: [{
          text: this.$t('date.lastWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.lastMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.last3Month'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      currentKey: this.$route.query.groupId.split(',')[0],
      // 搜索
      searchKey: 'alarmPoint',
      searchValue: '',
      /**
       * 详情
       */
      dialog: false,
      form: {}
    };
  },
  computed: {
    dataCom() {
      let groupId = this.$route.query.groupId.split(',')
      let data = []
      groupId.forEach((item, index) => {
        let label = ''
        if (index == 0) {
          label = `${this.$t(`monitor['主机']`)}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
        } else {
          label = `${this.$t(`monitor['从机']`)}-${index < 10 ? '0' + index : index}`
        }
        data.push({
          label,
          id: item
        })
      })
      return data
    },
    currentNodeKey: {
      get: function () {
        return this.currentKey
      },
      set: function (newValue) {
        this.currentKey = newValue
      }
    },
    getBmsCluster() {
      return ({alarmPoint, dc, det, protocolType}) => {
        const ALARM_POINT_BASE = 9900
        const DC_OFFSET = det == '16' ? 161000: 241000
        const BMS_COUNT = 9
        const isBMSBAUCUSTER = det == '24' && protocolType == 20

        // 输入验证
        if (isNaN(Number(alarmPoint)) || isNaN(Number(dc))) {
          throw new Error('Invalid input: alarmPoint and dc must be numbers')
        }

        let alarmPointNum = Number(alarmPoint)
        let clusterIndex = Math.floor((alarmPointNum - ALARM_POINT_BASE) / 10)

        // 边界条件处理
        if (clusterIndex < 0 || clusterIndex >= BMS_COUNT) {
          clusterIndex = BMS_COUNT // 超出范围则默认为 'BMS'
        }

        const dcValue = parseInt(dc) - DC_OFFSET + 1
        return `${dcValue}#${det == '16' ? 'BMS': 'BMS-BAU'}${clusterIndex === BMS_COUNT ? '' : '_' + (isBMSBAUCUSTER ? this.$t('簇') : '') + (clusterIndex + 1)}`
      }
    },
    getPropFn() {
      return (prop) => {
        let lang = this.$store.getters.language
        switch (lang) {
          case 'zh':
            return prop
          case 'en':
            return prop + 'Us'
          case 'it':
            return prop + 'It'
        }
      }
    }
  },
  mounted() {
    // this.queryInfo.ac = this.currentNodeKey
   this.date = this.changeDateType(1)
  },
  methods: {
    handleNodeClick(node) {
      this.currentKey = node.id
      this.getList()
    },
    getList() {
      this.loading = true
      alarmList({
        ...this.queryInfo,
        ac: this.currentNodeKey,
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        state: this.queryInfo.state,
        startTime: this.queryInfo.startTime,
        endTime: this.queryInfo.endTime + ' 23:59:59',
        [this.searchKey]: this.searchValue
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let data = res.rows
        data.forEach(item => {
          if (item.det == "13") {
            item.name = `${parseInt(item.dc) - 131000 + 1}#Monet-AC`
          } else if (item.det == '14') {
            item.name = `${parseInt(item.dc) - 141000 + 1}#Monet-DC`
          } else if (item.det == '15') {
            item.name = `${parseInt(item.dc) - 151000 + 1}#Monet-STS`
          } else if (item.det == '16') {
            // item.name = `${parseInt(item.dc) - 161000 + 1}#BMS`
            item.name = this.getBmsCluster(item)
          } else if (item.det == '12') {
            item.name = this.$t('monitor.control')
          } else if (item.det == '19') {
            if (item.alarmPoint == '19029' || item.alarmPoint == '19030' || item.alarmPoint == '19031' || item.alarmPoint == '19022') {
              item.name = `${parseInt(item.dc) - 191000 + 1}#${this.$t('充电桩')}_2#${this.$t('枪')}`
            } else if (item.alarmPoint == '19026' || item.alarmPoint == '19027' || item.alarmPoint == '19028' || item.alarmPoint == '19011') {
              item.name = `${parseInt(item.dc) - 191000 + 1}#${this.$t('充电桩')}_1#${this.$t('枪')}`
            } else {
              item.name = `${parseInt(item.dc) - 191000 + 1}#${this.$t('充电桩')}`
            }
          } else if (item.det == '24') {
            item.name = this.getBmsCluster(item)
          }
        })
        this.tableData = data
        this.total = res.total
        this.loading = false
      })
    },
    handleDateChange(date) {
      if (!date) return this.changeDateType()
      this.queryInfo.startTime = date[0]
      this.queryInfo.endTime = date[1]
      this.getList()
    },
    changeDateType(type) {
      let startTime = ''
      let endTime = ''
      if (this.dateType == this.$t('date.day')) {
        this.date = [this.$moment(new Date()).startOf('day').format('YYYY-MM-DD'), this.$moment(new Date()).endOf('day').format('YYYY-MM-DD')]
        startTime = this.date[0]
        endTime = this.date[1]
      } else if (this.dateType == this.$t('date.month')) {
        this.date = [this.$moment().startOf('month').format("YYYY-MM-DD"), this.$moment().endOf('month').format("YYYY-MM-DD")]
        startTime = this.date[0]
        endTime = this.date[1]
      } else if (this.dateType == this.$t('date.year')) {
        this.date = [this.$moment().startOf('year').format("YYYY-MM-DD"), this.$moment().endOf('year').format("YYYY-MM-DD")]
        startTime = this.date[0]
        endTime = this.date[1]
      }
      this.queryInfo = {
        ...this.queryInfo,
        startTime,
        endTime
      }
      if (type !== 1) this.getList()
      return [
        startTime,
        endTime
      ]
    },
    // 搜索
    handleSearchClick() {
      this.getList()
    },
    // 处理
    handle(row) {
      this.$modal.confirm(this.$t(`alarm['Are you sure you want to handle this exception manually?']`)).then(() => {
        updateStateByRecordNameIds([row.alarmNameId]).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: res.msg
          })
          this.$message({
            type: 'success',
            message: this.$t(`alarm['Processed successfully!']`)
          })
          this.getList()
        })
      })
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    },
    /**
     * 获取详情
     */
    handleDetailsClick(row) {
      getAlarmDetails({
        alarmPoint: row.alarmPoint,
        alarmValueIndex: row.alarmValueIndex,
        det: row.det,
      }).then(res => {
        if (res.code !== 200) throw this.$message({
          type: 'error',
          message: res.msg
        })
        if (!res.data) throw this.$message({
          type: 'primary',
          message: this.$t('暂无详细信息')
        })
        const replaceFn = (str, type) => str?.replace(type == 1 ? /[\n\t]/g: /[\/]/g, type == 1 ? '<br/>': '、')
        this.form = {
          ...row,
          ...res.data,
          createTime: row.createTime,
          treatingMethod: replaceFn(res.data.treatingMethod, 1),
          treatingMethodIt: replaceFn(res.data.treatingMethodIt, 1),
          treatingMethodUs: replaceFn(res.data.treatingMethodUs, 1),
          reasonsOfProblem: replaceFn(res.data.reasonsOfProblem, 1),
          reasonsOfProblemIt: replaceFn(res.data.reasonsOfProblemIt, 1),
          reasonsOfProblemUs: replaceFn(res.data.reasonsOfProblemUs, 1),
          alarmInvolvedComponents: replaceFn(res.data.alarmInvolvedComponents, 2),
          alarmInvolvedComponentsIt: replaceFn(res.data.alarmInvolvedComponentsIt, 2),
          alarmInvolvedComponentsUs: replaceFn(res.data.alarmInvolvedComponentsUs, 2),
        }
        this.dialog = true
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.fault_box {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  display: flex;

  .box-left {
    flex: 1;
  }
  .box-right {
    flex: 7;
  }

  .table_box {
    padding-bottom: 10px;

    .solve {
      display: flex;
      align-items: center;
    }
  }
}
:deep(.tree) {
  height: 100%;
  border-right: 1px solid #D6D6D6;
  margin-right: 12px;

  .tree-title {
    padding: 10px;
  }

  .el-tree-node__content {
    height: auto;
  }

  .is-current >.el-tree-node__content {
    font-weight: bold;
    color: var(--base-color);
    background-color: #f6f6f6;
  }
  .el-tree-node__content:hover,
  .el-upload-list__item:hover {
    background-color: #f6f6f6;
  }
}

::v-deep .alarm-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
