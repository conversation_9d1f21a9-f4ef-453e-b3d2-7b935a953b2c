<template>
  <div class="home">
    <div class="echarts_box">
      <div class="echarts_1">
        <!-- 流动图 -->
        <type-1 v-if="$route.query.type == 1"></type-1>
        <type-2 v-if="$route.query.type == 2"></type-2>
        <type-3 v-if="$route.query.type == 3"></type-3>
        <type-4 v-if="$route.query.type == 4"></type-4>
        <type-5 v-if="$route.query.type == 5"></type-5>
        <template v-if="$route.query.type == 6">
          <type-6-load v-if="isEmTypePCC"></type-6-load>
          <type-6 v-else></type-6>
        </template>
        <template v-if="$route.query.type == 7">
          <type-7 v-if="!isEmTypePCC"></type-7>
          <type-9 v-else></type-9>
        </template>
        <type-8 v-if="$route.query.type == 8"></type-8>
        <type-9 v-if="$route.query.type == 9"></type-9>
        <type-10 v-if="$route.query.type == 10"></type-10>
        <type-11 v-if="$route.query.type == 11"></type-11>
        <type-12 v-if="$route.query.type == 12"></type-12>
        <type-13 v-if="$route.query.type == 13"></type-13>
      </div>
      <div class="echarts_2">
        <div class="echarts-header">
          <span class="name">
            {{ $t('monitor.lineTitle') }}
            <el-tooltip class="item" effect="dark" placement="top" :content="$t('不根据当地时间统计，根据创建项目所选时区统计')">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <div>
            <el-date-picker v-model="lineDate" format="yyyy-MM-dd" valueFormat="yyyy-MM-dd" :picker-options="{
              disabledDate
            }" @change="handleLineDateChange" style="width: 140px" />
          </div>
        </div>
        <div class="chart-wrapper">
          <line-chart-energy />
        </div>
      </div>
      <div class="echarts_2">
        <div class="echarts-header">
          <span class="name">{{ $t('monitor.barTitle') }}</span>
          <div>
            <el-button type="primary" size="medium" style="margin-right: 10px" @click="handleExportClick">{{
              $t('common.exportReport') }}</el-button>
            <el-radio-group v-model="dateType" size="medium" style="margin-right: 10px" @input="handleDateTypeChange">
              <el-radio-button :label="$t('date.week')"></el-radio-button>
              <el-radio-button :label="$t('date.month')"></el-radio-button>
              <el-radio-button :label="$t('date.year')"></el-radio-button>
            </el-radio-group>
            <el-date-picker v-model="date" v-bind="dateTypeOptions" @change="handleDateChange" :picker-options="{
              firstDayOfWeek: 1
            }" :clearable="false" style="width: 140px" />
          </div>
        </div>
        <div class="barchart" v-loading="loading">
          <bar-chart />
        </div>
      </div>
    </div>
    <div class="echarts_box_2">
      <device></device>
    </div>

    <el-dialog :visible.sync="dialogVisible" :title="$t('export.exportDetail')" center :modal-append-to-body="false">
      <el-form :model="form" label-width="auto">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="`${$t('alarm.belong')}:`" prop="projectName">
              <el-input v-model="baseInfo.projectName" :placeholder="$t(`device['Please enter device name']`)"
                disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('device.name')}:`" prop="deviceName">
              <el-input v-model="form.deviceName" :placeholder="$t(`device['Please enter device name']`)" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('device.screenId')}:`" prop="deviceSerialNumber">
              <el-input v-model="form.deviceSerialNumber"
                :placeholder="$t(`device['Please enter machine serial number (screen)']`)" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('device.model')}:`" prop="deviceModel">
              <el-input v-model="form.deviceModel" :placeholder="$t(`device['Please enter device model']`)" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('device.type')}:`">
              <el-input v-model="form.deviceType" :placeholder="$t(`device['Please enter device model']`)" disabled />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item :label="$t('电量类型')">
              <el-checkbox-group v-model="form.electricTypeList">
                <el-checkbox v-for="item in electricTypeOptions" :label="item.label" :name="item.label" :key="item.value" />
              </el-checkbox-group>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="`${$t('export.selectDate')}:`">
              <el-date-picker v-model="form.date" type="daterange" range-separator="-"
                :start-placeholder="$t('date.start')" :end-placeholder="$t('date.end')" :picker-options="pickerOptions"
                value-format="yyyy-MM-dd" style="width: 100%" @change="handleDateExportChange" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm()">{{ $t('common.export') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import BarChart from '../../dashboard/BarChart'
import LineChartEnergy from '../../dashboard/LineChartEnergy'
import device from "./device/index.vue";
import Type1 from './flowComponents/type-1.vue'
import Type2 from './flowComponents/type-2.vue'
import Type3 from './flowComponents/type-3.vue'
import Type4 from './flowComponents/type-4.vue'
import Type5 from './flowComponents/type-5.vue'
import Type6 from './flowComponents/type-6.vue'
import Type6Load from './flowComponents/type-6-load.vue'
import Type7 from './flowComponents/type-7.vue'
import Type8 from './flowComponents/type-8.vue'
import Type9 from './flowComponents/type-9.vue'
import Type10 from './flowComponents/type-10.vue'
import Type11 from './flowComponents/type-11.vue'
import Type12 from './flowComponents/type-12.vue'
import Type13 from './flowComponents/type-13.vue'
import { getDeviceType } from '@/hook/useDeviceType'
import { emTypeOptions } from '@/constant'

export default {
  name: "operations",
  components: {
    BarChart,
    LineChartEnergy,
    device,
    Type1,
    Type2,
    Type3,
    Type4,
    Type5,
    Type6,
    Type6Load,
    Type7,
    Type8,
    Type9,
    Type10,
    Type11,
    Type12,
    Type13,
  },
  props: {
    lineDateInit: String
  },
  data() {
    return {
      date: '',
      dateType: this.$t('date.week'),
      loading: true,
      dialogVisible: false,
      form: {
        electricTypeList: []
      },
      electricTypeOptions: [
        {
          label: this.$t('储能电量'),
          value: 0
        },
        {
          label: this.$t('光伏电量'),
          value: 1
        },
        {
          label: this.$t('电网电量'),
          value: 2
        },
        {
          label: this.$t('电表'),
          value: 3
        },
      ],
      queryInfo: {
        startTime: this.$moment(new Date()).format('YYYY-MM-DD'),
        endTime: this.$moment(new Date()).format('YYYY-MM-DD'),
        type: 1,
        deviceType: this.$route.query.type,
        deviceSerialNumber: this.$route.query.id
      },
      pickerOptions: {
        shortcuts: [{
          text: this.$t('date.lastWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.lastMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.last3Month'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      lineDate: '',
    };
  },
  computed: {
    flowData() {
      return this.$store.state.monitor.flowData
    },
    baseInfo() {
      let form = this.$store.state.monitor.baseInfo
      let type = this.$route.query.type
      form.deviceType = getDeviceType(type, true)
      form.date = [this.$moment(new Date()).format('YYYY-MM-DD'), this.$moment(new Date()).format('YYYY-MM-DD')]
      this.form = {
        ...form
      }
      return form
    },
    // 流动拓补图展示圆
    showCircle() {
      let flowData = this.$store.state.monitor.flowData
      return (name) => {
        if (-1 < flowData[name] && flowData[name] < 1) {
          return false
        } else if (flowData[name] == 0) {
          return false
        } else {
          return true
        }
      }
    },
    dateTypeOptions() {
      if (this.dateType == this.$t('date.week')) {
        return {
          type: "week",
          placeholder: "选择周",
          format: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        }
      } else if (this.dateType == this.$t('date.month')) {
        return {
          type: "month",
          placeholder: "选择月",
          format: "yyyy-MM",
          valueFormat: "yyyy-MM-dd"
        }
      } else if (this.dateType == this.$t('date.year')) {
        return {
          type: "year",
          placeholder: "选择年",
          format: "yyyy",
          valueFormat: "yyyy-MM-dd"
        }
      }
    },
    isEmTypePCC() {
      let data = this.$store.state.monitor.pcs_ele
      return (this.$route.query.type == 7 || this.$route.query.type == 6) && data.some(item => {
        return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
      })
    }
  },
  watch: {
    lineDateInit() {
      this.lineDate = this.lineDateInit
    }
  },
  mounted() {
    this.date = this.changeDateFn(new Date()).startTime
    this.lineDate = this.lineDateInit
  },
  methods: {
    handleDateTypeChange() {
      this.changeDateFn(this.date)
    },
    handleDateChange(date) {
      this.changeDateFn(date)
    },
    changeDateFn(date) {
      this.loading = true
      let queryInfo = {
        startTime: '',
        endTime: '',
        type: 1,
        deviceType: this.$route.query.type
      }
      if (this.dateType == this.$t('date.week')) {
        let weekOfday = this.$moment(date).format("E"); //计算是这周第几天
        let monday = this.$moment(date).subtract(weekOfday - 1, "days"); //获取周一
        let sunday = this.$moment(date).add(7 - weekOfday, "days"); // 获取周日
        queryInfo.startTime = monday.format('YYYY-MM-DD')
        queryInfo.endTime = sunday.format('YYYY-MM-DD')
        queryInfo.type = 1
      } else if (this.dateType == this.$t('date.month')) {
        queryInfo.startTime = this.$moment(date).startOf('month').format('YYYY-MM-DD')
        queryInfo.endTime = this.$moment(queryInfo.startTime).endOf('month').format("YYYY-MM-DD")
        queryInfo.type = 2
      } else if (this.dateType == this.$t('date.year')) {
        queryInfo.startTime = date
        queryInfo.endTime = date
        queryInfo.type = 3
      }
      this.$store.commit('SET_QUERYINFO', queryInfo)
      this.$store.dispatch('electricStatisticsFn', this.$route.query.id).then(() => {
        this.loading = false
      })
      return queryInfo
    },
    handleDateExportChange(date) {
      if (date == null) {
        this.queryInfo.startTime = ''
        this.queryInfo.endTime = ''
      } else {
        this.queryInfo.startTime = date[0]
        this.queryInfo.endTime = date[1]
      }
    },
    handleExportClick() {
      this.dialogVisible = true
    },
    handleConfirm() {
      if (!this.queryInfo.startTime) return this.$message({
        type: 'error',
        message: this.$t(`export['Please select date']`)
      })
      // let electricTypeList = this.electricTypeOptions.filter(item => this.form.electricTypeList.findIndex(i => i == item.label) != -1).map(item => item.value)
      let electricMeterListRes = this.$store.state.monitor.electricData.electricMeterListRes
      let electricMeterList = electricMeterListRes.length ? electricMeterListRes.map(item => {
        return {
          dc: item.dc,
          dcName: item.dcName
        }
      }) : []
      this.queryInfo.fileName = `${this.form.deviceSerialNumber}_${this.form.deviceType}_${this.queryInfo.startTime}__${this.queryInfo.endTime}_${this.$t('export.fileSuffix')}`
      this.$store.dispatch('exportDeviceMonitoringFn', {
        ...this.queryInfo,
        electricMeterList
      }).then(res => {
        this.dialogVisible = false
      })
    },
    /**
     *
     * 功率分析
     */
    handleLineDateChange(e) {
      this.changeLineDateFn(e)
    },
    changeLineDateFn(date) {
      let utcOffset = Number(this.$route.query.time.split(':')[0])
      if (date) {
        // let nowDate = this.$moment.utc().utcOffset(utcOffset).format('YYYY-MM-DD')
        let nowDate = this.$moment(this.$store.state.monitor.control.sdt).format('YYYY-MM-DD')
        if (date == nowDate) this.lineDate = this.$store.state.monitor.control.sdt
        if (this.$moment(date).isBefore(nowDate)) this.lineDate = date + ' 23:59:59'
      } else {
        this.lineDate = this.$store.state.monitor.control.sdt
      }
      this.$store.commit('SET_LINEQUERYINFO', {
        date: this.lineDate
      })
      this.$store.dispatch('powerAnalysisStatisticsFn', {
        deviceSerialNumber: this.$route.query.id,
        deviceType: this.$route.query.type,
        timeZone: this.$route.query.time,
      })
    },
    disabledDate(time) {
      let utcOffset = Number(this.$route.query.time.split(':')[0])
      return time.getTime() > new Date(this.lineDateInit).getTime()
    }
  }
};
</script>
<style lang="scss" scoped>
.home {
  display: flex;
  /* height: calc(100vh - 295px); */
}

.echarts_box {
  height: calc(100% - 15px);
  flex: 4.5;
  display: flex;
  width: 50%;
  flex-direction: column;

  .echarts_1 {
    height: 260px;
    width: 100%;
    display: flex;
    margin-right: 10px;
    border: 1px solid #D6D6D6;
    border-radius: 12px;
    position: relative;
    margin-bottom: 10px;
    padding-right: 20px;
  }

  .echarts_2 {
    height: 270px;
    border: 1px solid #D6D6D6;
    border-radius: 12px;
    margin-bottom: 10px;
    padding: 10px 10px 0 10px;
    box-sizing: border-box;

    .chart-wrapper {
      height: 90%;
      width: 100%;
    }

    .name {
      float: left;

      font-weight: 700;
    }

    .barchart {
      /* margin-top: 30px; */
      height: calc(100% - 36px);
    }

    .echarts-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .echarts_2:last-child {
    margin-bottom: 0;
  }
}

.echarts_box_2 {
  height: 820px;
  flex: 6;
  border: 1px solid #D6D6D6;
  border-radius: 12px;
  margin-left: 10px;
}

::v-deep .el-range-editor--medium {
  width: calc(100% - 55px);
}

::v-deep .el-input--medium .el-input__inner {
  padding-right: 0;
}
</style>
