<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';
import resize from './mixins/resize'
import { emTypeOptions } from '@/constant'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '98%'
    },
    height: {
      type: String,
      default: '95%'
    }
  },
  data() {
    return {
    }
  },
  watch: {
    '$store.state.monitor.powerGroupLineData.times': {
      deep: true,
      handler(newValue, oldValue) {
        if (this.$store.state.monitor.powerGroupLineData.times) this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  computed: {
    control() {
      let controlFn = () => {
        let currentNodeKey = this.$store.state.monitor.currentNodeKey
        let data = this.$store.state.monitor.groupList[0]
        if (data && Object.keys(data).length == 11) return data.control
        return {
          ac: "null",
          createTime: null,
          isAnalysis: 0,
          jk_1000: null,
          jk_1001: null,
          jk_1002: null,
          jk_1003: null,
          jk_1004: null,
          jk_1005: null,
          jk_1015: null,
          jk_1016: null,
          jk_1017: null,
          jk_1031: null,
          jk_1032: null,
          jk_1033: null,
          jk_1051: null,
          jk_1056: null,
          jk_1074: null,
          jk_1077: null,
          jk_1092: null,
          jk_1093: null,
          jk_1094: null,
          jk_1095: null,
          jk_1105: null,
          jk_1106: null,
          onLineState: "离线",
          sdt: null,
        }
      }
      let controlData = controlFn()
      return controlData
    },
    isShowV54154() {
      let versionStart = this.control?.jk_1000?.split('V')[1].split('.')[0]
      let versionTwo = this.control?.jk_1000?.split('V')[1].split('.')[1]
      let versionThere = this.control?.jk_1000?.split('V')[1].split('.')[2]
      if (versionStart == 5) if (versionTwo == 4154) return true
      return false
    },
    // 组合设备类型，主机为RT07储能系统(纯并网)、PCC电表，电池功率-电表有功功率,取绝对值
    isDeviceMasterTypeRT07() {
      let groupType = this.$route.query.groupType.split(',')
      let data = this.$store.state.monitor.groupList[0]
      if (data && Object.keys(data).length == 11) {
        let isEmTypePCC = data.ele.some(item => {
          return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
        })
        if ((groupType[0] == 7 || groupType[0] == 6) && isEmTypePCC) return true
      }
      return false
    }
  },
  methods: {
    initChart() {
      if (this.chart) this.chart.dispose()
      this.chart = echarts.init(this.$el)
      this.chart.clear();
      let options = {
        title: {
          subtext: `${this.$t('common.unit')}：kW`,
          left: '2%',
        },
        xAxis: {
          data: this.$store.state.monitor.powerGroupLineData.times,
          // boundaryGap: true,
          axisTick: {
            show: false
          }
        },
        dataZoom: [
          {
            type: 'inside',
          }
        ],
        grid: {
          left: '2%',
          right: '2%',
          bottom: 10,
          top: 45,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10],
          formatter: (params) => {
            let groupData = this.$store.state.monitor.powerGroupLineData.groupData
            let groupId = this.$route.query.groupId.split(',')
            let htmlStart = `<div><div>${params[0].name}</div>`
            params.forEach((param, index) => {
              if (param.seriesName.indexOf('SOC') !== -1) {
                htmlStart += `
                <div style="display: flex;align-items: center;">
                  <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                  ${param.seriesName}：
                  <span style="font-weight: 600;margin-right: 3px;">${param.data !== null ? param.data : '--'}</span> %
                </div>`
              } else if (param.seriesName == this.$t('monitor.flowItem4')) {
                if (this.isShowV54154) {
                  htmlStart += `
                  <div style="display: flex;align-items: center;">
                    <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                    ${param.seriesName}：
                    <span style="font-weight: 600;margin-right: 3px;">${groupData[groupId[0]].cells[param.dataIndex] !== null ? groupData[groupId[0]].cells[param.dataIndex] : '--'}</span> kW
                  </div>`
                } else {
                  groupId.forEach((item, index) => {
                    let label = ''
                    if (index == 0) {
                      label = `${this.$t(`monitor['主机']`)}(${index + 1 < 10 ? '0' + (index + 1) : index + 1})`
                    } else {
                      label = `${this.$t(`monitor['从机']`)}(${index < 10 ? '0' + index : index})`
                    }
                    htmlStart += `
                  <div style="display: flex;align-items: center;">
                    <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                    ${label}-${param.seriesName}：
                    <span style="font-weight: 600;margin-right: 3px;">${groupData[item].cells[param.dataIndex] !== null ? groupData[item].cells[param.dataIndex] : '--'}</span> kW
                  </div>`
                  })
                }
              } else if (param.seriesName == this.$t('monitor.flowItem3')) {
                if (!this.isShowV54154) {
                  if (this.isDeviceMasterTypeRT07) {
                    let data = null
                    let groupLineData = this.$store.state.monitor.powerGroupLineData
                    data = groupLineData.jk103133s.map((item, index) => {
                      let em_10012s = groupLineData.em_10012s[index]
                      return (item - em_10012s).toFixed(2)
                    })
                    htmlStart += `
                    <div style="display: flex;align-items: center;">
                      <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                      ${param.seriesName}：
                      <span style="font-weight: 600;margin-right: 3px;">${data[param.dataIndex] !== null ? data[param.dataIndex] : '--'}</span> kW
                    </div>`
                  } else {
                    htmlStart += `
                    <div style="display: flex;align-items: center;">
                      <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                      ${param.seriesName}：
                      <span style="font-weight: 600;margin-right: 3px;">${groupData[groupId[0]].loads[param.dataIndex] !== null ? groupData[groupId[0]].loads[param.dataIndex] : '--'}</span> kW
                    </div>`
                  }
                } else {
                  groupId.forEach((item, index) => {
                    let label = ''
                    if (index == 0) {
                      label = `${this.$t(`monitor['主机']`)}(${index + 1 < 10 ? '0' + (index + 1) : index + 1})`
                    } else {
                      label = `${this.$t(`monitor['从机']`)}(${index < 10 ? '0' + index : index})`
                    }
                    htmlStart += `
                  <div style="display: flex;align-items: center;">
                    <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                    ${label}-${param.seriesName}：
                    <span style="font-weight: 600;margin-right: 3px;">${groupData[item].loads[param.dataIndex] !== null ? groupData[item].loads[param.dataIndex] : '--'}</span> kW
                  </div>`
                  })
                }
              } else {
                htmlStart += `
                <div style="display: flex;align-items: center;">
                  <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                  ${param.seriesName}：
                  <span style="font-weight: 600;margin-right: 3px;">${param.data !== null ? param.data : '--'}</span> kW
                </div>`
              }
            })
            return htmlStart + '</div>'
          }
        },
        yAxis: {
          type: 'value',
          splitLine: { show: false },
          minInterval: 5
        },
        legend: {
          icon: 'circle',
          type: 'scroll'
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: '',
            textAlign: 'center',
            fill: '#000',
            width: 30,
            height: 30,
            fontSize: 16
          }
        },
        series: [],
        color: [
          '#2ec7c9',
          '#b6a2de',
          '#5ab1ef',
          '#ffb980',
          '#d87a80',
          '#8d98b3',
        ]
      }
      let type = this.$route.query.type
      // 电网功率
      if (type == 10000 || type == 10002) {
        let data = null
        if (this.isDeviceMasterTypeRT07) {
          data = this.$store.state.monitor.powerGroupLineData.em_10012s
        } else {
          data = this.$store.state.monitor.powerGroupLineData.powers
        }
        options.series.push({
          name: this.$t('monitor.flowItem1'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data
        })
      }
      // 电池功率
      if (type == 10000 || type == 10002) {
        options.series.push({
          name: this.$t('monitor.flowItem4'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data: this.$store.state.monitor.powerGroupLineData.cells
        })
      }
      // 负载功率
      if (type == 10000 || this.isShowV54154) {
        let data = null
        let groupLineData = this.$store.state.monitor.powerGroupLineData
        data = groupLineData.loads
        options.series.push({
          name: this.$t('monitor.flowItem3'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data
        })
      }
      if (this.isDeviceMasterTypeRT07) {
        let data = null
        let groupLineData = this.$store.state.monitor.powerGroupLineData
        data = groupLineData.jk103133s.map((item, index) => {
          let em_10012s = groupLineData.em_10012s[index]
          return (item - em_10012s).toFixed(2)
        })
        options.series.push({
          name: this.$t('monitor.flowItem3'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data
        })
      }
      // 光伏功率
      if (type == 10000 || type == 10001) {
        options.series.push({
          name: this.$t('monitor.flowItem2'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data: this.$store.state.monitor.powerGroupLineData.photovoltaics
        })
      }
      // soc
      let groupData = this.$store.state.monitor.powerGroupLineData.groupData
      for (const key in groupData) {
        let socData = groupData[key].socs ?? []
        let socDet = new Set()
        socData.forEach(item1 => {
          if (item1) Object.keys(item1).forEach(item2 => socDet.add(item2))
        })
        if (Array.from(socDet).length) {
          Array.from(socDet).forEach(item3 => {
            options.series.push({
              name: item3.substring(0, 2) == '16' ? `${groupData[key].label}_${parseInt(item3) - 161000 + 1}#SOC` : `${groupData[key].label}_SOC`,
              data: socData.map(item4 => {
                if (item4) {
                  if (item4[item3] !== null) {
                    return _.round(item4[item3], 2)
                  } else {
                    return null
                  }
                } return null
              }),
              smooth: true,
              type: 'line',
              symbol: 'none',
              soc: true
            })
          })
        }
      }
      // 直流母线功率
      // if (type == 2 || type == 3 || type == 4 || type == 5 || type == 8 || type == 10) {
      //     options.series.push({
      //       name: this.$t('monitor.flowItem5'),
      //         smooth: true,
      //         type: 'line',
      //         symbol: 'none',
      //         data: this.$store.state.monitor.powerLineData.bus
      //     })
      //   }
      options && this.chart.setOption(options)
    }
  }
}
</script>
