<template>
  <div :class="classObj" class="app-wrapper" id="app-wrapper" :style="{'--current-color': theme}">

    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar v-if="!sidebar.hide" class="sidebar-container"/>
    <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar />
        <tags-view v-if="needTagsView"/>
      </div>
      <app-main/>
      <right-panel>
        <settings/>
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import variables from '@/assets/styles/variables.scss'
import autofit from 'autofit.js'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView
  },
  // mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    }
  },
  mounted() {
    autofit.init({
      dh: 900,
      dw: 1912,
      el: "#app-wrapper",
      resize: true
    })
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mixin.scss";
  @import "~@/assets/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
    /* min-width: 1912px; */

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{var(--base-sidebar-width)});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px);
  }

  .sidebarHide .fixed-header {
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }


  :deep(.el-menu) {
    border-right: none !important;
    overflow-x: hidden;
    background: rgba(0, 147, 182, 1);
    .el-menu-item {
      margin: 5px 0;
      border-radius: 15px;
      color: var(--base-menu-color);
    }
    .el-submenu__title {
      margin: 5px 0;
      border-radius: 15px;
      color: var(--base-menu-color);
      i {
        color: var(--base-menu-color);
      }
    }
    .el-menu-item.is-active {
      border-radius: 15px;
      background: var(--menu-back-ground-active) !important;
      color: var(--base-menu-color-active) !important;
      i {
        color: var(--base-menu-color-active) !important;
      }
    }
    .el-menu-item:hover {
      border-radius: 15px;
      background: var(--menu-back-ground-active) !important;
      color: var(--base-menu-color-active) !important;
      i {
        color: var(--base-menu-color-active) !important;
      }
    }
    .el-submenu__title:hover {
      border-radius: 15px;
      background: var(--menu-back-ground-active) !important;
      color: var(--base-menu-color-active) !important;
      i {
        color: var(--base-menu-color-active) !important;
      }
    }
    .item-title {
      width: 80%;
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
      font-size: 16px;
    }
  }
</style>
