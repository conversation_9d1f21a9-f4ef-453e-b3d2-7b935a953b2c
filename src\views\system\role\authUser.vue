<template>
  <div class="app-container">
    <div class="input_box">
      <div class="header-title">{{ $t('menu.assign') }}</div>
      <div>
        <div class="input_ment">
        <el-input
          v-model="queryParams.userName"
          :placeholder="$t(`user['Please enter user name']`)"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </div>
      <div class="input_ment">
        <el-input
          v-model="queryParams.phonenumber"
          :placeholder="$t(`user['Please enter phone number']`)"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </div>
      <div class="input_ment">
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
      </div>

      <div class="input_ment">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="openSelectUser"
          v-hasPermi="['system:role:add']"
        >{{ $t('menu.addUser') }}</el-button>
      </div>
      <div class="input_ment">
        <el-button
          type="danger"
          icon="el-icon-circle-close"
          :disabled="multiple"
          @click="cancelAuthUserAll"
          v-hasPermi="['system:role:remove']"
        >{{ $t('menu.batch') }}</el-button>
      </div>
      <div class="input_ment">
        <el-button
          type="warning"
          icon="el-icon-close"
          @click="handleClose"
        >{{ $t('common.Closure') }}</el-button>
      </div>
      </div>
    </div>
      

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('user.name')" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('user.nickname')" prop="nickName" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('user.email')" prop="email" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('user.phone')" prop="phonenumber" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.handle')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="cancelAuthUser(scope.row)"
            v-hasPermi="['system:role:remove']"
          >{{ $t('menu.canPerm') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
      style="margin-top: 20px;text-align: right;"
    />
    <select-user ref="select" :roleId="queryParams.roleId" @ok="handleQuery" />
  </div>
</template>

<script>
import { allocatedUserList, authUserCancel, authUserCancelAll } from "@/api/system/role";
import selectUser from "./selectUser";

export default {
  name: "AuthUser",
  dicts: ['sys_normal_disable'],
  components: { selectUser },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中用户组
      userIds: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleId: undefined,
        userName: undefined,
        phonenumber: undefined
      }
    };
  },
  created() {
    const roleId = this.$route.params && this.$route.params.roleId;
    if (roleId) {
      this.queryParams.roleId = roleId;
      this.getList();
    }
  },
  methods: {
    /** 查询授权用户列表 */
    getList() {
      this.loading = true;
      allocatedUserList(this.queryParams).then(response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 返回按钮
    handleClose() {
      const obj = { path: "/system/role" };
      this.$tab.closeOpenPage(obj);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.userIds = selection.map(item => item.userId)
      this.multiple = !selection.length
    },
    /** 打开授权用户表弹窗 */
    openSelectUser() {
      this.$refs.select.show();
    },
    /** 取消授权按钮操作 */
    cancelAuthUser(row) {
      const roleId = this.queryParams.roleId;
      this.$modal.confirm(this.$t(`menu['Are you sure you want to cancel the role of this user?']`)).then(function() {
        return authUserCancel({ userId: row.userId, roleId: roleId });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('menu.canPerSuccess'));
      }).catch(() => {});
    },
    /** 批量取消授权按钮操作 */
    cancelAuthUserAll(row) {
      const roleId = this.queryParams.roleId;
      const userIds = this.userIds.join(",");
      this.$modal.confirm(this.$t(`menu['Do you want to uncheck the user authorization data item?']`)).then(function() {
        return authUserCancelAll({ roleId: roleId, userIds: userIds });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('menu.canPerSuccess'));
      }).catch(() => {});
    }
  }
};
</script>