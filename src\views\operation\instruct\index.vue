<template>
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-select v-model="queryInfo.status" :placeholder="$t('common.select')" style="width: 120px"
            @change="handleSearchClick()">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option :label="$t('成功')" :value="0" />
            <el-option :label="$t('失败')" :value="1" />
          </el-select>
        </div>
        <div class="input_ment">
          <el-select v-model="queryInfo.type" :placeholder="$t('common.select')" style="width: 120px"
            @change="handleSearchClick">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="'SN'" value="ac"></el-option>
              <el-option :label="$t('创建人员')" value="nickName"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
          }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="ac" :label="$t('device.sn')" width="240" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.ac }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.ac"
              v-clipboard:success="copySuccess"></i>
          </template>
        </el-table-column>
        <el-table-column prop="nickName" :label="$t(`log['操作人员']`)" show-overflow-tooltip align="center" />
        <el-table-column prop="type" :label="$t(`log['指令类型']`)" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag type="primary">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="parameterSetting" :label="$t(`log['下发参数']`)" show-overflow-tooltip align="center" />
        <el-table-column prop="parameterSettingValue" :label="$t(`log['下发参数值']`)" show-overflow-tooltip align="center">
        </el-table-column>
        <el-table-column prop="status" :label="$t(`log['执行结果']`)" show-overflow-tooltip class-name="alarm-state"
          align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleLookClick(scope.row)" size="mini"
              v-if="scope.row.type == 5 || scope.row.type == 8">
              <div style="display: flex;align-items: center;">
                <div class="solve" v-if="scope.row.status == 0">
                  <svg-icon icon-class="solve" style="width: 1.3em;height: 1.3em;" />
                </div>
                <div class="solve" v-else-if="scope.row.status == 1">
                  <svg-icon icon-class="solve-off" style="width: 1.3em;height: 1.3em;" />
                </div>
                <div class="solve" v-else>
                  <svg-icon icon-class="await" style="width: 1.3em;height: 1.3em;" />
                </div>
                <div style="margin-left: 5px;">{{ $t('查看详情') }}</div>
              </div>
            </el-button>
            <div v-else>
              <div class="solve" v-if="scope.row.status == 0">
                <svg-icon icon-class="solve" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t(`log['执行成功']`) }}</span>
              </div>
              <div class="solve" v-else-if="scope.row.status == 1">
                <svg-icon icon-class="solve-off" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t(`log['执行失败']`) }}</span>
              </div>
              <div class="solve" v-else>
                <svg-icon icon-class="await" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t(`log['已下发，等待执行']`) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t(`log['下发时间']`)" show-overflow-tooltip align="center" />
        <el-table-column prop="updateTime" :label="$t(`log['完成时间']`)" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.updateTime ? scope.row.updateTime : '-' }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>

    <!-- 查看升级记录 -->
    <el-dialog :visible.sync="dialogView" :close-on-click-modal="false" :modal-append-to-body="false" center>
      <template slot="title">
        <div style="display: flex;align-items: center">
          <div style="margin-right: 10px;">{{ viewData.type == 5 ? $t('升级记录') : $t('远程控制记录') }}</div>
          <div class="solve" v-if="viewData.status == 0">
            <svg-icon icon-class="solve" style="width: 1.3em;height: 1.3em;" />
            <span class="el-icon--right">{{ $t(`log['执行成功']`) }}</span>
          </div>
          <div class="solve" v-else-if="viewData.status == 1">
            <svg-icon icon-class="solve-off" style="width: 1.3em;height: 1.3em;" />
            <span class="el-icon--right">{{ $t(`log['执行失败']`) }}</span>
          </div>
          <div class="solve" v-else>
            <svg-icon icon-class="await" style="width: 1.3em;height: 1.3em;" />
            <span class="el-icon--right">{{ $t(`log['已下发，等待执行']`) }}</span>
          </div>
        </div>
      </template>
      <el-table :data="upgradeProgressInfo" stripe :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
        :cell-style="{ 'text-align': 'center', 'font-size': '14px' }" style="width: 100%;" row-key="deviceId"
        v-if="viewData.type == 5">
        <el-table-column prop="ac" :label="$t('device.sn')" show-overflow-tooltip width="200px" />
        <el-table-column prop="softUpdFileName" :label="$t('升级文件')" show-overflow-tooltip width="200px" />
        <el-table-column prop="softCurVersion" :label="$t('升级版本')" show-overflow-tooltip width="200px" />
        <el-table-column prop="current" :label="$t('当前升级个数')" show-overflow-tooltip width="200px" />
        <el-table-column prop="total" :label="$t('总升级个数')" show-overflow-tooltip width="200px" />
        <el-table-column prop="softUpdResult" :label="$t('升级结果')" show-overflow-tooltip width="200px" />
        <el-table-column prop="updDesc" :label="$t('升级描述')" show-overflow-tooltip width="200px" />
        <el-table-column prop="softUpdTime" :label="$t('升级时间(UTC+00:00)')" show-overflow-tooltip width="200px" />
      </el-table>
      <el-table :data="vncInfo" stripe :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
        :cell-style="{ 'text-align': 'center', 'font-size': '14px' }" style="width: 100%;" row-key="deviceId"
        v-if="viewData.type == 8">
        <el-table-column prop="ac" :label="$t('device.sn')" show-overflow-tooltip width="200px" />
        <el-table-column prop="askdt" :label="$t('回复类型')" show-overflow-tooltip min-width="100px">
          <template #default="scoped">
            <el-tag type="primary">{{ scoped.row.askdt == 'IT25' ? $t('下发') : $t('关闭') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="code" :label="$t('回复结果')" show-overflow-tooltip>
          <template #default="scoped">
            <el-tag :type="getBitType(scoped.row.code).type">{{ getBitType(scoped.row.code).text }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sdt" :label="$t('回复时间')" show-overflow-tooltip width="180px" />
        <el-table-column prop="vncDesc" :label="$t('描述')" />
        <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip width="200px" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogView = false">{{ $t('common.Closure') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { commandLogList, getSelectVncInfo } from '@/api/operation/instruct'
import { decimalToBinaryReverseArray } from '@/utils/ruoyi'

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      typeOptions: [
        {
          label: this.$t('common.all'),
          value: undefined
        },
        {
          value: 1,
          label: this.$t(`log['系统设置参数']`)
        },
        {
          value: 6,
          label: this.$t(`log['系统开关机']`)
        },
        {
          value: 2,
          label: this.$t(`log['MAC参数']`)
        },
        {
          value: 3,
          label: this.$t(`log['MDC参数']`)
        },
        {
          value: 4,
          label: this.$t(`log['电池参数']`)
        },
        {
          value: 5,
          label: this.$t(`log['设备升级']`)
        },
        {
          value: 8,
          label: this.$t('下发远程')
        },
      ],
      dialogView: false,
      viewData: {},
      vncInfo: [],
      // 搜索
      searchKey: 'ac',
      searchValue: '',
    };
  },
  mounted() {
    this.getList()
  },
  computed: {
    getTypeText() {
      return (type) => {
        if (type) return this.typeOptions.find(item => item.value == type).label
      }
    },
    upgradeProgressInfo() {
      return this.$store.state.param.upgradeProgressInfo
    },
    getBitType() {
      return (num) => {
        let n = parseInt(num)
        if (n == 0) return {
          text: this.$t('成功'),
          type: 'success'
        }
        let res = decimalToBinaryReverseArray(n)
        let result = []
        let msg = [this.$t('成功'), this.$t('目录创建失败'), this.$t('文件下载失败'), this.$t('库文件缺失')]
        res.forEach((item, index) => {
          if (index != 0) {
            if (item == 1) result.push(msg[index])
          }
        })
        return {
          text: result.join('，'),
          type: 'danger'
        }
      }
    }
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      this.tableData = []
      commandLogList({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        status: this.queryInfo.status,
        type: this.queryInfo.type,
        [this.searchKey]: this.searchValue
      }).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    async handleLookClick(row) {
      this.viewData = row
      if (row.type == 5) {
        await this.$store.dispatch('param/getUpgradeProgressFn', { ac: row.ac, uuid: row.uuid })
        this.dialogView = true
      } else if (row.type == 8) {
        await this.getSelectVncInfoFn({ ac: row.ac, uuid: row.uuid })
        this.dialogView = true
      }
    },
    /**
     * vnc
     */
    async getSelectVncInfoFn(queryInfo) {
      const res = await getSelectVncInfo(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      this.vncInfo = res.data
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    },
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;

  .solve {
    display: flex;
    align-items: center;
  }
}

::v-deep .alarm-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
