/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-25 12:08:46
 * @FilePath: \elecloud_platform-main\src\store\modules\app.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Cookies from 'js-cookie'
import axios from 'axios'
import { getAddressByIP } from '@/api/common'
import { detail } from '@/api/system/logo'
import i18n from '@/lang'

import { jsonp } from 'vue-jsonp'

const state = {
  ipInfo: {},
  domainInfo: {},
  baseImg: {},
  flowImg: {},
  theme: {}
}

const mutations = {
  SET_IPINFO: (state, data) => {
    state.ipInfo = data
  },
  SET_DOMAININFO: (state, data) => {
    state.domainInfo = data
  },
  SET_BASEIMG: (state, data) => {
    state.baseImg = data
  },
  SET_FLOWIMG: (state, data) => {
    state.flowImg = data
  },
  SET_THEME: (state, data) => {
    state.theme = data
  },
}

const actions = {
  async getIpInfo({ commit }) {
    try {
      // const res = await jsonp('https://api.map.baidu.com/location/ip?ak=8HtKRTrIMxMOTHv5P2CGotp848GnHCNv')
      const res = await axios.get('https://qifu-api.baidubce.com/ip/local/geo/v1/district')
      let data = {
        countryCode: 'CN',
        ip: res.data.ip,
        lat: res.data.data.lat,
        lng: res.data.data.lng,
      }
      if (res.data.data.country == '中国') {
        data.countryCode = 'CN'
      } else {
        data.countryCode = 'EN'
      }
      Cookies.set('ip', JSON.stringify(data))
      commit('SET_IPINFO', data)
      // console.log(data, res.data);
      return data
    } catch (error) {
      console.log(error);
      return {
        countryCode: 'EN'
      }
    }
  },
  async getDomainName({ commit, dispatch, rootGetters }) {
    Cookies.set('sidebarStatus', 1)
    const domainName = window.location.hostname;
    const origin = window.location.origin
    const res = await detail({ domainName })
    // 网站标题
    let lang = Cookies.get('language')
    let title = document.getElementsByTagName("title")
    if (lang == 'zh') {
      title[0].innerHTML = res.data ? res.data.title : '亿兰科云平台'
    } else {
      title[0].innerHTML = res.data ? res.data.titleUs : 'Elecloud'
    }

    // 主题
    if (res.data && res.data.theme) {
      let theme = JSON.parse(res.data.theme)
      localStorage.setItem(
        "layout-setting",
        `{
            "theme":"${theme['--primary-color']}"
          }`
      );
      dispatch('settings/changeSetting', {
        key: 'theme',
        value: theme['--primary-color']
      }, { root: true })
      for (var key in theme) {
        document.documentElement.style.setProperty(key, theme[key]);
      }
      commit('SET_THEME', theme)
    } else {
      commit('SET_THEME', {
        '--primary-color': '#0093b6',
        '--menu-back-ground-active': '#29a4c2',
        '--base-menu-color': '#fff',
        '--base-menu-color-active': '#fff',
        '--base-color': '#000000E6',
        '--menu-back-ground': '#0093b6',
        '--menu-logo-back-ground': '#0093b6',
      })
    }
    if (res.data && res.data.baseImg) {
      commit('SET_BASEIMG', JSON.parse(res.data.baseImg))
    } else {
      commit('SET_BASEIMG', {
        // running: '#FB560A',
        // moneySum: '#FB8477',
        // device: '#4E6070',
        // guangfu: '#7A89FE',
        // capacity: '#28C79C',
        // ratedPower: '#B6A2DE',
        // discharge: '#54B9FF',
        // charge: '#00CCC6',
        // capacityPower: '#FFAA43',
        running: '#0093b6',
        moneySum: '#0093b6',
        device: '#0093b6',
        guangfu: '#0093b6',
        capacity: '#0093b6',
        ratedPower: '#0093b6',
        discharge: '#0093b6',
        charge: '#0093b6',
        capacityPower: '#0093b6',
      })
    }
    if (res.data && res.data.flowImg) {
      commit('SET_FLOWIMG', JSON.parse(res.data.flowImg))
    } else {
      commit('SET_FLOWIMG', {
        // flow_pv: '#F8B52F',
        // flow_load: '#6BE6C1',
        // flow_pe: '#B6A2DE',
        // flow_en: '#626C91',
        // flow_de: '#0093B6',
        // flow_dc: '#A0A7E6',
        // flow_ac: '#3FB1E3',
        // flow_bt: '#C4EBAD',
        flow_pv: '#0093b6',
        flow_load: '#0093b6',
        flow_pe: '#0093b6',
        flow_en: '#0093b6',
        flow_de: '#0093b6',
        flow_dc: '#0093b6',
        flow_ac: '#0093b6',
        flow_bt: '#0093b6',
      })
    }

    if (res.data) {
      commit('SET_DOMAININFO', res.data)
    } else {
      commit('SET_DOMAININFO', {
        title: i18n.t('欢迎'),
        titleUs: i18n.t('欢迎')
      })
    }

    // 网站icon
    let link = document.querySelector("link[rel*='icon']") || document.createElement('link')
    link.rel = 'icon'
    link.href = `${origin}/prod-api${res.data ? res.data.appbarLogoPath : ''}` // 图片放public目录
    document.getElementsByTagName('head')[0].appendChild(link)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
