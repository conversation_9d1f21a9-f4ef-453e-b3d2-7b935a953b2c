/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-05-27 11:54:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-05-27 18:10:10
 * @FilePath: \elecloud_platform-main\src\api\operation\alias.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取列表
export function bindAliasList(queryInfo) {
  return request({
    url: '/system/device/point/list',
    method: 'get',
    params: queryInfo
  })
}
// 设备详情页展示 - 无分页
export function bindAliasQuery(queryInfo) {
  return request({
    url: '/system/device/point/query',
    method: 'get',
    params: queryInfo
  })
}
// 绑定测点获取列表 - 无分页
export function getPointAll(queryInfo) {
  return request({
    url: '/system/point/getAll',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addBindAlias(data) {
  return request({
    url: '/system/device/point/add',
    method: 'post',
    data
  })
}

//  修改
export function editBindAlias(data) {
  return request({
    url: '/system/device/point/update',
    method: 'post',
    data
  })
}

// 删除
export function deleteBindAlias(queryInfo) {
  return request({
    url: `/system/device/point/${queryInfo.devicePointIds}`,
    method: 'delete'
  })
}
