<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-01 11:14:30
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="param-box">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" style="width: 50%">
      <el-form-item :label="`${$t(`param['下发状态']`)}`" prop="status" style="width: 50%">
        <span slot="label">
          {{ $t(`param['下发状态']`) }}
          <el-tooltip class="item" effect="dark" placement="right">
            <div slot="content" style="line-height: 1.5">
              &emsp;{{ $t(`param['未下发']`) }}：{{ $t(`param['该类参数从未下发']`) }};
              <br />
              &emsp;{{ $t(`param['下发中']`) }}：{{ $t(`param['参数已成功下发至设备，执行未知，请等待']`) }};
              <br />
              {{ $t(`param['下发成功']`) }}：{{ $t(`param['参数已成功下发至设备并已执行成功']`) }};
              <br />
              {{ $t(`param['下发失败']`) }}：{{ $t(`param['参数已成功下发至设备，设备并未执行成功']`) }}
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-link :underline="false" v-if="form.status == 0">{{ $t(`param['未下发']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 1" type="success">{{ $t(`param['下发成功']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 2" type="primary">{{ $t(`param['下发中']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 3" type="danger">{{ $t(`param['下发失败']`) }}</el-link>
      </el-form-item>
      <!-- <el-form-item :label="`当前版本号：`" prop="setting1912">
        <el-input v-model="form.setting1912" :placeholder="'请选择'">
          <span slot="suffix">kW</span>
        </el-input>
      </el-form-item> -->
      <el-form-item :label="$t(`oss['访问域名']`)" prop="endpoint">
        <el-radio-group v-model="form.endpoint" style="width: 100%">
          <el-radio v-for="item in addressOptions" :key="item.text" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t(`oss['文件类型']`)" prop="fileType">
        <el-select ref="elSelect" v-model="form.fileType" :placeholder="$t(`common['select']`)" style="width: 100%;">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(`失败是否重启`)" prop="updateType" v-if="form.fileType == 2">
        <span slot="label">
          {{ $t('失败是否重启') }}
          <el-tooltip class="item" effect="dark" placement="right">
            <div slot="content" style="line-height: 1.5">
              {{ $t('该功能只有新版本才支持') }}
              <br />
              {{ $t('通用版本') }}: V7.2.4
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-radio-group v-model="form.failRestart" style="width: 100%">
          <el-radio :label="1">{{ $t('menu.yes') }}</el-radio>
          <el-radio :label="0">{{ $t('menu.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t(`oss['升级对象']`)" prop="upgradeObject"
        v-if="form.fileType == 5 || form.fileType == 3 || form.fileType == 4">
        <el-select ref="elSelect" v-model="form.upgradeObject" :placeholder="$t(`common['select']`)"
          style="width: 100%;">
          <el-option v-for="item in objOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t(`param['版本文件']`)}`" prop="fileAllPath" v-if="form.fileType != 6">
        <el-select ref="elSelect" v-model="form.fileAllPath" :placeholder="$t('common.select')" style="width: 100%">
          <el-option v-for="item in ossOptions" :key="item.filePath" :label="item.fileName" :value="item.filePath" />
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t('TAR版本文件')}`" prop="fileAllPath" v-if="form.fileType == 6">
        <el-select ref="elSelect" v-model="form.fileAllPath" :placeholder="$t('common.select')" style="width: 100%">
          <el-option v-for="item in ossOptions" :key="item.filePath" :label="item.fileName" :value="item.filePath" />
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t('INI版本文件')}`" prop="fileAllPathIni" v-if="form.fileType == 6">
        <el-select ref="elSelect" v-model="form.fileAllPathIni" :placeholder="$t('common.select')" style="width: 100%">
          <el-option v-for="item in ossOptions" :key="item.bmsFilePathIni" :label="item.bmsFileNameIni"
            :value="item.bmsFilePathIni" />
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t('升级进度')}`" v-if="form.status !== 0">
        <div v-if="isShowProgress" style="line-height: 40px;">
          <el-progress :percentage="percentage" :color="progressColor" :format="progressFormat" :stroke-width="20"
            style="line-height: 40px;" class="progress-bar"></el-progress>
        </div>
        <div style="display: flex;align-items: center" v-else>
          <template v-if="form.fileType != 6">
            <template v-for="item in progressInfo">
              <div :key="item.current" v-if="!_.isEmpty(item)"
                style="height: 100px;width: 80px;border: 1px solid #000;margin-right: 2px;"
                :style="{ background: item.background, borderColor: item.borderColor, color: item.color }">
                <div style="font-size: 14px;line-height: 60px;text-align: center; padding: 0 3px">
                  {{ $t('模块') }}{{ item.current }}
                  <el-progress :percentage="item.progress" :text-inside="true" :stroke-width="13"
                    :status="item.status"></el-progress>
                </div>
              </div>
            </template>
            <div style="margin-left: 10px;font-size: 14px; color: #13ce66;" v-if="form.status == 1">{{ $t('升级成功') }}
            </div>
            <div style="margin-left: 10px;font-size: 14px; color: #409eff;" v-if="form.status == 2">{{ $t('升级中') }}
            </div>
            <div style="margin-left: 10px;font-size: 14px; color: #f56c6c;" v-if="form.status == 3">{{ $t('升级失败') }}
            </div>
          </template>
          <template v-else>
            <div style="margin-left: 10px;font-size: 14px;"
              :style="{ color: form.status == 1 ? '#13ce66' : '#f56c6c' }">{{
                bmsUpdateMessage }}</div>
          </template>
        </div>
      </el-form-item>
    </el-form>
    <div class="box-footer">
      <el-divider></el-divider>
      <!-- <el-button :style="{ width: $convertPx(100, 'rem') }" @click="handleSaveClick()">保存</el-button> -->
      <el-button type="primary" :style="{ width: $convertPx(100, 'rem') }" @click="handleSendClick"
        :disabled="isSend">{{
          $t(`param['下发']`) }}</el-button>
    </div>

    <el-dialog :visible.sync="dialogVisible" width="20%" :modal-append-to-body="false">
      <span slot="title"></span>
      <el-result icon="success" :subTitle="$t(`param['参数已下发至设备']`)">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="handleResultClick">{{ $t(`param['查看执行结果']`) }}</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed, watchEffect, watch, nextTick } from 'vue'
import { useStore, useRoute, useRouter } from '@/utils/vueApi.js'
import _ from 'lodash'
import { checkPermi } from '@/utils/permission.js'

const { proxy } = getCurrentInstance()
const props = defineProps({
  index: {
    type: Number,
    default: 0
  }
})

watch(
  () => props.index,
  () => {
    form.value.ac = route.query.groupId.split(',')[props.index]
    getInfo()
  }
)

const store = useStore()
const route = useRoute()
const router = useRouter()

const form = ref({
  fileAllPath: null,
  ac: route.query.groupId.split(',')[props.index],
  status: 0,
  endpoint: 'http://oss-cn-shenzhen.aliyuncs.com',
  fileType: 2,
  upgradeObject: null,
  bucketname: null,
  uuid: null,
  failRestart: 0
})
const ossOptions = computed(() => store.state.param.ossOptions)
const rules = ref({
  fileAllPath: [
    { required: true, message: proxy.$t(`param['请选择版本文件']`), trigger: 'blur' }
  ],
  fileAllPathIni: [
    { required: true, message: proxy.$t(`param['请选择版本文件']`), trigger: 'blur' }
  ],
  endpoint: [
    { required: true, message: proxy.$t(`common['select']`), trigger: 'blur' }
  ],
  fileType: [
    { required: true, message: proxy.$t(`common['select']`), trigger: 'blur' }
  ],
  upgradeObject: [
    { required: true, message: proxy.$t(`common['select']`), trigger: 'blur' }
  ],
})
const typeOptions = computed(() => {
  if (checkPermi(['system:sendMqtt:upgradeJson'])) {
    return [
      {
        value: 2,
        label: proxy.$t(`oss['HMI升级文件']`)
      },
      {
        value: 3,
        label: proxy.$t(`oss['MAC升级文件']`)
      },
      {
        value: 4,
        label: proxy.$t(`oss['MDC升级文件']`)
      },
      {
        value: 5,
        label: proxy.$t(`oss['STS升级文件']`)
      },
      {
        value: 6,
        label: proxy.$t('BMS升级文件')
      },
    ]
  }
  if (isShowV74280.value && checkPermi(['system:sendMqtt:BMSupgradeJson'])) {
    return [{
      value: 6,
      label: proxy.$t('BMS升级文件')
    }]
  }
  else return [
    {
      value: 2,
      label: proxy.$t(`oss['HMI升级文件']`)
    },
    {
      value: 3,
      label: proxy.$t(`oss['MAC升级文件']`)
    },
    {
      value: 4,
      label: proxy.$t(`oss['MDC升级文件']`)
    },
    {
      value: 5,
      label: proxy.$t(`oss['STS升级文件']`)
    },
    {
      value: 6,
      label: proxy.$t('BMS升级文件')
    },
  ]
})
const objOptions = computed(() => {
  let type = form.value.fileType
  if (type == 3 || type == 4) {
    return [
      {
        value: 'DSP',
        label: 'DSP',
      },
      {
        value: 'ARM',
        label: 'ARM',
      },
    ]
  } else if (type == 5) {
    return [
      {
        value: 'DSP',
        label: 'DSP',
      }
    ]
  } else {
    return []
  }
})
const addressOptions = ref([
  {
    value: 'http://oss-cn-shenzhen.aliyuncs.com',
    text: 1,
    label: proxy.$t('深圳'),
    bucketname: 'elecod-oss'
  },
  {
    value: 'http://oss-ap-southeast-1.aliyuncs.com',
    text: 2,
    label: proxy.$t('新加坡'),
    bucketname: 'elecod-oss-global'
  },
])
const searchFn = () => {
  let endpoint = addressOptions.value.find(item => item.value == form.value.endpoint).text
  store.dispatch('param/allOssListFn', {
    endpoint,
    fileType: form.value.fileType,
    upgradeObject: form.value.upgradeObject,
  })
}

watch(() => form.value.fileType, () => {
  searchFn()
  if (form.value.fileAllPath) form.value.fileAllPath = undefined
  if (form.value.upgradeObject) form.value.upgradeObject = undefined
})
watch(() => form.value.upgradeObject, (val, oldVal) => {
  searchFn()
  if (form.value.fileAllPath)form.value.fileAllPath = undefined
})
watch(() => form.value.endpoint, () => {
  searchFn()
  if (form.value.fileAllPath) form.value.fileAllPath = undefined
  if (form.value.upgradeObject) form.value.upgradeObject = undefined
})

const handleSaveClick = () => {
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      const api = store.state.param.macInfo ? 'editMACFn' : 'addMACFn'
      store.dispatch(`param/${api}`, form.value).then(async (res) => {
        proxy.$message({
          type: 'success',
          message: proxy.$t(`param['保存成功']`)
        })
        await getInfo()
      })
    }
  });
}

// 下发
const handleSendClick = () => {
  if (form.value.status == 2) return proxy.$message({
    type: 'warning',
    message: proxy.$t(`param['正在下发中，请稍后再下发']`)
  })
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      proxy.$modal.loading(`${proxy.$t(`param['正在下发中']`)}...`);
      store.dispatch('param/sendParamUpdateFn', {
        ac: form.value.ac,
        fileAllPath: form.value.fileAllPath,
        endpoint: form.value.endpoint,
        fileType: form.value.fileType,
        upgradeObject: form.value.upgradeObject,
        version: ossOptions.value.find(item => item.filePath == form.value.fileAllPath).version,
        bucketname: addressOptions.value.find(item => item.value == form.value.endpoint).bucketname,
        failRestart: form.value.fileType == 2 ? form.value.failRestart : null,
        fileAllPathIni: form.value.fileType == 6 ? form.value.fileAllPathIni : null,
      }).then(res => {
        // dialogVisible.value = true
        clearInterval(progressTime.value)
        getInfo()
        proxy.$modal.closeLoading()
      }).catch(() => {
        proxy.$modal.closeLoading()
      })
    }
  })
}

const dialogVisible = ref(false)
/**
 * 查看结果
 */
const handleResultClick = () => {
  let routeData = router.resolve({
    path: '/operation/log/instruct',
  });
  window.open(routeData.href, '_blank');
}

const getInfo = async () => {
  const res = await store.dispatch('param/upgradeInfoFn', { ac: form.value.ac })
  if (!res) {
    form.value = {
      fileAllPath: undefined,
      ac: route.query.groupId.split(',')[props.index],
      status: 0,
      endpoint: 'http://oss-cn-shenzhen.aliyuncs.com',
      fileType: 2,
      upgradeObject: undefined,
      bucketname: undefined,
      uuid: undefined
    }
    if (isShowV74280.value && checkPermi(['system:sendMqtt:BMSupgradeJson'])) form.value.fileType = 6
    else form.value.fileType = 2
    return
  }
  form.value.status = res.status
  form.value.uuid = res.uuid
  form.value.fileType = res.fileType ? Number(res.fileType) : 2
  form.value.fileAllPath = res.fileAllPath
  nextTick(() => {
    form.value.upgradeObject = res.upgradeObject
  })
  if (!form.value.uuid || form.value.status == 0) return
  // 获取进度
  if (form.value.status == 2) {
    getProgress()
  } else {
    getProgressInfo()
  }
}

const progressTime = ref(null)
const getProgress = async () => {
  percentage.value = 0
  await getInfoTwo()
  await getProgressInfo()
  let pollTime = 0
  if (form.value.fileType == 2) {
    pollTime = 10000
  } else {
    if (form.value.upgradeObject == 'DSP') {
      pollTime = progressInfo.value.length < 1 ? 10000 : 60000
    } else if (form.value.upgradeObject == 'ARM') {
      pollTime = 10000
    } else {
      pollTime = 60000
    }
  }
  if (form.value.status !== 2) clearInterval(progressTime.value)
  progressValue.value = 0
  progressTime.value = setInterval(() => {
    getInfoTwo().then(() => {
      getProgressInfo()
    })
  }, pollTime)
}
const getInfoTwo = async () => {
  const res = await store.dispatch('param/upgradeInfoFn', { ac: form.value.ac })
  if (!res) return
  form.value.status = res.status
  form.value.uuid = res.uuid
  form.value.fileType = res.fileType ? Number(res.fileType) : 2
  form.value.fileAllPath = res.fileAllPath
  form.value.upgradeObject = res.upgradeObject
  if (form.value.status !== 2) clearInterval(progressTime.value)
}
/**
 * 升级进度
 */
const percentage = ref(0)
const isDownloadFile = ref(true)
const isShowFail = ref(false)
const bmsUpdateMessage = ref('')
watch(() => percentage.value, () => {
  if (percentage.value >= 100) {
    if (isDownloadFile.value) percentage.value = 99
  }
})
const progressFormat = (percentage) => {
  if (form.value.status == 1) {
    if (form.value.fileType == 2) {
      return percentage === 100 ? `HMI ${proxy.$t('升级成功')}` : `${percentage}%  ${proxy.$t('正在下载远程升级包')}`
    }
  } else {
    if (percentage === 100) {
      return proxy.$t('远程升级包下载成功')
    } else {
      if (isShowFail.value && percentage == 0) return proxy.$t('远程升级包下载失败')
      return `${percentage}%  ${proxy.$t('正在下载远程升级包')}`
    }
  }
}
const progressInfo = ref([])
const progressResInfo = ref([])
const progressColor = ref('#409eff')
const isShowProgress = ref(true)
const progressValue = ref(0)
watch(() => progressValue.value, () => {
  if (progressValue.value >= 100) {
    progressValue.value = 99
  }
})
const getProgressInfo = async () => {
  progressInfo.value = progressInfo.value.filter(item => item.mi == form.value.uuid || _.isEmpty(item))
  const res = await store.dispatch('param/getUpgradeProgressFn', { ac: form.value.ac, uuid: form.value.uuid })
  progressResInfo.value = res
  if (!res.length) { // 返回为空数组时
    if (form.value.status == 3) { // 下发失败
      isDownloadFile.value = false
      percentage.value = 0
      progressColor.value = '#f56c6c'
      isShowProgress.value = true
      isShowFail.value = true // 是否显示失败
    } else { // 下发中
      isDownloadFile.value = true
      percentage.value = percentage.value + 10
      progressColor.value = '#409eff'
      isShowProgress.value = true
      isShowFail.value = false // 是否显示失败
    }
  } else if (res.length == 1) { // 下载文件成功，有模块的要加载模块
    isDownloadFile.value = false
    isShowFail.value = false // 是否显示失败
    if (res[0].dc == "12") { // 升级屏、只有下载文件
      if (form.value.status == 3) {
        isDownloadFile.value = false
        percentage.value = 0
        progressColor.value = '#f56c6c'
        isShowProgress.value = true
        isShowFail.value = true // 是否显示失败
      } else {
        percentage.value = 100
        progressColor.value = '#67c23a'
        isShowProgress.value = true
      }
    } else if (res[0].dc == '16') {
        if (res[0].type == '0' && res[0].softUpdResult == 'success') {
          percentage.value = 100
          progressColor.value = '#67c23a'
          isShowProgress.value = true
        } else {
          isDownloadFile.value = false
          percentage.value = 0
          progressColor.value = '#f56c6c'
          isShowProgress.value = true
          isShowFail.value = true // 是否显示失败
        }
    } else {
      percentage.value = 100
      progressColor.value = '#67c23a'
      getProgressValue()
      // 第一个模块，自定义添加
      progressInfo.value = [
        {
          id: 0,
          mi: form.value.uuid,
          current: 1,
          total: 1,
          background: '#f4f4f5',
          borderColor: '#d3d4d6',
          color: '#909399',
          progress: progressValue,
          status: undefined,
        }
      ]
      // 下载升级文件进度条隐藏
      isShowProgress.value = false
    }
  } else {
    isDownloadFile.value = false
    isShowProgress.value = false // 下载升级文件进度条隐藏
    isShowFail.value = false // 是否显示失败
    if (res.length == 2 && form.value.fileType == 6) { // 升级BMS
      let type = res[1].type
      if (type == '10') {
        bmsUpdateMessage.value = proxy.$t('升级成功')
      } else {
        bmsUpdateMessage.value = res[1].updDesc
      }
    } else {
      setModuleFn(res)
    }
  }
}
const setModuleFn = (res) => {
  if (form.value.status == 2) { // 下发状态为--下发中
      res.forEach((item, index) => {
        if (index) { // 返回的报文必须要有数据
          if (index = res.length - 1 && item.type == "2") {

          } else { // 返回的数据的最后一项是否为下载完成的结果，如果是就不添加模块
            // 判断模块数组中是否包含返回的报文中的已经升级成功的模块数
            let currentIndex = progressInfo.value.findIndex(item1 => item1.id == Number(item.current))
            if (currentIndex == -1) { // 如果模块数组中没有包含返回的报文中的已经升级成功的模块数，则添加
              // 如果模块数组中没有数据，则直接添加
              if (progressInfo.value.length == 0) {
                // 返回的报文中最后一项为已升级成功的状态，则直接按照报文中的模块数添加至模块数组中
                if (res[res.length - 1].type == "2") {
                  progressInfo.value[item.current] = {
                    id: Number(item.current),
                    mi: item.mi,
                    current: Number(item.current) + 1,
                    total: Number(item.total),
                    background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
                    borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
                    color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
                    progress: 100,
                    status: item.softUpdResult == 'error' ? 'exception' : 'success',
                  }
                  progressInfo.value = [...progressInfo.value]
                } else {
                  // 返回的报文不一定包含几个模块，则自定义添加所包含的模块
                  progressInfo.value.push({
                    id: Number(item.current),
                    mi: item.mi,
                    current: Number(item.current) + 1,
                    total: Number(item.total),
                    background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
                    borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
                    color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
                    progress: 100,
                    status: item.softUpdResult == 'error' ? 'exception' : 'success',
                  })
                  progressInfo.value = [...progressInfo.value]
                  // 如果添加好的模块数小于模块总数，则添加下一个模块
                  if (progressInfo.value.length < progressInfo.value[0].total) {
                    getProgressValue()
                    console.log(progressValue.value, 11111111111111111);
                    progressInfo.value.push({
                      id: progressInfo.value[progressInfo.value.length - 1].id + 1,
                      mi: progressInfo.value[0].mi,
                      current: progressInfo.value[progressInfo.value.length - 1].current + 1,
                      total: progressInfo.value[0].total,
                      background: '#f4f4f5',
                      borderColor: '#d3d4d6',
                      color: '#909399',
                      progress: progressValue.value >= 100 ? 99 : progressValue.value,
                      status: undefined,
                    })
                    progressInfo.value = [...progressInfo.value]
                  }
                }
              } else if (progressInfo.value.length !== 0 && progressInfo.value.length <= progressInfo.value[0].total) {
                getProgressValue()
                progressInfo.value.push({
                  id: currentIndex + 1,
                  mi: progressInfo.value[0].mi,
                  current: progressInfo.value[0].current + 1,
                  total: progressInfo.value[0].total,
                  background: '#f4f4f5',
                  borderColor: '#d3d4d6',
                  color: '#909399',
                  progress: progressValue.value >= 100 ? 99 : progressValue.value,
                  status: undefined,
                })
                progressInfo.value = [...progressInfo.value]
              }
            } else { // 模块数组中包含返回的报文中的已经升级成功的模块数 -- 已包含
              // 自定义的模块数据显示成功
              progressInfo.value[item.current] = {
                id: Number(item.current),
                mi: item.mi,
                current: Number(item.current) + 1,
                total: Number(item.total),
                background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
                borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
                color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
                progress: 100,
                status: item.softUpdResult == 'error' ? 'exception' : 'success',
              }
              progressInfo.value = [...progressInfo.value]
              // 如果模块数组的长度小于总模块数，则自定义添加一个模块
              if (progressInfo.value.length < progressInfo.value[0].total) {
                getProgressValue()
                progressInfo.value.push({
                  id: progressInfo.value[progressInfo.value.length - 1].id + 1,
                  mi: progressInfo.value[0].mi,
                  current: progressInfo.value[progressInfo.value.length - 1].current + 1,
                  total: progressInfo.value[0].total,
                  background: '#f4f4f5',
                  borderColor: '#d3d4d6',
                  color: '#909399',
                  progress: progressValue.value >= 100 ? 99 : progressValue.value,
                  status: undefined,
                })
                progressInfo.value = [...progressInfo.value]
              }
            }
          }
        }
      })
    } else { // 有下发结果，不管是失败还是成功
      res.forEach((item, index) => {
        if (index) {
          if (index = res.length - 1 && item.type == "2") {

          } else {
            progressInfo.value[item.current] = {
              id: Number(item.current),
              mi: item.mi,
              current: Number(item.current) + 1,
              total: Number(item.total),
              background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
              borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
              color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
              progress: 100,
              status: item.softUpdResult == 'error' ? 'exception' : 'success',
            }
            progressInfo.value = [...progressInfo.value]
          }
        }
      })
    }
}

const getProgressValue = () => {
  if (form.value.fileType == 2) {
    progressValue.value = progressValue.value >= 100 ? 99 : progressValue.value + 33
  } else {
    if (form.value.upgradeObject == 'DSP') {
      progressValue.value = progressValue.value >= 100 ? 99 : progressValue.value + 19
    } else {
      progressValue.value = progressValue.value >= 100 ? 99 : progressValue.value + 33
    }
  }
}

const control = computed(() => {
  let control = function () {
    let currentNodeKey = store.state.monitor.currentNodeKey
    let data = store.state.monitor.groupList[props.index]
    if (data && Object.keys(data).length == 11) return data.control
    return {
      ac: "null",
      createTime: null,
      isAnalysis: 0,
      jk_1000: null,
      jk_1001: null,
      jk_1002: null,
      jk_1003: null,
      jk_1004: null,
      jk_1005: null,
      jk_1015: null,
      jk_1016: null,
      jk_1017: null,
      jk_1031: null,
      jk_1032: null,
      jk_1033: null,
      jk_1051: null,
      jk_1056: null,
      jk_1074: null,
      jk_1077: null,
      jk_1092: null,
      jk_1093: null,
      jk_1094: null,
      jk_1095: null,
      jk_1105: null,
      jk_1106: null,
      onLineState: "离线",
      sdt: null,
    }
  }
  let controlData = control()
  return controlData
})
const isSend = computed(() => {

  if (_.isEmpty(control.value)) return true
  return control.value['onLineState'] == '离线'
})

/**
 * 是否显示V74280
 * BMS升级
 */
 const isShowV74280 = computed(() => {
  let versionStart = control.value?.jk_1000?.split('V')[1].split('.')[0]
  let versionTwo = control.value?.jk_1000?.split('V')[1].split('.')[1]
  let versionThere = control.value?.jk_1000?.split('V')[1].split('.')[2]
  if (versionStart == 7) if (versionTwo == 4280) return true
  return false
})

onMounted(() => {
  getInfo()
  // store.dispatch('param/allOssListFn')
  searchFn()
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: none;
}

:deep(.progress-bar) {
  .el-progress-bar {
    width: 60%;
  }

  .el-progress__text {
    font-size: 14px !important;
  }

  .el-progress__text i {
    font-size: 20px !important;
  }
}

:deep(.el-progress-bar__innerText) {
  width: 100% !important;
  text-align: center !important;
  line-height: 13px !important;
}
</style>
