<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-01-03 17:56:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-23 15:11:59
 * @FilePath: \elecloud_platform-main\src\layout\components\Sidebar\Item.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      vnodes.push(<svg-icon icon-class={icon} style={'margin-right: 10px;width: 18px;height: 18px;min-width: 18px;'} />)
    }

    if (title) {
      if (title.length > 5) {
        vnodes.push(<div slot='title' class={'item-title'} title={(title)}>{(title)}</div>)
      } else {
        vnodes.push(<div slot='title' class={'item-title'}>{(title)}</div>)
      }
    }
    return vnodes
  }
}
</script>
