<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-01-13 15:08:06
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- 储能 -->
<template>
  <div class="home">
    <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ $t('device.info') }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot">
        <el-descriptions-item :label="$t('device.model')">
          {{ baseInfo.deviceModel }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ratedCapacity')">
          {{ baseInfo.deviceBatteryCapacity }}kWh
        </el-descriptions-item>
        <el-descriptions-item :label="$t('device.version')">
          {{ baseInfo.deviceFactoryVersion }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.topItem2')">
          {{ baseInfo.deviceRatedPower }}kW
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.topItem6')" v-if="$route.query.type == 1">
          {{ baseInfo.photovoltaicInstalledCapacity }}kWp
        </el-descriptions-item>
        <el-descriptions-item :label="$t('device.sn')">
          {{ baseInfo.deviceSerialNumber }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('device.address')">
          {{ baseInfo.projectAddress }}
        </el-descriptions-item>
      </el-descriptions>
  </div>
</template>
<script>

export default {
  name: "information",
  data() {
    return {
    };
  },
  computed: {
    baseInfo() {
      return this.$store.state.monitor.baseInfo
    }
  },
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
}
</style>
