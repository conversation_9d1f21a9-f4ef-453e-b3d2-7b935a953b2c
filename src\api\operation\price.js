import request from '@/utils/request'

// 获取列表
export function priceList(queryInfo) {
  return request({
    url: '/system/electricPrice/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addPrice(data) {
  return request({
    url: '/system/electricPrice',
    method: 'post',
    data
  })
}

// 修改
export function editPrice(data) {
  return request({
    url: '/system/electricPrice',
    method: 'put',
    data
  })
}

// 获取全部数据
export function allPrice(queryInfo) {
  return request({
    url: '/system/electricPrice/getElectricPrices',
    method: 'get',
    params: queryInfo
  })
}

// 删除
export function deletePrice(queryInfo) {
  return request({
    url: `/system/electricPrice/${queryInfo.electricIds}`,
    method: 'delete'
  })
}
