<template>
  <div class="box">
    <div class="input_box elevation-4">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="$t(`backup['方案名称']`)" value="title"></el-option>
              <el-option :label="$t('创建人员')" value="createBy"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
            }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{ $t('添加方案') }}</el-button>
        </div>
      </div>
    </div>
    <el-row :gutter="12" class="card-cont" v-loading="loading" v-if="tableData.length">
      <el-col :span="8" v-for="item in tableData" :key="item.id" class="cont-item">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="card-header">
            <div class="card-header-title">{{ item.title }}</div>
            <div>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleTitleClick(item)">{{
                  $t('common.check') }}</el-button>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleEditClick(item)"
                :disabled="item.id == 1 && !$auth.hasRole('admin')">{{
                  $t('common.edit') }}</el-button>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleDeleteClick(item)"
                :disabled="item.id == 1 && !$auth.hasRole('admin')">{{
                  $t('common.delete') }}</el-button>
            </div>
          </div>
          <el-form :model="item" label-width="auto" class="cont-form" disabled>
            <el-form-item :label="$t('创建人员')" prop="createBy">
              {{ item.createBy }}
            </el-form-item>
            <el-row>
              <el-col :span="24" v-for="(subItem, index) in item.pointList" :key="`${index}-form`">
                <el-form-item :label="`${$t(`price['时段']`)}${index + 1}`">
                  <el-row :gutter="10">
                    <el-col :span="6">
                      <el-form-item :prop="'pointList.' + index + '.startTime'">
                        <el-time-select v-model="subItem.startTime" :picker-options="{
                          start: '00:00',
                          step: '01:00',
                          end: '24:00'
                        }" :placeholder="$t('sim.startTime')" style="width: 100%;" readonly></el-time-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="1">~</el-col>
                    <el-col :span="6">
                      <el-form-item :prop="'pointList.' + index + '.endTime'">
                        <el-time-select v-model="subItem.endTime" :picker-options="{
                          start: '00:00',
                          step: '01:00',
                          end: '24:00',
                        }" :placeholder="$t('sim.endTime')" style="width: 100%;" readonly></el-time-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item :prop="'pointList.' + index + '.power'">
                        <el-input v-model="subItem.power" :placeholder="$t(`price['功率']`)" style="width: 100%" readonly>
                          <span slot="suffix">kW</span>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="3">
                      <el-form-item :prop="'pointList.' + index + '.enable'">
                        <el-switch v-model="subItem.enable" :active-value="1" :inactive-value="0"
                          style="margin-left: 20%;" disabled />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <el-empty :image-size="200" v-else></el-empty>
    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
      @pagination="getList" style="margin-top: 20px;text-align: right;" />


    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" :width="$convertPx(900, 'rem')"
      :title="dialogTitle">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
        <el-form-item :label="$t(`backup['方案名称']`)" prop="title">
          <el-input v-model="form.title" :placeholder="$t(`backup['请输入方案名称']`)" />
        </el-form-item>
        <div class="dialog-add-wrapper">
          <el-form-item :label="`${$t(`price['时段']`)}${index + 1}`" v-for="(item, index) in form.pointList"
            :key="`${index}-form`">
            <span slot="label">
              {{ `${$t(`price['时段']`)}${index + 1}` }}
              <el-tooltip class="item" effect="dark" placement="top" :content="$t('功率：分正负，正为放电，负为充电')">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
            <el-row :gutter="10" type="flex" justify="space-between">
              <el-col :span="6" style="padding-left: 0">
                <el-form-item :prop="'pointList.' + index + '.startTime'" :rules="{
                  required: true,
                  message: $t('sim.startTime')
                }" label-width="0">
                  <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                    :picker-options="limitTimeCom(index, 'startTime')" :placeholder="$t('sim.startTime')"
                    style="width: 100%;"></el-time-picker>
                  <!-- <el-time-select v-model="item.startTime" :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '24:00',
        }"
                  :placeholder="$t('sim.startTime')" style="width: 100%;"></el-time-select> -->
                </el-form-item>
              </el-col>
              <el-col :span="0.5" class="icon-box">~</el-col>
              <el-col :span="6">
                <el-form-item :prop="'pointList.' + index + '.endTime'" :rules="{
                  required: true,
                  message: $t('sim.endTime')
                }" label-width="0">
                  <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                    :picker-options="limitTimeCom(index, 'endTime')" :placeholder="$t('sim.endTime')"
                    style="width: 100%;"></el-time-picker>
                  <!-- <el-time-select v-model="item.endTime" :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '24:00',
        }" :placeholder="$t('sim.endTime')" style="width: 100%;"></el-time-select> -->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :prop="'pointList.' + index + '.power'" :rules="{
                  required: true,
                  message: $t(`price['功率']`)
                }" label-width="0">
                  <el-input-number v-model="item.power" :precision="1" :placeholder="`${$t(`price['功率']`)}`"
                    class="input-number"></el-input-number>
                  <span class="suffix">kW</span>
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <el-form-item :prop="'pointList.' + index + '.enable'" :rules="{
                  required: true,
                  message: $t(`price['请选择使能']`)
                }" label-width="0">
                  <el-switch v-model="item.enable" :active-value="1" :inactive-value="0" style="margin-left: 20%;" />
                </el-form-item>
              </el-col>
              <el-col :span="1">
                <el-tooltip :content="$t('price.add')" placement="top">
                  <div class="icon-box" @click="handleTimeAdd"><i class="el-icon-circle-plus-outline icon"></i></div>
                </el-tooltip>
              </el-col>
              <el-col :span="1">
                <el-tooltip :content="$t('common.delete')" placement="top">
                  <div class="icon-box" @click="handleDeleteTime(index)"><i class="el-icon-circle-close icon"></i></div>
                </el-tooltip>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <span>{{ $t('注：') }} {{ $t('功率：分正负，正为放电，负为充电') }}</span>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('formRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="dialogLookVisible" center :modal-append-to-body="false" :width="$convertPx(900, 'rem')"
      :title="dialogLookTitle">
      <el-table :data="pointList">
        <el-table-column type="index" label="#" width="40" align="center">
        </el-table-column>
        <el-table-column prop="startTime" :label="$t('sim.startTime')" show-overflow-tooltip align="center">

          <template slot-scope="scope">
            <el-time-select v-model="scope.row.startTime" :picker-options="{
              start: '00:00',
              step: '01:00',
              end: '24:00'
            }" :placeholder="$t(`price['请选择开始时间']`)" style="width: 100%;" readonly></el-time-select>
          </template>
        </el-table-column>
        <el-table-column prop="endTime" :label="$t('sim.endTime')" show-overflow-tooltip align="center">

          <template slot-scope="scope">
            <el-time-select v-model="scope.row.endTime" :picker-options="{
              start: '00:00',
              step: '01:00',
              end: '24:00',
            }" :placeholder="$t(`price['请选择结束时间']`)" style="width: 100%;" readonly></el-time-select>
          </template>
        </el-table-column>
        <el-table-column prop="power" :label="`${$t(`price['功率']`)}(kW)`" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.power" :placeholder="$t(`price['请输入功率']`)" style="width: 100%" readonly />
          </template>
        </el-table-column>
        <el-table-column prop="enable" :label="$t(`backup['使能']`)" show-overflow-tooltip align="center">

          <template slot-scope="scope">
            <el-switch v-model="scope.row.enable" :active-value="1" :inactive-value="0"
              :active-text="$t(`backup['使能']`)" :inactive-text="$t(`backup['不使能']`)" disabled>
            </el-switch>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import _ from 'lodash'
import { jfpgList, addJfpg, editJfpg, deleteJfpg } from '@/api/operation/jfpg'

export default {
  data() {
    return {
      dialogVisible: false,
      dialogTitle: this.$t(`backup['添加方案']`),
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      form: {
        title: '',
        pointList: [
          {
            enable: '1',
            endTime: '',
            power: '',
            startTime: ''
          }
        ]
      },
      rules: {
        title: [
          { required: true, message: this.$t(`backup['请输入方案名称']`), trigger: 'blur' }
        ],
        pointList: [
          { required: true, message: this.$t(`price['请添加时段']`), trigger: 'blur' }
        ],
      },
      dialogLookVisible: false,
      dialogLookTitle: this.$t(`backup['方案名称']`),
      pointList: [],
      // 搜索
      searchKey: 'title',
      searchValue: '',
    };
  },
  computed: {
    limitTimeCom() {
      return (index, type) => {
        let options = {
          start: '00:00',
          step: '01:00',
          end: '24:00',
          minTime: undefined,
          selectableRange: '00:00:00 - 23:59:00'
        }
        if (index) {
          if (type == 'startTime') {
            // options.minTime = this.$moment(this.form.pointList[index - 1].endTime, 'HH:mm').subtract(1, 'hours').format('HH:mm')
            options.minTime = this.form.pointList[index - 1].endTime
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
          if (type == 'endTime') {
            options.minTime = this.form.pointList[index].startTime
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
        } else {
          if (type == 'startTime') options.minTime = undefined
          if (type == 'endTime') {
            options.minTime = this.form.pointList[0].startTime || '00:00'
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
        }
        return options
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      jfpgList({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        [this.searchKey]: this.searchValue
      }).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == this.$t(`backup['添加方案']`)) { // 添加
            this.addJfpgFn()
          } else { // 修改
            this.editJfpgFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogTitle = this.$t(`backup['添加方案']`)
      this.dialogVisible = true;
      this.form = {
        title: '',
        pointList: [
          {
            enable: 1,
            endTime: '',
            power: undefined,
            startTime: ''
          }
        ]
      }
      this.$nextTick(() => {
        this.resetForm('form')
      })
    },
    // 修改
    handleEditClick(row) {
      this.dialogTitle = this.$t(`backup['修改方案']`)
      this.dialogVisible = true;
      let data = _.cloneDeep(row)
      this.form = {
        ...data
      }
    },
    addJfpgFn() {
      addJfpg({
        title: this.form.title,
        pointList: this.form.pointList,
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Addition Failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Added successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    editJfpgFn() {
      editJfpg({
        title: this.form.title,
        pointList: this.form.pointList,
        id: this.form.id
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Change failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Modify successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteJfpg({
          cutTopIds: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    handleTimeAdd() {
      if (this.form.pointList.length == 12) return this.$message({
        type: 'danger',
        message: `${this.$t(`price['最多只能添加12条哦']`)}~`
      })
      this.form.pointList.push({
        enable: 1,
        endTime: '',
        power: '',
        startTime: ''
      })
    },
    handleDeleteTime(index) {
      if (this.form.pointList.length == 1) return this.$message({
        type: 'error',
        message: `${this.$t(`price['至少要有一条哦']`)}~`
      })
      this.form.pointList.splice(index, 1)
    },
    // 查看详情
    handleTitleClick(row) {
      this.dialogLookTitle = row.title
      this.dialogLookVisible = true
      this.pointList = row.pointList
    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;

  .input_box {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    &-title {
      width: 70%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .card-cont {
    /* max-height: 840px; */
    height: calc(100% - 80px);
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
  }

  .cont-item {
    margin-bottom: 20px;
    flex: 0 0 auto;
  }

  .cont-form {
    width: 100%;
    height: 310px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .icon-box {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .icon {
    font-size: 28px;
    display: block;
  }
}

.dialog-add-wrapper {
  max-height: 400px;
  overflow: auto;
  padding-right: 20px;
  margin-bottom: 20px;
}

::v-deep .el-divider {
  border-top: 1px #dcdfe6 dashed;
  background-color: #fff;
}
:deep(.el-card) {
  border: thin solid rgb(204, 204, 204);
  box-shadow: none !important;
  .el-card__header {
    border-bottom: thin solid rgb(204, 204, 204) !important;
  }
  .el-card__body, .el-main {
    padding-bottom: 0 !important;
  }
}
</style>
