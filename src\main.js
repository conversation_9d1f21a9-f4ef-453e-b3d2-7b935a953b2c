/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-23 11:48:36
 * @FilePath: \elecloud_platform-main\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Cookies from 'js-cookie'
import Element from 'element-ui'
import './assets/styles/element-variables.scss'
import moment from 'moment'
import 'moment/locale/zh-cn';
import i18n from './lang'
import { convertPx } from '@/utils'

// import BaiduMap from 'vue-baidu-map';
import * as VueGoogleMaps from 'vue2-google-maps'
import VueAMap from '@vuemap/vue-amap';
import '@vuemap/vue-amap/dist/style.css'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store, { useStore } from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'
import './assets/icons/font_3136660_ut3wbiszke8/iconfont.css'
import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree, decimalToBinaryReverseArray } from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'

moment.locale('zh-cn');

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.decimalToBinaryReverseArray = decimalToBinaryReverseArray
Vue.prototype.$moment = moment
Vue.prototype.$convertPx = convertPx
// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
DictData.install()
// use添加i18n
Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value)
})

if (!Cookies.get('language')) Cookies.set('language', 'en')

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.use(VueAMap);
VueAMap.initAMapApiLoader({
  key: '0e0ea84252884786ed02d4d88edf1464',
  securityJsCode: 'bb29c7963af5d67b34b5369e40baa4fa',
  plugins: ['AMap.Geocoder', 'AMap.Geolocation '],
  version: '1.4.15',
  Loca: { version: '1.3.2' }
});
Vue.use(VueGoogleMaps, {
  load: {
    key: 'AIzaSyACiZiVKfcxLy3rMaBn-ga9Q5pjVBwuIh8',
    libraries: 'places',
    language: Cookies.get('language'),
  }
});

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
