<script setup>
import { ref, getCurrentInstance, computed, onMounted } from 'vue'
import { useRoute, useStore } from '@/utils/vueApi.js'
import { list, uploadServiceData, deleteServiceData } from '@/api/monitors/history'
import { downloadOss, downloadOSSSingapore } from '@/api/operation/upgrade'
import { handleExport } from '@/utils/export'

const route = useRoute()
const store = useStore()
const { proxy } = getCurrentInstance()

const dataCom = computed(() => {
  let groupId = route.query.groupId.split(',')
  let data = []
  groupId.forEach((item, index) => {
    let label = ''
    if (index == 0) {
      label = `${proxy.$t(`monitor['主机']`)}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
    } else {
      label = `${proxy.$t(`monitor['从机']`)}-${index < 10 ? '0' + index : index}`
    }
    data.push({
      label,
      id: item
    })
  })
  return data
})
const currentKey = ref(route.query.groupId.split(',')[0])
const currentNodeKey = computed({
  get: () => {
    return currentKey.value
  },
  set: (newValue) => {
    currentKey.value = newValue
  }
})
const currentNode = computed(() => {
  return currentKey.value ? dataCom.value.find(item => item.id == currentKey.value) : dataCom.value[0]
})
const handleNodeClick = (node) => {
  currentKey.value = node.id
  currentNode.value = node
  getList()
}
onMounted(() => {
  getList()
})

const tableData = ref([])
const total = ref(10)
const queryInfo = ref({
  pageNum: 1,
  pageSize: 10,
  ac: undefined
})
const loading = ref(true)

const getList = async () => {
  loading.value = true
  const res = await list({ ...queryInfo.value, ac: currentNodeKey.value })
  if (res.code !== 200) return proxy.$message({
    type: 'error',
    message: res.msg
  })
  let data = res.rows
  tableData.value = data
  total.value = res.total
  loading.value = false
}

const handleAddClick = () => {
  ruleForm.value = {
    bucketname: '',
    fileName: '',
    endpoint: 1,
    fileType: 2
  }
  dialogVisible.value = true
}

const getEndpointText = computed(() => {
  return (type) => {
    return type == 1 ? proxy.$t('深圳') : proxy.$t('新加坡')
  }
})

const dialogVisible = ref(false)
const ruleForm = ref({
  bucketname: '',
  fileName: '',
  endpoint: 1,
  fileType: 2
})
const rules = ref({
  fileName: [
    { required: true, message: proxy.$t(`oss['请输入文件名称']`), trigger: 'blur' }
  ],
  bucketname: [
    { required: true, message: proxy.$t('common.select'), trigger: 'blur' }
  ],
})
const handleCancelClick = () => {
  dialogVisible.value = false
}
const handleConfirm = (formName) => {
  proxy.$refs[formName].validate(async (valid) => {
    if (valid) {
      const res = await uploadServiceData({
        ac: currentNodeKey.value,
        bucketname: ruleForm.value.bucketname,
        fileName: ruleForm.value.fileName,
        endpoint: ruleForm.value.bucketname == 'elecod-oss' ? 'http://oss-cn-shenzhen.aliyuncs.com' : 'http://oss-ap-southeast-1.aliyuncs.com',
        fileType: 2
      })
      if (res.code !== 200) return proxy.$message({
        type: 'error',
        message: res.msg
      })
      dialogVisible.value = false
      getList()
    }
  })
}

const handleDownClick = async (row) => {
  let api = row.endpoint == 1 ? downloadOss : downloadOSSSingapore
  proxy.$modal.loading(`${proxy.$t(`oss['正在下载文件，请稍候']`)}...`);
  api({ filePath: row.filePath }).then(res => {
    if (res.code) if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: res.msg
    })
    handleExport(res, `${row.fileName}.db`, '')
    proxy.$modal.closeLoading()
    proxy.$message({
      type: 'success',
      message: proxy.$t(`oss['下载文件成功']`)
    })
  }).catch(() => {
    proxy.$modal.closeLoading()
  })
}

const handleDeleteClick = (row) => {
  proxy.$confirm(proxy.$t(`menu['Are you sure to delete the data item?']`), proxy.$t('common.systemPrompt'), {
    confirmButtonText: proxy.$t('common.confirm'),
    cancelButtonText: proxy.$t('common.cancel'),
    type: 'warning'
  }).then(() => {
    deleteServiceData(row.id).then(res => {
      if (res.code !== 200) return proxy.$message({
        type: 'error',
        message: proxy.$t(`common['Deleted Failed']`)
      });
      getList()
      proxy.$message({
        type: 'success',
        message: proxy.$t(`common['Deleted successfully']`)
      });
    })
  }).catch(() => {
    proxy.$message({
      type: 'info',
      message: proxy.$t(`common['Deletion Cancelled']`)
    });
  });
}

const isSend = computed(() => {
  let control = function () {
    let currentNodeKey = 0
    let data = store.state.monitor.groupList[0]
    if (data && Object.keys(data).length == 11) return data.control
    return {
      ac: "null",
      createTime: null,
      isAnalysis: 0,
      jk_1000: null,
      jk_1001: null,
      jk_1002: null,
      jk_1003: null,
      jk_1004: null,
      jk_1005: null,
      jk_1015: null,
      jk_1016: null,
      jk_1017: null,
      jk_1031: null,
      jk_1032: null,
      jk_1033: null,
      jk_1051: null,
      jk_1056: null,
      jk_1074: null,
      jk_1077: null,
      jk_1092: null,
      jk_1093: null,
      jk_1094: null,
      jk_1095: null,
      jk_1105: null,
      jk_1106: null,
      onLineState: "离线",
      sdt: null,
    }
  }
  let controlData = control()
  if (_.isEmpty(controlData)) return true
  return controlData['onLineState'] == '离线'
})
</script>

<template>
  <div class="cont">
    <div class="cont-left">
      <el-tree :data="dataCom" @node-click="handleNodeClick" node-key="id" class="tree"
        :current-node-key="currentNodeKey" ref="treeRef">
        <div class="tree-title" slot-scope="{ node }">
          <div>{{ node.label }}</div>
        </div>
      </el-tree>
    </div>

    <div class="cont-right">
      <div class="input_box">
        <div>
          <div class="input_ment">
            <el-button type="primary" @click="handleAddClick()" icon="el-icon-add" :disabled="isSend">
              {{ $t('获取数据') }}
              <el-tooltip class="item" effect="dark" :content="$t('设备离线，不可获取')" placement="bottom" v-if="isSend">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-button>
          </div>
        </div>
      </div>

      <div class="table_box">
        <el-table v-loading="loading" :data="tableData" class="table" stripe style="width: 100%;"
          :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
          :cell-style="{ 'text-align': 'center', 'font-size': '14px' }">
          <el-table-column type="index" label="#" width="60">
          </el-table-column>
          <el-table-column prop="ac" :label="$t('alarm.sn')" show-overflow-tooltip />
          <el-table-column prop="fileName" :label="$t(`oss['文件名称']`)" show-overflow-tooltip />
          <el-table-column prop="endpoint" :label="$t(`oss['上传地区']`)" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag type="primary">
                {{ getEndpointText(scope.row.endpoint) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="filePath" :label="$t(`oss['文件路径']`)" show-overflow-tooltip />
          <el-table-column prop="status" :label="$t(`log['执行结果']`)" show-overflow-tooltip class-name="alarm-state">
          <template slot-scope="scope">
              <div class="solve" v-if="scope.row.status == 0">
                <svg-icon icon-class="solve" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t(`oss['上传成功']`) }}</span>
              </div>
              <div class="solve" v-else-if="scope.row.status == 1">
                <svg-icon icon-class="solve-off" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t(`oss['上传失败']`) }}</span>
              </div>
              <div class="solve" v-else>
                <svg-icon icon-class="await" style="width: 1.3em;height: 1.3em;" />
                <span class="el-icon--right">{{ $t('上传中') }}</span>
              </div>
          </template>
        </el-table-column>
          <el-table-column prop="nickName" :label="$t('创建人员')" show-overflow-tooltip />
          <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip />
          <el-table-column fixed="right" :label="$t('common.handle')" width="150">
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="scope.row.status == 1 ? $t('文件上传失败，不可下载或者删除'): $t('文件上传中，不可下载或者删除')" placement="bottom" v-if="scope.row.status !== 0">
              <div>
                <el-button @click="handleDownClick(scope.row)" disabled type="text" size="small">{{ $t(`oss['下载']`) }}</el-button>
              <el-button @click="handleDeleteClick(scope.row)" disabled type="text" size="small">{{ $t('common.delete')
                }}</el-button>
              </div>
            </el-tooltip>
            <div v-else>
              <el-button @click="handleDownClick(scope.row)" type="text" size="small">{{ $t(`oss['下载']`) }}</el-button>
              <el-button @click="handleDeleteClick(scope.row)" type="text" size="small">{{ $t('common.delete')
                }}</el-button>
            </div>
          </template>
        </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
          @pagination="getList" style="margin-top: 20px;text-align: right;" />
      </div>

      <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px"
        :title="$t(`oss['上传文件']`)">
        <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
          <el-form-item :label="$t('alarm.sn')" prop="ac">
            <span>{{ currentNode.label }}_{{ currentNode.id }}</span>
          </el-form-item>
          <el-form-item :label="$t(`oss['上传地区']`)" prop="bucketname">
            <el-radio-group v-model="ruleForm.bucketname" style="width: 100%">
              <el-radio label="elecod-oss">{{ $t('深圳') }}</el-radio>
              <el-radio label="elecod-oss-global">{{ $t('新加坡') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t(`oss['文件名称']`)" prop="fileName">
            <el-input v-model="ruleForm.fileName" :placeholder="$t(`oss['请输入文件名称']`)" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>

</template>

<style lang="scss" scoped>
.cont {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  display: flex;

  .cont-left {
    flex: 1;
  }

  .cont-right {
    flex: 7;
  }

  .table_box {
    padding-bottom: 10px;
    .solve {
      display: flex;
      align-items: center;
    }
  }
}
.tree {
  height: 100%;
  border-right: 1px solid #D6D6D6;
  margin-right: 12px;
}
.tree-title {
  padding: 10px;
}
::v-deep .el-tree-node__content {
  height: auto;
}
::v-deep .is-current {
  font-weight: bold;
  color: var(--base-color);
  background-color: #F5F7FA;
  padding-right: 20px;
}
::v-deep .alarm-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
