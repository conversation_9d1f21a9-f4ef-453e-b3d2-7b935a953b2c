<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '95%'
    },
    xData: {
      type: Array,
      required: true
    },
    yData: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    yData: {
      deep: true,
      handler(newValue, oldValue) {
        if (this.yData.length) this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      if (this.chart) this.chart.dispose()
      this.chart = echarts.init(this.$el)
      this.chart.clear();
      let options = {
        title: {
          subtext: `${this.$t('common.unit')}：kWh`,
          top: '2%'
        },
        xAxis: {
          data: this.xData,
          axisTick: {
            show: false
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10],
          formatter: (params) => {
            let htmlStart = `<div><div>${params[0].name}</div>`
            params.forEach((param, index) => {
              if (!this.yData.length) return
              let name = null
              let dc = this.yData[index].module
              if (dc.indexOf('13') !== -1) {
                name = `${parseInt(dc) - 131000 + 1}#Monet-AC`
              } else if (dc.indexOf('14') !== -1) {
                name = `${parseInt(dc) - 141000 + 1}#Monet-DC`
              } else if (dc.indexOf('16') !== -1) {
                name = `${parseInt(dc) - 161000 + 1}#BMS`
              } else {
                name = null
              }
              htmlStart += `
                <div style="display: flex;align-items: center;">
                  <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                  ${this.yData[index].ac}${name !== null ? ' '+'-'+' '+name: ''} - ${param.seriesName}：
                  <span style="font-weight: 600;margin-right: 3px;">${param.value !== null ? param.value : '--'}</span> ${this.yData[index].unit}
                </div>`
            })
            return htmlStart + '</div>'
          }
        },
        dataZoom: [
          {
            type: 'inside',
          }
        ],
        grid: {
          left: '2%',
          right: '2%',
          bottom: '1%',
          top: '8%',
          containLabel: true
        },
        yAxis: {
          type: 'value',
          splitLine: { show: false },
        },
        legend: {
          type: 'plain',
          type: 'scroll',
          top: '0'
        },
        series: []
      }
      this.yData.forEach(item => {
        options.series.push({
          name: item.name,
          itemStyle: {  // 使用方法二的写法
            // color: {
            //   type: 'linear',
            //   x: 0,  //右
            //   y: 0,  //下
            //   x2: 0,  //左
            //   y2: 1,  //上
            //   colorStops: [
            //     {
            //       offset: 0,
            //       color: '#0DB0F5'
            //     },
            //     {
            //       offset: 0.5,
            //       color: '#2499EE'
            //     },
            //     {
            //       offset: 1,
            //       color: '#8477FF'
            //     }
            //   ]
            // }
          },
          smooth: true,
          type: 'bar',
          barWidth: 10,
          data: item.values,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        })
      })
      options && this.chart.setOption(options)
    }
  }
}
</script>
