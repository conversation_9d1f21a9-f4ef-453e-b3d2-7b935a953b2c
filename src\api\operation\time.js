import request from '@/utils/request'

// 获取列表
export function timeList(queryInfo) {
  return request({
    url: '/system/zone/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addTime(data) {
  return request({
    url: '/system/zone',
    method: 'post',
    data
  })
}

// 修改
export function editTime(data) {
  return request({
    url: '/system/zone',
    method: 'put',
    data
  })
}

// 删除
export function deleteTime(queryInfo) {
  return request({
    url: `/system/zone/${queryInfo.id}`,
    method: 'delete'
  })
}
