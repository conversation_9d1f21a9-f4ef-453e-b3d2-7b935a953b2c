<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-03-15 11:29:00
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="param-box">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" style="overflow: auto;">
      <el-form-item :label="`${$t(`param['下发状态']`)}：`" prop="status" style="width: 50%">
        <span slot="label">
          {{ $t(`param['下发状态']`) }}
          <el-tooltip class="item" effect="dark" placement="bottom">
            <div slot="content" style="line-height: 1.5">
              &emsp;{{ $t(`param['未下发']`) }}：{{ $t(`param['该类参数从未下发']`) }};
              <br />
              &emsp;{{ $t(`param['下发中']`) }}：{{ $t(`param['参数已成功下发至设备，执行未知，请等待']`) }};
              <br />
              {{ $t(`param['下发成功']`) }}：{{ $t(`param['参数已成功下发至设备并已执行成功']`) }};
              <br />
              {{ $t(`param['下发失败']`) }}：{{ $t(`param['参数已成功下发至设备，设备并未执行成功']`) }}
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-link :underline="false" v-if="form.status == 0">{{ $t(`param['未下发']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 1" type="success">{{ $t(`param['下发成功']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 2" type="primary">{{ $t(`param['下发中']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 3" type="danger">{{ $t(`param['下发失败']`) }}</el-link>
      </el-form-item>
      <el-form-item :label="`${$t(`param['开关机']`)}`" prop="setting1899" style="width: 50%">
        <span slot="label">
          {{ $t(`param['开关机']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('控制系统按其工作模式启停。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-radio-group v-model="form.setting1899" style="width: 100%">
          <el-radio label="1">{{ $t(`param['开机']`) }}</el-radio>
          <el-radio label="0">{{ $t(`param['关机']`) }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="box-footer">
      <el-divider></el-divider>
      <!-- <el-button :style="{ width: $convertPx(100, 'rem') }" @click="handleSaveClick()">保存</el-button> -->
      <el-button :style="{ width: $convertPx(100, 'rem') }" @click="getInfo">{{
    $t(`tagsView.refresh`) }}</el-button>
      <el-button type="primary" :style="{ width: $convertPx(100, 'rem') }" @click="handleSendClick"
        :disabled="isSend">{{
    $t(`param['下发']`) }}</el-button>
    </div>


    <el-dialog :visible.sync="dialogVisible" width="20%" custom-class="result-dialog" :modal-append-to-body="false">
      <span slot="title"></span>
      <el-result icon="success" :subTitle="$t(`param['参数已下发至设备']`)">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="handleResultClick">{{ $t(`param['查看执行结果']`) }}</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed, onUnmounted } from 'vue'
import { useStore, useRoute, useRouter } from '@/utils/vueApi.js'
import _ from 'lodash'

const { proxy } = getCurrentInstance()

const store = useStore()
const route = useRoute()
const router = useRouter()

const form = ref({
  setting1899: "1",
  ac: route.query.id,
  status: 0
})
const rules = ref({})

/**
 * 保存
 */
const handleSaveClick = () => {
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      const api = store.state.param.onOffInfo ? 'editOnOffFn' : 'addOnOffFn'
      store.dispatch(`param/${api}`, form.value).then(async (res) => {
        proxy.$message({
          type: 'success',
          message: proxy.$t(`param['保存成功']`)
        })
        await getInfo()
      })
    }
  });
}

// 下发
const handleSendClick = () => {
  if (form.value.status == 2) return proxy.$message({
    type: 'warning',
    message: proxy.$t(`param['正在下发中，请稍后再下发']`)
  })
  proxy.$modal.loading(`${proxy.$t(`param['正在下发中']`)}...`);
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      store.dispatch('param/sendParamOnOffFn', {
        ac: form.value.ac,
        id: form.value.id,
        setting1899: form.value.setting1899,
      }).then(async res => {
        // dialogVisible.value = true
        proxy.$modal.closeLoading()
      }).catch(() => {
        proxy.$modal.closeLoading()
      })
    }
  })
}

const dialogVisible = ref(false)

/**
 * 查看结果
 */
const handleResultClick = () => {
  let routeData = router.resolve({
    path: '/operation/log/instruct',
  });
  window.open(routeData.href, '_blank');
}

const getInfo = async () => {
  const res = await store.dispatch('param/onOffInfoFn', { ac: route.query.id })
  if (!res) return
   let data = _.cloneDeep(form.value)
    if (res.status == 1 && data.status == 2) {
      proxy.$message({
        type: 'success',
          message: proxy.$t(`param['下发成功']`)
        })
    } else if (res.status == 3 && data.status == 2) {
      proxy.$message({
        type: 'error',
        message: proxy.$t(`param['下发失败']`)
      })
    }
  form.value = {
    ...res,
  }
}

const isSend = computed(() => {
  if (_.isEmpty(store.state.monitor.control)) return true
  return store.state.monitor.control['onLineState'] == '离线'
})

const time = ref(null)
onMounted(() => {
  getInfo()

  // time.value = setInterval(() => {
  //   getInfo()
  // }, 10000)
})
onUnmounted(() => {
  clearInterval(time.value)
})
</script>

<style lang="scss" scoped>
.icon-box {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon {
  font-size: 28px;
  display: block;
}

:deep(.result-dialog .el-dialog__header) {
  border-bottom: none;
}
</style>
