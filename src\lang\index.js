/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-01-13 09:28:31
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-03-28 18:10:52
 * @FilePath: \elecloud_platform-main\src\lang\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// index.js
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'// element-ui lang
import elementItLocale from 'element-ui/lib/locale/lang/it'
import enLocale from './en'
import zhLocale from './zh'
import itLocale from './it'

Vue.use(VueI18n)

const messages = {
  en: {
    ...enLocale,
    ...elementEnLocale
  },
  zh: {
    ...zhLocale,
    ...elementZhLocale
  },
  it: {
    ...itLocale,
    ...elementItLocale
  }
}

const i18n = new VueI18n({
  // 设置语言 选项 en | zh
  locale: Cookies.get('language') || 'en',
  // 设置文本内容
  messages
})

export const languageOptions = [
  {
    value: 'zh',
    label: '中文简体',
    api: 'zh_CN'
  },
  {
    value: 'en',
    label: 'English',
    api: 'en_US'
  },
  {
    value: 'it',
    label: 'ltaliano',
    api: 'en_IT'
  }
]

export const t = i18n.t

export default i18n
