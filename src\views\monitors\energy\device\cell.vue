<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:01
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-08 14:31:28
 * @FilePath: \elecloud_platform-main\src\views\monitors\energy\device\cell.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="home">
    <template v-if="isShowCell == 1">
      <div class="cluster-box">
        <template v-for="(item, index) in cell">
          <div v-for="(clusterItem, clusterIndex) in item.clusterData" :key="`${item.name}-${clusterIndex + 1}`" class="cluster-item">
            <div class="cluster-item-top">
              <div style="font-weight: bold;">{{ `${item.name}-${clusterIndex + 1}#${$t('包')}` }}</div>
              <el-button type="primary" size="small" style="padding-top: 6px;padding-bottom: 6px;"
                @click="handleClusterClick(index, clusterIndex, `${item.name}-${clusterIndex + 1}#${$t('包')}`)">
                >> {{ $t('详情') }}
              </el-button>
            </div>
            <div class="cluster-item-cell" v-if="clusterItem.positive">
              <div class="cluster-item-color">{{ $t('簇级传感器1') }}</div>
              <div>{{ clusterItem.positive }}℃</div>
            </div>
            <div class="cluster-item-cell mt-12" v-if="clusterItem.negative">
              <div class="cluster-item-color">{{ $t('簇级传感器2') }}</div>
              <div>{{ clusterItem.negative }}℃</div>
            </div>
            <div class="cluster-item-cell mt-12">
              <div class="cluster-item-color">{{ $t('最高单体电压') }}</div>
              <div>{{ clusterItem.maxVoltage }}V</div>
            </div>
            <div class="cluster-item-cell mt-12">
              <div class="cluster-item-color">{{ $t('最低单体电压') }}</div>
              <div>{{ clusterItem.minVoltage }}V</div>
            </div>
            <div class="cluster-item-cell mt-12">
              <div class="cluster-item-color">{{ $t('最高单体温度') }}</div>
              <div>{{ clusterItem.maxTemperature }}℃</div>
            </div>
            <div class="cluster-item-cell mt-12">
              <div class="cluster-item-color">{{ $t('最低单体温度') }}</div>
              <div>{{ clusterItem.minTemperature }}℃</div>
            </div>
          </div>
        </template>
      </div>
    </template>
    <template v-else-if="isShowCell == 2">
      <div class="cell-box-top">
        <div style="font-weight: bold;font-size: 16px;">{{ currentName }}</div>
        <el-button type="primary" size="small" @click="handleBackClick">
          {{ $t('common.back') }}
        </el-button>
      </div>
      <div style="display: flex;flex-wrap: wrap;">
        <div v-for="(item, index) in cellData" :key="index + 'cell'" style="width: 10%;margin: 13px;">

          <div class="cell-item">
            <div class="cell-item-top">
            </div>
            <div class="cell-item-cont">
              <div class="cell-item-cont-row" v-if="!isEmpty(cell[currentBmsIndex].bms_7101_7612)">
                <div class="cell-item-cont-color">{{ $t('电压') }}：</div>
                <div>
                  <span style="font-weight: 600">{{ item.cellVoltage }}</span>
                  V
                </div>
              </div>
              <div class="cell-item-cont-row" v-if="!isEmpty(cell[currentBmsIndex].bms_7613_8124)">
                <div class="cell-item-cont-color">{{ $t('温度') }}：</div>
                <div>
                  <span style="font-weight: 600">{{
                    item.cellTemperature
                    }}</span>
                  ℃
                </div>
              </div>
              <div class="cell-item-cont-row" v-if="!isEmpty(cell[currentBmsIndex].bms_9150_9661)">
                <div class="cell-item-cont-color">{{ $t('温升') }}：</div>
                <div>
                  <span style="font-weight: 600">{{
                    item.cellTemperatureRise
                    }}</span>
                  ℃
                </div>
              </div>
              <div class="cell-item-cont-row" v-if="!isEmpty(cell[currentBmsIndex].bms_8125_8637)">
                <div class="cell-item-cont-color">{{ $t('电流') }}：</div>
                <div>
                  <span style="font-weight: 600">{{
                    item.cellCurrent
                    }}</span>
                  A
                </div>
              </div>
              <div class="cell-item-cont-row" v-if="!isEmpty(cell[currentBmsIndex].bms_8638_9149)">
                <div class="cell-item-cont-color">{{ $t('阻抗') }}：</div>
                <div>
                  <span style="font-weight: 600">{{
                    item.cellImpedance
                    }}</span>
                  Ω
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="isShowCell == 0">
      <div class="cell-info">{{ $t('暂未配置电池电芯规格信息，请联系管理员或售后人员。') }}</div>
    </template>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { useStore, useRoute } from '@/utils/vueApi.js'
import { isEmpty } from 'lodash'

const store = useStore()

const cell = computed(() => {
  let data = store.state.monitor.pcs_cell
  data.forEach(item => {
    item.name = `${parseInt(item.dc) - 161000 + 1}#BMS`
  })
  return data
})
const isShowCell = computed({
  get() {
    return store.state.monitor.isShowCell
  },
  set(value) {
    store.commit('SET_ISSHOWCELL', value)
  }
})

const currentBmsIndex = ref(0)
const currentClusterIndex = ref(0)
const currentName = ref()
const cellData = computed(() => {
  if (cell.value.length) {
    return cell.value[currentBmsIndex.value]?.clusterData?.length ? cell.value[currentBmsIndex.value]?.clusterData[currentClusterIndex.value].data : []
  }
  return []
})
const handleClusterClick = (index, clusterIndex, name) => {
  currentBmsIndex.value = index
  currentClusterIndex.value = clusterIndex
  currentName.value = name
  isShowCell.value = 2
}
const handleBackClick = () => {
  isShowCell.value = 1
}
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
  overflow: auto;
  font-size: 14px;
  color: var(--base-color);
  line-height: 1.5;
}

.cluster-box {
  display: flex;
  flex-wrap: wrap;
}

.cluster-item {
  width: 30%;
  margin: 20px 0;
  text-align: left;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  margin-right: 20px;
  padding: 20px 24px;

  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  &-cell {
    display: flex;
    justify-content: space-between;
  }

  &-color {
    color: #4b5563
  }
}

.mt-12 {
  margin-top: 12px
}

.cell-box-top {
  display: flex;
  justify-content: space-between;
  align-items: center
}

.cell-item {
  position: relative;
  width: 100%;
  height: 140px;
  display: flex;
  align-items: center;
  flex-direction: column;

  &-top {
    width: 40%;
    height: 15.5%;
    background-color: #eee;
    border-radius: 20px;
    position: absolute;
    top: 0;
    left: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-cont {
    width: 100%;
    height: 85%;
    background-color: #eee;
    border-radius: 20px;
    margin-top: 10%;
    padding-top: 10%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-evenly;

    &-row {
      display: flex;
      align-items: center;
    }

    &-color {
      color: #757575
    }
  }
}
.cell-info {
  text-align: center;
  height: 400px;
  line-height: 400px;
}
</style>
