import request from '@/utils/request'

// 查询公告列表
export function listNotice(query) {
  return request({
    url: '/system/notice/list',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return request({
    url: '/system/notice/' + noticeId,
    method: 'get'
  })
}

// 新增公告
export function addNotice(data) {
  return request({
    url: '/system/notice',
    method: 'post',
    data: data
  })
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: '/system/notice',
    method: 'put',
    data: data
  })
}

// 删除公告
export function delNotice(noticeId) {
  return request({
    url: '/system/notice/' + noticeId,
    method: 'delete'
  })
}

// 获取当前登录用户的未读公告数
export function getUnreadNoticeCount() {
  return request({
    url: '/system/notice/selectByUnreadCount',
    method: 'get'
  })
}

// 获取当前登录用户的未读公告
export function getUnreadNoticeList(queryInfo) {
  return request({
    url: '/system/notice/listNoticeRecipients',
    method: 'get',
    params: queryInfo
  })
}

// 已读公告
export function readNotice(ids) {
  return request({
    url: `/system/notice/editRead/${ids}`,
    method: 'get',
  })
}