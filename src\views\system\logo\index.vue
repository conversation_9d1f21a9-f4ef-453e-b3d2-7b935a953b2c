<template>
  <!-- SIM列表 -->
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t('域名')" style="width: 200px;" v-model="queryInfo.sim" clearable></el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
          }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus" v-hasPermi="['system:logo:add']">{{
            $t('common.add') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="domainName" :label="$t('域名')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.domainName }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.domainName"
              v-clipboard:success="copySuccess"></i>
          </template>
        </el-table-column>
        <el-table-column prop="title" :label="$t('中文网站名称')" show-overflow-tooltip align="center" />
        <el-table-column prop="titleUs" :label="$t('英文网站名称')" show-overflow-tooltip align="center" />
        <el-table-column prop="homeLogoPath" :label="$t('登录logo')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-image :style="{ width: $convertPx(100, 'rem'), height: $convertPx(60, 'rem') }"
              :src="`${origin}/prod-api/${scope.row.homeLogoPath}`"
              :preview-src-list="[`${origin}/prod-api/${scope.row.homeLogoPath}`]">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="appbarLogoPath" :label="$t('导航栏logo')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-image :style="{ width: $convertPx(100, 'rem'), height: $convertPx(60, 'rem') }"
              :src="`${origin}/prod-api/${scope.row.appbarLogoPath}`"
              :preview-src-list="[`${origin}/prod-api/${scope.row.appbarLogoPath}`]">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button @click="handleEditClick(scope.row)" type="text" size="small" v-hasPermi="['system:logo:edit']">{{
              $t('common.edit') }}</el-button>
            <el-button @click="handleThemeClick(scope.row)" type="text" size="small"
              v-hasPermi="['system:logo:edit']">{{
                $t('主题') }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small"
              v-hasPermi="['system:logo:remove']">{{ $t('common.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>
    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px" :title="dialogTitle">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
        <el-form-item :label="$t(`域名`)" prop="domainName">
          <el-input v-model="ruleForm.domainName" :placeholder="$t('域名')" />
        </el-form-item>
        <el-form-item :label="$t(`中文网站名称`)" prop="title">
          <el-input v-model="ruleForm.title" :placeholder="$t(`中文网站名称`)" />
        </el-form-item>
        <el-form-item :label="$t(`英文网站名称`)" prop="titleUs">
          <el-input v-model="ruleForm.titleUs" :placeholder="$t(`英文网站名称`)" />
        </el-form-item>
        <el-form-item :label="$t(`中文大屏标题`)" prop="bigScreenCn">
          <el-input v-model="ruleForm.bigScreenCn" :placeholder="$t(`中文大屏标题`)" />
        </el-form-item>
        <el-form-item :label="$t(`英文大屏标题`)" prop="bigScreenUs">
          <el-input v-model="ruleForm.bigScreenUs" :placeholder="$t(`英文大屏标题`)" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t(`宽度`)" prop="width">
              <el-input v-model="ruleForm.logoWidth" :placeholder="$t('宽度')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t(`高度`)" prop="height">
              <el-input v-model="ruleForm.logoHeight" :placeholder="$t('高度')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t(`登录logo`)" prop="homeLogoPath">
          <file-upload :value="ruleForm.homeLogoPath" @input="getLogin" size="130 x 65px" :limit="1" :fileSize="20"
            :fileType="['png', 'jpeg', 'jpg']"></file-upload>
        </el-form-item>
        <el-form-item :label="$t(`导航栏logo`)" prop="appbarLogoPath">
          <file-upload :value="ruleForm.appbarLogoPath" @input="getBar" size="128 x 128px" :limit="1" :fileSize="20"
            :fileType="['ico']"></file-upload>
        </el-form-item>
        <el-form-item :label="$t(`中文大屏logo`)" prop="bigScreenLogoPathCn">
          <file-upload :value="ruleForm.bigScreenLogoPathCn" @input="getScreenCn" size="200 x 55px" :limit="1"
            :fileSize="20" :fileType="['png', 'jpeg', 'jpg']"></file-upload>
        </el-form-item>
        <el-form-item :label="$t(`英文大屏logo`)" prop="bigScreenLogoPathUs">
          <file-upload :value="ruleForm.bigScreenLogoPathUs" @input="getScreenUs" size="200 x 55px" :limit="1"
            :fileSize="20" :fileType="['png', 'jpeg', 'jpg']"></file-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="dialogThemeVisible" center :modal-append-to-body="false" width="820px" :title="'主题'">
      <el-form :model="themeForm" label-width="auto">
        <el-row>
          <el-col :span="8">
            <el-form-item :label="$t(`主题颜色`)">
              <el-color-picker v-model="themeForm['--primary-color']"
                :predefine="['#0093b6', '#1890ff', '#304156', '#2881', '#11a983', '#13c2c2', '#6959CD', '#f5222d',]"
                class="theme-picker" popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t(`侧边栏活跃背景色`)" prop="--menu-back-ground-active">
              <el-color-picker v-model="themeForm['--menu-back-ground-active']" class="theme-picker"
                popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t(`侧边栏背景颜色`)" prop="--menu-back-ground">
              <el-color-picker v-model="themeForm['--menu-back-ground']" class="theme-picker"
                popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t(`侧边栏logo背景颜色`)" prop="--menu-logo-back-ground">
              <el-color-picker v-model="themeForm['--menu-logo-back-ground']" class="theme-picker"
                popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t(`侧边栏文字颜色`)" prop="--base-menu-color">
              <el-color-picker v-model="themeForm['--base-menu-color']" class="theme-picker"
                popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t(`侧边栏文字活跃颜色`)" prop="--base-menu-color-active">
              <el-color-picker v-model="themeForm['--base-menu-color-active']" class="theme-picker"
                popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t(`页面文字颜色`)" prop="--base-color">
              <el-color-picker v-model="themeForm['--base-color']" class="theme-picker"
                popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <!-- 基本图标 -->
          <el-col :span="8">
            <el-form-item :label="`capacityPower`" prop="capacityPower">
              <el-color-picker v-model="baseForm.capacityPower" class="theme-picker"
                popper-class="theme-picker-dropdown" style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`charge`" prop="charge">
              <el-color-picker v-model="baseForm.charge" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`discharge`" prop="discharge">
              <el-color-picker v-model="baseForm.discharge" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`ratedPower`" prop="ratedPower">
              <el-color-picker v-model="baseForm.ratedPower" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`capacity`" prop="capacity">
              <el-color-picker v-model="baseForm.capacity" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`guangfu`" prop="guangfu">
              <el-color-picker v-model="baseForm.guangfu" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`device`" prop="device">
              <el-color-picker v-model="baseForm.device" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`moneySum`" prop="moneySum">
              <el-color-picker v-model="baseForm.moneySum" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`running`" prop="running">
              <el-color-picker v-model="baseForm.running" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <!-- 流动图标 -->
          <el-col :span="8">
            <el-form-item :label="`flow_pv`" prop="flow_pv">
              <el-color-picker v-model="flowForm.flow_pv" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`flow_load`" prop="flow_load">
              <el-color-picker v-model="flowForm.flow_load" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`flow_pe`" prop="flow_pe">
              <el-color-picker v-model="flowForm.flow_pe" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`flow_en`" prop="flow_en">
              <el-color-picker v-model="flowForm.flow_en" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`flow_de`" prop="flow_de">
              <el-color-picker v-model="flowForm.flow_de" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`flow_dc`" prop="flow_dc">
              <el-color-picker v-model="flowForm.flow_dc" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`flow_ac`" prop="flow_ac">
              <el-color-picker v-model="flowForm.flow_ac" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="`flow_bt`" prop="flow_bt">
              <el-color-picker v-model="flowForm.flow_bt" class="theme-picker" popper-class="theme-picker-dropdown"
                style="display: flex;align-items: center;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogThemeVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button @click="handleResetClick">{{ $t('common.reset') }}</el-button>
        <el-button type="primary" @click="handleThemeConfirm()">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { logoList, addLogo, editLogo, deleteLogo } from '@/api/system/logo'
import ThemePicker from '@/components/ThemePicker'

export default {
  components: { ThemePicker },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '添加域名',
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      ruleForm: {
        domainName: '',
        logoPathCn: '',
        logoPathUs: '',
        homeLogoPath: '',
        logoWidth: '',
        logoHeight: ''
      },
      rules: {
        domainName: [
          { required: true, message: this.$t('域名'), trigger: 'blur' }
        ]
      },
      // 主题
      dialogThemeVisible: false,
      themeForm: {
        '--primary-color': '',
        '--menu-back-ground-active': '',
        '--base-menu-color': '',
        '--base-menu-color-active': '',
        '--base-color': '',
      },
      baseForm: {
        running: '',
        moneySum: '',
        device: '',
        guangfu: '',
        capacity: '',
        ratedPower: '',
        discharge: '',
        charge: '',
        capacityPower: '',
      },
      flowForm: {
        flow_pv: '',
        flow_load: '',
        flow_pe: '',
        flow_en: '',
        flow_de: '',
        flow_dc: '',
        flow_ac: '',
        flow_bt: '',
      }
    };
  },
  mounted() {
    this.getList()
    this.origin = window.location.origin
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      logoList(this.queryInfo).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      console.log(this.ruleForm);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == this.$t('添加域名')) { // 添加
            this.addLogoFn()
          } else { // 修改
            this.editLogoFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogTitle = this.$t('添加域名')
      this.dialogVisible = true;
      this.ruleForm = {
        domainName: '',
        logoPathCn: '',
        logoPathUs: '',
        homeLogoPath: '',
        title: '',
        titleUs: '',
        bigScreenCn: '',
        bigScreenUs: '',
        bigScreenLogoPathCn: '',
        bigScreenLogoPathUs: '',
        logoWidth: '',
        logoHeight: ''
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    // 修改
    handleEditClick(row) {
      this.dialogTitle = this.$t('修改域名')
      this.dialogVisible = true;
      this.ruleForm = {
        ...row
      }
    },
    addLogoFn() {
      addLogo({
        domainName: this.ruleForm.domainName,
        logoPathCn: this.ruleForm.logoPathCn,
        logoPathUs: this.ruleForm.logoPathUs,
        title: this.ruleForm.title,
        homeLogoPath: this.ruleForm.homeLogoPath,
        appbarLogoPath: this.ruleForm.appbarLogoPath,
        zoomLogoPath: this.ruleForm.zoomLogoPath,
        titleUs: this.ruleForm.titleUs,
        bigScreenCn: this.ruleForm.bigScreenCn,
        bigScreenUs: this.ruleForm.bigScreenUs,
        bigScreenLogoPathCn: this.ruleForm.bigScreenLogoPathCn,
        bigScreenLogoPathUs: this.ruleForm.bigScreenLogoPathUs,
        logoWidth: this.ruleForm.logoWidth,
        logoHeight: this.ruleForm.logoHeight,
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Addition Failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Added successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    editLogoFn() {
      editLogo({
        domainName: this.ruleForm.domainName,
        logoPathCn: this.ruleForm.logoPathCn,
        logoPathUs: this.ruleForm.logoPathUs,
        title: this.ruleForm.title,
        homeLogoPath: this.ruleForm.homeLogoPath,
        appbarLogoPath: this.ruleForm.appbarLogoPath,
        zoomLogoPath: this.ruleForm.zoomLogoPath,
        titleUs: this.ruleForm.titleUs,
        bigScreenCn: this.ruleForm.bigScreenCn,
        bigScreenUs: this.ruleForm.bigScreenUs,
        bigScreenLogoPathCn: this.ruleForm.bigScreenLogoPathCn,
        bigScreenLogoPathUs: this.ruleForm.bigScreenLogoPathUs,
        logoWidth: this.ruleForm.logoWidth,
        logoHeight: this.ruleForm.logoHeight,
        id: this.ruleForm.id,
        theme: this.ruleForm.theme,
        baseImg: this.ruleForm.baseImg,
        flowImg: this.ruleForm.flowImg
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Change failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Modify successfully']`)
        })
        this.getList()
        this.dialogVisible = false
        this.dialogThemeVisible = false
      })
    },
    getCn(res) {
      this.ruleForm.logoPathCn = res
    },
    getUs(res) {
      this.ruleForm.logoPathUs = res
    },
    getLogin(res) {
      this.ruleForm.homeLogoPath = res
    },
    getBar(res) {
      this.ruleForm.appbarLogoPath = res
    },
    getZoom(res) {
      this.ruleForm.zoomLogoPath = res
    },
    getScreenCn(res) {
      this.ruleForm.bigScreenLogoPathCn = res
    },
    getScreenUs(res) {
      this.ruleForm.bigScreenLogoPathUs = res
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteLogo({
          ids: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    },
    // 主题
    handleThemeClick(row) {
      this.ruleForm = {
        ...row
      }
      this.themeForm = {
        ...row.theme ? JSON.parse(row.theme) : {
          '--primary-color': '#0093b6',
          '--menu-back-ground-active': '#29a4c2',
          '--base-menu-color': '#fff',
          '--base-menu-color-active': '#fff',
          '--base-color': '#000000E6',
          '--menu-back-ground': '#0093b6',
          '--menu-logo-back-ground': '#0093b6',
        }
      }
      this.baseForm = {
        ...row.baseImg ? JSON.parse(row.baseImg) : {
          // running: '#FB560A',
          // moneySum: '#FB8477',
          // device: '#4E6070',
          // guangfu: '#7A89FE',
          // capacity: '#28C79C',
          // ratedPower: '#B6A2DE',
          // discharge: '#54B9FF',
          // charge: '#00CCC6',
          // capacityPower: '#FFAA43',
          running: '#0093b6',
          moneySum: '#0093b6',
          device: '#0093b6',
          guangfu: '#0093b6',
          capacity: '#0093b6',
          ratedPower: '#0093b6',
          discharge: '#0093b6',
          charge: '#0093b6',
          capacityPower: '#0093b6',
        }
      }
      this.flowForm = {
        ...row.flowImg ? JSON.parse(row.flowImg) : {
          // flow_pv: '#F8B52F',
          // flow_load: '#6BE6C1',
          // flow_pe: '#B6A2DE',
          // flow_en: '#626C91',
          // flow_de: '#0093B6',
          // flow_dc: '#A0A7E6',
          // flow_ac: '#3FB1E3',
          // flow_bt: '#C4EBAD',
          flow_pv: '#0093b6',
          flow_load: '#0093b6',
          flow_pe: '#0093b6',
          flow_en: '#0093b6',
          flow_de: '#0093b6',
          flow_dc: '#0093b6',
          flow_ac: '#0093b6',
          flow_bt: '#0093b6',
        }
      }
      this.dialogThemeVisible = true
    },
    handleThemeConfirm() {
      this.ruleForm.theme = JSON.stringify(this.themeForm)
      this.ruleForm.baseImg = JSON.stringify(this.baseForm)
      this.ruleForm.flowImg = JSON.stringify(this.flowForm)
      this.editLogoFn()
    },
    handleResetClick() {
      this.themeForm = {
        '--primary-color': '#0093b6',
        '--menu-back-ground-active': '#29a4c2',
        '--base-menu-color': '#fff',
        '--base-menu-color-active': '#fff',
        '--base-color': '#000000E6',
        '--menu-back-ground': '#0093b6',
        '--menu-logo-back-ground': '#0093b6',
      }
      this.baseForm = {
        // running: '#FB560A',
        // moneySum: '#FB8477',
        // device: '#4E6070',
        // guangfu: '#7A89FE',
        // capacity: '#28C79C',
        // ratedPower: '#B6A2DE',
        // discharge: '#54B9FF',
        // charge: '#00CCC6',
        // capacityPower: '#FFAA43',
        running: '#0093b6',
        moneySum: '#0093b6',
        device: '#0093b6',
        guangfu: '#0093b6',
        capacity: '#0093b6',
        ratedPower: '#0093b6',
        discharge: '#0093b6',
        charge: '#0093b6',
        capacityPower: '#0093b6',
      }
      this.flowForm = {
        // flow_pv: '#F8B52F',
        // flow_load: '#6BE6C1',
        // flow_pe: '#B6A2DE',
        // flow_en: '#626C91',
        // flow_de: '#0093B6',
        // flow_dc: '#A0A7E6',
        // flow_ac: '#3FB1E3',
        // flow_bt: '#C4EBAD',
        flow_pv: '#0093b6',
        flow_load: '#0093b6',
        flow_pe: '#0093b6',
        flow_en: '#0093b6',
        flow_de: '#0093b6',
        flow_dc: '#0093b6',
        flow_ac: '#0093b6',
        flow_bt: '#0093b6',
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;
}
</style>
