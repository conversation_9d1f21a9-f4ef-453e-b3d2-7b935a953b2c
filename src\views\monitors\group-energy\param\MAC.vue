<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-12-30 10:45:12
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="param-box">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" style="overflow: auto">
      <el-form-item :label="`${$t(`param['下发状态']`)}`" prop="status" style="width: 50%">
        <span slot="label">
          {{ $t(`param['下发状态']`) }}
          <el-tooltip class="item" effect="dark" placement="bottom">
            <div slot="content" style="line-height: 1.5">
              &emsp;{{ $t(`param['未下发']`) }}：{{ $t(`param['该类参数从未下发']`) }};
              <br />
              &emsp;{{ $t(`param['下发中']`) }}：{{ $t(`param['参数已成功下发至设备，执行未知，请等待']`) }};
              <br />
              {{ $t(`param['下发成功']`) }}：{{ $t(`param['参数已成功下发至设备并已执行成功']`) }};
              <br />
              {{ $t(`param['下发失败']`) }}：{{ $t(`param['参数已成功下发至设备，设备并未执行成功']`) }}
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-link :underline="false" v-if="form.status == 0">{{ $t(`param['未下发']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 1" type="success">{{ $t(`param['下发成功']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 2" type="primary">{{ $t(`param['下发中']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 3" type="danger">{{ $t(`param['下发失败']`) }}</el-link>
      </el-form-item>
      <el-form-item :label="`${$t(`param['有功功率设置']`)}`" prop="setting1912" style="width: 50%">
        <span slot="label">
          {{ $t(`param['有功功率设置']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('在手动模式下，控制系统输入出有功功率，正为放电，负为充电。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1912" :placeholder="'请输入'">
          <span slot="suffix">（-100~100）%</span>
        </el-input> -->
        <el-input-number v-model="form.setting1912" :precision="1" :step="0.1" :max="100" :min="-100" :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（-100~100）%</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['无功功率设置']`)}`" prop="setting1913" style="width: 50%">
        <span slot="label">
          {{ $t(`param['无功功率设置']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('控制系统输出无功功率正为容性，负为感性。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1913" :placeholder="'请输入'">
          <span slot="suffix">（-100~100）%</span>
        </el-input> -->
        <el-input-number v-model="form.setting1913" :precision="1" :step="0.1" :max="100" :min="-100" :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（-100~100）%</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['功率因数设置']`)}`" prop="setting1914" style="width: 50%">
        <span slot="label">
          {{ $t(`param['功率因数设置']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('调节输出的有功功率和无功功率的比值。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1914" :placeholder="'请输入'">
          <span slot="suffix">（-1~1）</span>
        </el-input> -->
        <el-input-number v-model="form.setting1914" :precision="3" :step="0.01" :max="1" :min="-1" :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（-1~1）</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['并离网设置']`)}`" prop="setting1915" style="width: 50%">
        <span slot="label">
          {{ $t(`param['并离网设置']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('在手动控制下，设置其系统是否并入电网。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-radio-group v-model="form.setting1915" style="width: 100%">
          <el-radio label="0">{{ $t('common.GridConnection') }}</el-radio>
          <el-radio label="1">{{ $t('common.OffGrid') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="box-footer">
      <el-divider></el-divider>
      <!-- <el-button :style="{ width: $convertPx(100, 'rem') }" @click="handleSaveClick()">保存</el-button> -->
      <el-button :style="{ width: $convertPx(100, 'rem') }" @click="getInfo">{{
    $t(`tagsView.refresh`) }}</el-button>
      <el-button type="primary" :style="{ width: $convertPx(100, 'rem') }" @click="handleSendClick" :disabled="isSend">{{ $t(`param['下发']`) }}</el-button>
    </div>

    <el-dialog :visible.sync="dialogVisible" width="20%" :modal-append-to-body="false">
      <span slot="title"></span>
      <el-result icon="success" :subTitle="$t(`param['参数已下发至设备']`)">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="handleResultClick">{{ $t(`param['查看执行结果']`) }}</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed, onUnmounted, defineProps, watch } from 'vue'
import { useStore, useRoute, useRouter } from '@/utils/vueApi.js'
import _ from 'lodash'

const { proxy } = getCurrentInstance()
const props = defineProps({
  index: {
    type: Number,
    default: 0
  }
})

watch(
  () => props.index,
  () => {
    form.value.ac = route.query.groupId.split(',')[props.index]
    getInfo()
  }
)

const store = useStore()
const route = useRoute()
const router = useRouter()

const form = ref({
  setting1912: undefined,
  setting1913: undefined,
  setting1914: undefined,
  setting1915: "0",
  ac: route.query.groupId.split(',')[props.index],
  status: 0
})
const rules = ref({
  // setting1912: [
  //   { pattern: /^-?100(?:\.0)?$|^(?:-?(?:0\.\d|[1-9]\d?\.([0-9]{1})))?$|100(?:\.0)?$/, message: '请输入-100至100的数字，并且精确到1位小数' }
  // ],
  // setting1913: [
  //   { pattern: /^-?100(?:\.0)?$|^(?:-?(?:0\.\d|[1-9]\d?\.([0-9]{1})))?$|100(?:\.0)?$/, message: '请输入-100至100的数字，并且精确到1位小数' }
  // ],
  // setting1914: [
  //   { pattern: /^-?1(?:\.0{3})?$|^(?:-?(?:0\.[0-9]{3}|0?\.([0-9]{3})))?$|1(?:\.0{3})?$/, message: '请输入-1至1的数字，并且精确到3位小数' }
  // ],
})

const handleSaveClick = () => {
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      const api = store.state.param.macInfo ? 'editMACFn' : 'addMACFn'
      store.dispatch(`param/${api}`, form.value).then(async (res) => {
        proxy.$message({
          type: 'success',
          message: proxy.$t(`param['保存成功']`)
        })
        await getInfo()
      })
    }
  });
}

// 下发
const handleSendClick = () => {
  if (form.value.status == 2) return proxy.$message({
    type: 'warning',
    message: proxy.$t(`param['正在下发中，请稍后再下发']`)
  })
  proxy.$modal.loading(`${proxy.$t(`param['正在下发中']`)}...`);
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      store.dispatch('param/sendParamMACFn', {
        ac: form.value.ac,
        id: form.value.id,
        setting1912: form.value.setting1912,
        setting1913: form.value.setting1913,
        setting1914: form.value.setting1914,
        setting1915: form.value.setting1915
      }).then(async res => {
        // dialogVisible.value = true
        getInfo()
        proxy.$modal.closeLoading()
      }).catch(() => {
        proxy.$modal.closeLoading()
      })
    }
  });
}

const dialogVisible = ref(false)

/**
 * 查看结果
 */
const handleResultClick = () => {
  let routeData = router.resolve({
    path: '/operation/log/instruct',
  });
  window.open(routeData.href, '_blank');
}

const getInfo = async () => {
  const res = await store.dispatch('param/macInfoFn', { ac: form.value.ac })
  if (!res) {
    form.value = {
      setting1912: undefined,
      setting1913: undefined,
      setting1914: undefined,
      setting1915: "0",
      ac: route.query.groupId.split(',')[props.index],
      status: 0
    }
    return
  }
  for (let key in res) {
    if (res[key] == null) res[key] = undefined
  }
   let data = _.cloneDeep(form.value)
    if (res.status == 1 && data.status == 2) {
      proxy.$message({
        type: 'success',
          message: proxy.$t(`param['下发成功']`)
        })
    } else if (res.status == 3 && data.status == 2) {
      proxy.$message({
        type: 'error',
        message: proxy.$t(`param['下发失败']`)
      })
    }
  form.value = {
    ...res
  }
}

/**
 * 设置才有范围限制
 */
const isShowExceed = ref(false)
const isExceedCom = computed(() => {
  return (min) => {
    if (isShowExceed.value) {
    return min
  } else {
    return 0
  }
  }
})
const handleInputFocus = () => {
  isShowExceed.value = true
}
const handleInputBlur = () => {
  isShowExceed.value = false
}

const isSend = computed(() => {
  let control = function () {
    let currentNodeKey = store.state.monitor.currentNodeKey
      let data = store.state.monitor.groupList[props.index]
      if (data && Object.keys(data).length == 11) return data.control
      return {
        ac: "null",
        createTime: null,
        isAnalysis: 0,
        jk_1000: null,
        jk_1001: null,
        jk_1002: null,
        jk_1003: null,
        jk_1004: null,
        jk_1005: null,
        jk_1015: null,
        jk_1016: null,
        jk_1017: null,
        jk_1031: null,
        jk_1032: null,
        jk_1033: null,
        jk_1051: null,
        jk_1056: null,
        jk_1074: null,
        jk_1077: null,
        jk_1092: null,
        jk_1093: null,
        jk_1094: null,
        jk_1095: null,
        jk_1105: null,
        jk_1106: null,
        onLineState: "离线",
        sdt: null,
      }
  }
  let controlData = control()
  if (_.isEmpty(controlData)) return true
  return controlData['onLineState'] == '离线'
})

const time = ref(null)
onMounted(() => {
  getInfo()

  // time.value = setInterval(() => {
  //   getInfo()
  // }, 10000)
})
onUnmounted(() => {
  clearInterval(time.value)
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: none;
}
</style>
