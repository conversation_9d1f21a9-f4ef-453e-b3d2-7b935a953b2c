<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-05 12:25:26
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="param">
    <el-tree :data="dataCom" @node-click="handleNodeClick" node-key="id" class="tree" :current-node-key="currentNodeKey"
      default-expand-all :expand-on-click-node="false" ref="treeRef">
      <div class="tree-title" slot-scope="{ node }">
        <div>{{ node.label }}</div>
      </div>
    </el-tree>
    <System v-if="currentNodeKey == `${$t(`monitor['主机']`)}-${$t(`log['策略类参数设置']`)}`" />
    <MAC v-if="isMac.isShow" :index="isMac.currentIndex" />
    <MDC v-if="isMdc.isShow" :index="isMdc.currentIndex" />
    <BMS v-if="isBms.isShow" :index="isBms.currentIndex" />
    <Upgrade v-if="isUpdate.isShow" :index="isUpdate.currentIndex"></Upgrade>
    <OnOff v-if="currentNodeKey == `${$t(`monitor['主机']`)}-${$t(`log['系统开关机']`)}`"></OnOff>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance, nextTick } from 'vue'
import { useStore, useRoute } from '@/utils/vueApi.js'
import { checkPermi } from '@/utils/permission.js'

import MAC from './param/MAC.vue'
import MDC from './param/MDC.vue'
import BMS from './param/BMS.vue'
import System from './param/System.vue'
import Upgrade from './param/upgrade.vue'
import OnOff from './param/OnOff.vue'

const { proxy } = getCurrentInstance()

const store = useStore()
const route = useRoute()

const currentKey = ref(`${proxy.$t(`monitor['主机']`)}-${proxy.$t(`log['策略类参数设置']`)}`)
store.commit('SET_PARAMCURRENTKEY', `${proxy.$t(`monitor['主机']`)}-${proxy.$t(`log['策略类参数设置']`)}`)
const currentNodeKey = computed({
  get: function () {
    if (store.state.monitor.paramCurrentKey) {
      return store.state.monitor.paramCurrentKey
    }
    return currentKey.value
  },
  set: function (newValue) {
    currentKey.value = newValue
  }
})

const handleNodeClick = (node) => {
  if (node.children) return
  store.commit('SET_PARAMCURRENTKEY', node.id)
  currentNodeKey.value = node.id
}

const dataCom = computed(() => {
  let data = [
    {
      label: proxy.$t(`log['策略类参数设置']`),
      id: `${proxy.$t(`monitor['主机']`)}-${proxy.$t(`log['策略类参数设置']`)}`
    }, {
      label: proxy.$t(`log['系统开关机']`),
      id: `${proxy.$t(`monitor['主机']`)}-${proxy.$t(`log['系统开关机']`)}`
    }
  ]

  let groupList = store.state.monitor.groupList
  groupList.forEach((item, index) => {
    let children = []
    if (item.ac && item.ac.length) children.push({
      label: proxy.$t(`param['MAC参数设置']`),
      id: `${item.id}-${proxy.$t(`param['MAC参数设置']`)}`
    })
    if (item.dc && item.dc.length) children.push({
      label: proxy.$t(`param['MDC参数设置']`),
      id: `${item.id}-${proxy.$t(`param['MDC参数设置']`)}`
    })
    if ((item.bms && item.bms.length) || (item.bmsBau && item.bmsBau.length)) children.push({
      label: proxy.$t(`param['电池参数设置']`),
      id: `${item.id}-${proxy.$t(`param['电池参数设置']`)}`
    })
    if (checkPermi(['system:sendMqtt:upgradeJson'])) {
      children.push({
        label: proxy.$t(`param['在线升级']`),
        id: `${item.id}-${proxy.$t(`param['在线升级']`)}`
      })
    }
    let label = ''
    if (index == 0) {
      label = `${proxy.$t(`monitor['主机']`)} - ${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
    } else {
      label = `${proxy.$t(`monitor['从机']`)} - ${index < 10 ? '0' + index : index}`
    }
    data.push({
      label: label,
      children,
      id: item.id
    })
  })

  if (store.state.monitor.paramCurrentKey) {
    nextTick(() => {
      proxy.$refs?.treeRef?.setCurrentKey(store.state.monitor.paramCurrentKey)
    })
  }

  return data
})

const isMac = computed(() => {
  let currentKey = currentNodeKey.value
  let groupId = route.query.groupId.split(',')
  let index = groupId.findIndex(item => item == currentKey)
  if (index != -1) currentKey = `${groupId[index]}-${proxy.$t(`param['MAC参数设置']`)}`
  let currentIndex = groupId.findIndex(item => item == currentKey.split('-')[0])
  let type = currentKey.split('-')[1]
  if (type == proxy.$t(`param['MAC参数设置']`)) return {
    isShow: true,
    currentIndex
  }
  return {
    isShow: false,
    currentIndex
  }
})
const isMdc = computed(() => {
  let currentKey = currentNodeKey.value
  let groupId = route.query.groupId.split(',')
  let index = groupId.findIndex(item => item == currentKey)
  if (index != -1) currentKey = `${groupId[index]}-${proxy.$t(`param['MDC参数设置']`)}`
  let currentIndex = groupId.findIndex(item => item == currentKey.split('-')[0])
  let type = currentKey.split('-')[1]
  if (type == proxy.$t(`param['MDC参数设置']`)) return {
    isShow: true,
    currentIndex
  }
  return {
    isShow: false,
    currentIndex
  }
})
const isBms = computed(() => {
  let currentKey = currentNodeKey.value
  let groupId = route.query.groupId.split(',')
  let index = groupId.findIndex(item => item == currentKey)
  if (index != -1) currentKey = `${groupId[index]}-${proxy.$t(`param['电池参数设置']`)}`
  let currentIndex = groupId.findIndex(item => item == currentKey.split('-')[0])
  let type = currentKey.split('-')[1]
  if (type == proxy.$t(`param['电池参数设置']`)) return {
    isShow: true,
    currentIndex
  }
  return {
    isShow: false,
    currentIndex
  }
})
const isUpdate = computed(() => {
  let currentKey = currentNodeKey.value
  let groupId = route.query.groupId.split(',')
  let index = groupId.findIndex(item => item == currentKey)
  let currentIndex = groupId.findIndex(item => item == currentKey.split('-')[0])
  let type = currentKey.split('-')[1]
  if (type == proxy.$t(`param['在线升级']`)) return {
    isShow: true,
    currentIndex
  }
  return {
    isShow: false,
    currentIndex
  }
})
</script>

<style lang="scss" scoped>
:deep(.tree) {
  height: 100%;
  border-right: 1px solid #D6D6D6;
  flex: 1;

  .tree-title {
    padding: 10px;
  }

  .el-tree-node__content {
    height: auto;
  }

  .is-current >.el-tree-node__content {
    font-weight: bold;
    color: var(--base-color);
    background-color: #f6f6f6;
  }
  .el-tree-node__content:hover,
  .el-upload-list__item:hover {
    background-color: #f6f6f6;
  }
}

.param {
  height: 800px;
  display: flex;
}

.param-box {
  padding: 10px 20px 20px 20px;
  flex: 5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.param-title {
  margin-bottom: 20px;
  font-weight: bold;
}
</style>
