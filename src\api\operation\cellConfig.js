/*
 * @Author: Administrator <EMAIL>
 * @Date: 2025-03-03 16:50:27
 * @LastEditors: Administrator <EMAIL>
 * @LastEditTime: 2025-03-03 17:53:33
 * @FilePath: \elecloud_platform-main\src\api\operation\cellConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request';


// 获取列表
export function cellConfigList(queryInfo) {
  return request({
    url: '/system/BMSCellConfig/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addCellConfig(data) {
  return request({
    url: '/system/BMSCellConfig',
    method: 'post',
    data
  })
}

// 修改
export function editCellConfig(data) {
  return request({
    url: '/system/BMSCellConfig',
    method: 'put',
    data
  })
}

// 删除
export function deleteCellConfig(queryInfo) {
  return request({
    url: `/system/BMSCellConfig/${queryInfo.ids}`,
    method: 'delete'
  })
}

