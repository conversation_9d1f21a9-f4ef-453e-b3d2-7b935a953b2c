/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-01-31 11:34:13
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-03-08 12:14:07
 * @FilePath: \elecloud_platform-main\src\utils\export.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export function handleExport(data, fileName, suffix = '.xlsx') {
  let blob = new Blob([data]);
  let url = URL.createObjectURL(blob);
  // 重命名文件名称
  let link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${fileName}${suffix}`
  );
  link.click();
}
