<template>
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-select v-model="queryInfo.status" :placeholder="$t('common.select')" style="width: 120px">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="input_ment">
          <el-input :placeholder="$t(`oss['请输入文件名称']`)" style="width: 200px;" v-model="queryInfo.fileName"
            clearable></el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
            }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="fileName" :label="$t(`oss['文件名称']`)" show-overflow-tooltip align="center" />
        <el-table-column prop="fileType" :label="$t(`oss['文件类型']`)" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag type="primary" v-if="scope.row.fileType == 1">{{ $t(`log['收益统计报表']`) }}</el-tag>
            <el-tag type="primary" v-if="scope.row.fileType == 2">{{ $t(`log['电量统计报表']`) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('common.status')" show-overflow-tooltip class-name="alarm-state"
          align="center">
          <template slot-scope="scope">
            <div class="solve" v-if="scope.row.status == 2">
              <svg-icon icon-class="solve" style="width: 1.3em;height: 1.3em;" />
              <span class="el-icon--right">{{ $t(`log['导出成功']`) }}</span>
            </div>
            <div class="solve" v-else-if="scope.row.status == 3">
              <svg-icon icon-class="solve-off" style="width: 1.3em;height: 1.3em;" />
              <span class="el-icon--right">{{ $t(`log['导出失败']`) }}</span>
            </div>
            <div class="solve" v-else>
              <svg-icon icon-class="await" style="width: 1.3em;height: 1.3em;" />
              <span class="el-icon--right">{{ $t(`log['导出中']`) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button @click="handleDownClick(scope.row)" type="text" size="small" v-if="scope.row.status == 2">{{
              $t(`oss['下载']`) }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>
  </div>
</template>

<script>
import { exportRecordList } from '@/api/operation/exportRecord'
import { downloadOss } from '@/api/operation/upgrade'
import { handleExport } from '@/utils/export'

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      statusOptions: [
        {
          value: 2,
          label: '导出成功'
        },
        {
          value: 3,
          label: '导出失败'
        },
        {
          value: 1,
          label: '导出中'
        },
      ]
    };
  },
  mounted() {
    this.getList()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      exportRecordList(this.queryInfo).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleDownClick(row) {
      this.$modal.loading(`${this.$t(`oss['正在下载文件，请稍候']`)}...`);
      downloadOss({ filePath: row.fileUrl }).then(res => {
        if (res.code) if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        handleExport(res, row.fileName)
        this.$modal.closeLoading()
        this.$message({
          type: 'success',
          message: this.$t(`oss['下载文件成功']`)
        })
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;

  .solve {
    display: flex;
    align-items: center;
  }
}

::v-deep .alarm-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
