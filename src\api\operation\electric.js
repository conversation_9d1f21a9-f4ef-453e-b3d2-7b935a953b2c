/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-03-11 18:31:49
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-03-11 18:32:05
 * @FilePath: \elecloud_platform-main\src\api\operation\bill.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取列表
export function electricList(queryInfo) {
  return request({
    url: '/system/electricStatistics/list',
    method: 'get',
    params: queryInfo
  })
}

// 导出
export function electricExport(data) {
  return request({
    url: '/system/electricStatistics/export',
    method: 'post',
    data,
    responseType: "blob",
  })
}

export function electricDetail(queryInfo) {
  return request({
    url: '/system/electricStatistics/detail',
    method: 'get',
    params: queryInfo
  })
}

export function electricDetailExport(data) {
  return request({
    url: '/system/electricStatistics/detail/export',
    method: 'post',
    data,
    responseType: "blob",
  })
}
