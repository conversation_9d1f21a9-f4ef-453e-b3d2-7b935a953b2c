<template>
  <div class="map">
    <div class="search-box" v-if="isSearch">
      <GmapAutocomplete @place_changed="setPlace" :placeholder="$t(`project['Please enter full address']`)" />
    </div>
    <div class="GmapMapBox">
      <GmapMap @click="clickMap" :center="center" :zoom="zoom" style="width: 100%; height: 100%" ref="mapRef">
        <!-- 信息窗体 -->
        <GmapInfoWindow :options="infoOptions" :position="infoWindowPos" :opened="infoWinOpen"
          @closeclick="infoWinOpen = false">
          <div style="width: 300px">
            <div class="detail-row"><span class="detail-title">{{ $t('project.name') }}</span>：<el-button type="text"
                class="detail-row" @click="go2Detail">{{ infoContent.projectName }}</el-button></div>
            <div class="detail-row"><span class="detail-title">{{ $t('创建人员') }}</span>：{{ infoContent.nickName }}</div>
            <div class="detail-row"><span class="detail-title">{{ $t('home.pieRadioText2') }}</span>：<el-button
                type="text" class="detail-row" @click="go2Detail">{{ infoContent.countDevice }}</el-button> {{ $t('台')
              }}</div>
            <div class="detail-row"><span class="detail-title">{{ $t('所属国家') }}</span>：{{ infoContent.country }}</div>
            <div class="detail-row"><span class="detail-title">{{ $t('project.address') }}</span>：{{
              infoContent.projectAddress }}</div>
            <div class="detail-row"><span class="detail-title">{{ $t(`common['时区地址']`) }}</span>：{{
              infoContent[getPropFn] }}</div>
          </div>
        </GmapInfoWindow>
        <!-- 标记 -->
        <GmapMarker :key="`${index}-point`" v-for="(m, index) in markers" :position="m.position"
          @click="clickMarker(m, index)" />
      </GmapMap>
    </div>
  </div>
</template>


<script>
import { projectAll } from '@/api/property/device'
import { gmapApi } from 'vue2-google-maps'
import Cookies from 'js-cookie'

export default {
  name: "GoogleMap",
  data() {
    return {
      center: { lat: 10.0, lng: 10.0 }, // 中心位置
      center_: { lat: 10.0, lng: 10.0 }, // 保存当前点位置
      currentPlace: null,
      markers: [],
      places: [],
      placeName: "",
      dialogVisible: true,
      googlemap: "",
      hasSetPin: false,
      icon: '',
      zoom: 5,
      // 信息窗体
      infoContent: {},
      infoWindowPos: null,
      infoWinOpen: false,
      currentMidx: null,
      //optional: offset infowindow so it visually sits nicely on top of our marker
      infoOptions: {
        pixelOffset: {
          width: 0,
          height: -35
        }
      },
    };
  },
  props: ['isSearch', 'xy'],
  computed: {
    google: gmapApi,
    getPropFn() {
      let lang = this.$store.getters.language
      switch (lang) {
        case 'zh':
          return 'timeZoneAddress'
        case 'en':
          return 'timeZoneAddressUs'
        case 'it':
          return 'timeZoneAddressIt'
      }
    }
  },
  watch: {
    xy(val) {
      if (!this.isSearch) return
      if (!this.xy.lat) return this.geolocate()
      this.hasSetPin = true
      this.zoom = 15
      this.center = {
        lat: this.xy.lat,
        lng: this.xy.lng,
      };
      this.center_ = this.center
      this.markers = [{ position: this.center }];
    }
  },
  mounted() {
    if (this.isSearch) {
      if (!this.xy.lat) return this.geolocate()
      this.hasSetPin = true
      this.zoom = 15
      this.center = {
        lat: this.xy.lat,
        lng: this.xy.lng,
      };
      this.center_ = this.center
      this.markers = [{ position: this.center }];
    } else {
      this.getList()
    }

    this.$refs.mapRef.$mapPromise.then((map) => {
      this.map = this.$refs.mapRef.$mapObject;
      this.$gmapApiPromiseLazy().then(() => {
        this.placesService = new google.maps.places.PlacesService(this.map);
        this.Geocoder = new google.maps.Geocoder()
        this.infowindow = new google.maps.InfoWindow()
      })
    })
  },
  methods: {
    setPlace(place) {
      // console.log(place);
      this.currentPlace = place;
      this.addMarkerFun();
      const getPlaceData = (place, infoType, format) => {
        let data,
          addressComponents = [...place.address_components];

        addressComponents.forEach(componentItem => {
          if (componentItem.types[0] === infoType) {
            data = componentItem[format];
            addressComponents.length = 0;
          }
        });

        return data;
      };
      const district =
        getPlaceData(place, "sublocality", "long_name") ||
        getPlaceData(place, "sublocality_level_1", "long_name");
      const city =
        getPlaceData(place, "locality", "long_name") ||
        getPlaceData(place, "administrative_area_level_2", "long_name");
      const state = getPlaceData(place, "administrative_area_level_1", "short_name");
      const country = getPlaceData(place, "country", "long_name");
      // console.log(country, state, city, district);
      this.$emit('search', country, state, city, district, place.formatted_address, {
        lat: place.geometry.location.lat(),
        lng: place.geometry.location.lng(),
      })
    },
    addMarker() {
      this.addMarkerFun();
    },
    addMarkerFun() {
      if (this.currentPlace) {
        this.hasSetPin = true
        this.zoom = 10
        const marker = {
          lat: this.currentPlace.geometry.location.lat(),
          lng: this.currentPlace.geometry.location.lng(),
        };
        this.markers = [];
        this.markers.push({ position: marker });
        this.places.push(this.currentPlace);
        this.center = marker;
        this.center_ = marker;
        this.placeName = this.currentPlace.name;
        this.currentPlace = null;
      }
    },
    geolocate() {
      // if (navigator.geolocation) {
      //   navigator.geolocation.getCurrentPosition((position) => {
      //     if (position && position.coords && position.coords.latitude) {
      // alert("获取地理位置："+position.coords.latitude+","+position.coords.longitude)
      let ipData = JSON.parse(Cookies.get('ip'))
      this.hasSetPin = true
      this.zoom = 10
      if (ipData.lat) {
        this.center = {
          lat: ipData.lat,
          lng: ipData.lng,
        };
        this.center_ = this.center
        this.markers.push({ position: this.center });
      } else {
        this.center = {
          lat: 22.6369,
          lng: 113.9331,
        }
        this.center_ = this.center
        // this.markers.push({ position: this.center });
      }
      // console.log(this.center);
      //     }
      //   }, (error) => {  // html5 默认调用的谷歌的接口，会有安全限制
      //     switch (error.code) {
      //       case error.PERMISSION_DENIED: // 许可拒绝,用户选了不允许
      //         alert("您拒绝对获取地理位置的请求")
      //         alert(error.message);
      //         break;
      //       case error.POSITION_UNAVAILABLE: // 连不上GPS卫星，或者网络断了
      //         alert("位置信息是不可用的");
      //         alert(error.message);
      //         break;
      //       case error.TIMEOUT:  // /超时了
      //         alert("请求您的地理位置超时");
      //         alert(error.message);
      //         break;
      //       case error.UNKNOWN_ERROR:
      //         alert("未知错误");
      //         alert(error.message);
      //         break;
      //     }
      //   });
      // } else {
      //   alert("未获取获取到地理位置");
      //   vm.markers.push({ position: vm.center });
      // }
    },
    clickMap(e) {
      if (!this.isSearch) return
      this.hasSetPin = true
      let longlat = e.latLng.lat() + "," + e.latLng.lng();
      this.center_ = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };
      this.markers = [];
      this.markers.push({ position: this.center_ });

      this.Geocoder
        .geocode({ location: this.center_ })
        .then((res) => {
          if (res.results[0]) {
            // console.log(res.results[0]);
            const getPlaceData = (place, infoType, format) => {
              let data,
                addressComponents = [...place.address_components];

              addressComponents.forEach(componentItem => {
                if (componentItem.types[0] === infoType) {
                  data = componentItem[format];
                  addressComponents.length = 0;
                }
              });

              return data;
            };
            const district =
              getPlaceData(res.results[0], "political", "long_name") || getPlaceData(res.results[0], "sublocality", "long_name") ||
              getPlaceData(res.results[0], "sublocality_level_1", "long_name");
            const city =
              getPlaceData(res.results[0], "locality", "long_name") ||
              getPlaceData(res.results[0], "administrative_area_level_2", "long_name");
            const state = getPlaceData(res.results[0], "administrative_area_level_1", "short_name");
            const country = getPlaceData(res.results[0], "country", "long_name");
            this.$emit('search', country, state, city, district, res.results[0].formatted_address, {
              lat: res.results[0].geometry.location.lat(),
              lng: res.results[0].geometry.location.lng(),
            })
            // map.setZoom(11);

            // const marker = new google.maps.Marker({
            //   position: latlng,
            //   map: map,
            // });

            // this.infowindow.setContent(response.results[0].formatted_address);
            // this.infowindow.open(this.map, marker);
          } else {
            window.alert("No results found");
          }
        }).catch((e) => window.alert("Geocoder failed due to: " + e));

      // let request = { placeId: e.placeId }
      // this.placesService.getDetails(request, function (results, status) {
      //   console.log(results, status);
      //   if (status === 'OK') {
      //     if (results) {
      //       // alert(results.formatted_address)
      //       console.log(results.formatted_address);
      //     } else {
      //       window.alert('No results found')
      //     }
      //   }
      // })
    },
    // click markers show infoWindow
    clickMarker(val, idx) {
      // console.log(val, idx);
      this.center = val;
      this.infoWindowPos = val.position;
      this.infoContent = val;
      //check if its the same marker that was selected if yes toggle
      if (this.currentMidx == idx) {
        this.infoWinOpen = !this.infoWinOpen;
      }
      //if different marker set infowindow to open and reset current marker index
      else {
        this.infoWinOpen = true;
        this.currentMidx = idx;
      }
    },
    // click project name link to device monitor
    go2Detail() {
      this.$router.push({
        path: "../../monitors/opticalstorage?name=" + this.infoContent.projectName,
      });
    },
    submitMap() {
      if (!this.hasSetPin) {
        this.msgError(this.$t("googlemap.searchAndAdd"));
        return
      }
      let obj = Object.assign({}, this.center_);
      obj.name = `${this.center_.lat.toFixed(5)},${this.center_.lng.toFixed(5)}`;
      this.$emit("setMap", obj);
    },
    // 获取点位
    getList() {
      projectAll().then(res => {
        this.tableData = res.data

        if (this.tableData.length == 0) {
          this.geolocate()
        } else {
          this.hasSetPin = true
          this.zoom = 5
          this.center = {
            lat: this.tableData[0].projectLatitudey,
            lng: this.tableData[0].projectLatitudex,
          };
          this.center_ = this.center
          this.markers.push({ position: this.center });
        }
        this.tableData.forEach(marker => {
          this.markers.push({
            position: {
              lat: marker.projectLatitudey,
              lng: marker.projectLatitudex,
            },
            ...marker
          });
        });
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.map {
  width: 100%;
  height: 100%
}

.search-box input {
  height: 40px;
  width: 30%;
  border-radius: 5px;
  border: 1px solid #ccc;
  padding-left: 7px;
  outline: none;
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 200;
}

@media only screen and (max-width: 820px) {
  .img-box .el-image {
    height: 4vh;
  }

  .search-box input {
    height: 5vh;
  }
}

.map .GmapMapBox {
  width: 100%;
  height: 100%;
}

::v-deep .gm-style-mtc-bbw {
  bottom: 56px !important;
  top: auto !important;
}

.detail-row {
  padding: 3px 0;
}

.detail-title {
  font-weight: 600;
}
</style>
