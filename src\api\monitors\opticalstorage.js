/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-12 14:26:22
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-24 11:32:42
 * @FilePath: \elecloud_platform-main\src\api\monitors\opticalstorage.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 查询设备监控列表
export function deviceMonitoringList(queryInfo) {
  return request({
    url: '/system/deviceMonitoring/list',
    method: 'get',
    params: queryInfo
  })
}

// 获取设备详细信息(上)
export function deviceMonitoringDetailTop(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/top/${queryInfo.deviceSerialNumber}/${queryInfo.deviceType}`,
    method: 'get'
  })
}

// 获取设备详细信息（右）
export function deviceMonitoringDetailRight(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/right/${queryInfo.deviceSerialNumber}/${queryInfo.type}/${queryInfo.deviceType}`,
    method: 'get'
  })
}

// 功率分析统计
export function powerAnalysisStatistics(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/powerAnalysisStatistics`,
    method: 'post',
    data: queryInfo
  })
}

// 功率分析统计 - 组合类型
export function powerAnalysisStatisticsGroup(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/GroupPowerAnalysisStatistics`,
    method: 'post',
    data: queryInfo
  })
}

// 数据概括-动态图
export function selectDynamicGraph(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/selectDynamicGraph/${queryInfo.deviceSerialNumber}`,
    method: 'get'
  })
}

// 数据概括-动态图 - 组合类型
export function selectDynamicGraphGroup(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/selectGroupDynamicGraph/${queryInfo.deviceSerialNumber}`,
    method: 'get'
  })
}

// 获取用电量、放电量统计
export function electricStatistics(data) {
  return request({
    url: '/system/deviceMonitoring/electricStatistics',
    method: 'post',
    data
  })
}

// 导出电量
export function exportDeviceMonitoring(data) {
  return request({
    url: '/system/deviceMonitoring/export',
    method: 'post',
    data,
    responseType: "blob",
  })
}

// 分配设备 项目与设备一起分配
export function allotDevice(data) {
  return request({
    url: '/system/deviceMonitoring/allotDevice',
    method: 'post',
    data
  })
}

/**
 * 远程控制 VNC
 */
// 获取所有端口号
export function listAll(queryInfo) {
  return request({
    url: '/system/VNCPort/listAll',
    method: 'get',
    params: queryInfo
  })
}

// 获取id
export function vncParameterSettingByAc(queryInfo) {
  return request({
    url: `/system/vncParameterSetting/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}

// 开启远程
export function argumentsJsonFrpOpen(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonFrpOpen',
    method: 'post',
    data,
    timeout: 120000
  })
}

// 关闭远程
export function argumentsJsonFrpClose(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonFrpClose',
    method: 'post',
    data
  })
}
