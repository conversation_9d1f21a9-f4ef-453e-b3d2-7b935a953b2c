<template>
  <div class="box">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <el-radio-group v-model="dateType" size="medium" class="input_ment" @input="changeDateType">
          <el-radio-button :label="$t('date.day')"></el-radio-button>
          <el-radio-button :label="$t('date.month')"></el-radio-button>
          <el-radio-button :label="$t('date.year')"></el-radio-button>
        </el-radio-group>
        <div class="input_ment">
          <el-date-picker v-model="date" :type="dateTypeCom" :valueFormat='valueFormat' :range-separator="$t('date.to')"
            :start-placeholder="$t('date.start')" :picker-options="pickerOptions" :end-placeholder="$t('date.end')"
            @change="handleDateChange">
          </el-date-picker>
        </div>
        <div class="input_ment">
          <el-select v-model="queryInfo.deviceType" :placeholder="$t('common.select')" style="width: 120px">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="input_ment">
          <el-input :placeholder="$t(`device['Please enter device serial number']`)" style="width: 200px;" v-model="queryInfo.ac" clearable></el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
            }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary"  @click="handleExportClick()">{{ $t('common.exportReport') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" stripe show-summary :summary-method="getSummaries"
        :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
        :cell-style="{ 'text-align': 'center', 'font-size': '14px' }" style="width: 100%;" align="center" row-key="id"
        :tree-props="{children: 'children'}">
        <el-table-column type="index" label="#" width="60" />
        <el-table-column prop="deviceType" :label="$t('device.type')" show-overflow-tooltip width="320">
          <template slot-scope="scope">
            <el-tag type="primary">{{ getDeviceTypeFn(scope.row.deviceType, true) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ac" :label="$t('device.sn')" show-overflow-tooltip />
        <el-table-column prop="dischargeCapacityCalculate" :label="$t(`bill['放电量(kWh)']`)" sortable show-overflow-tooltip />
        <el-table-column prop="chargeCapacityCalculate" :label="$t(`bill['充电量(kWh)']`)" sortable show-overflow-tooltip />
        <el-table-column prop="photovoltaicPowerCapacityCalculate" :label="$t(`bill['光伏发电量(kWh)']`)" sortable show-overflow-tooltip width="300" />
        <el-table-column prop="updateTime" :label="$t(`bill['更新时间']`)" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.updateTime ? scope.row.updateTime: '-' }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="statisticsTime" :label="$t(`bill['统计时间']`)" show-overflow-tooltip /> -->
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>
  </div>
</template>

<script>
import { electricList, electricExport } from '@/api/operation/electric'
import { handleExport } from '@/utils/export'
import _ from 'lodash'
import { deviceTypeSingleOptions, deviceTypeGroupOptions, getDeviceType } from '@/hook/useDeviceType'

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      date: undefined, // 获取当前日期
      dateType: this.$t('date.month'),
      pickerOptions: {
        shortcuts: [{
          text: this.$t('date.lastWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.lastMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.last3Month'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    };
  },
  mounted() {
    this.changeDateType()
  },
  computed: {
    valueFormat() {
      if (this.dateType == this.$t('date.day')) {
        return 'yyyy-MM-dd'
      } else if (this.dateType == this.$t('date.month')) {
        return 'yyyy-MM'
      } else if (this.dateType == this.$t('date.year')) {
        return 'yyyy'
      }
    },
    dateTypeCom() {
      if (this.dateType == this.$t('date.day')) {
        return 'daterange'
      } else if (this.dateType == this.$t('date.month')) {
        return 'month'
      } else if (this.dateType == this.$t('date.year')) {
        return 'year'
      }
    },
    typeOptions() {
      return [...deviceTypeSingleOptions, ...deviceTypeGroupOptions]
    }
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      electricList(this.queryInfo).then(res => {
        let data = res.rows
        function mapData(list) {
          list.forEach(item => {
            item.dischargeCapacityCalculate = item.dischargeCapacityCalculate != null ? _.round(item.dischargeCapacityCalculate, 2): '--'
            item.chargeCapacityCalculate = item.chargeCapacityCalculate != null ? _.round(item.chargeCapacityCalculate, 2) : '--'
            item.photovoltaicPowerCapacityCalculate = item.photovoltaicPowerCapacityCalculate != null ? _.round(item.photovoltaicPowerCapacityCalculate, 2): '--'
            if (item.children) mapData(item.children)
          })
        }
        mapData(data)
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleDateChange(date) {
      if (!date) return this.changeDateType()
      if (this.dateType == this.$t('date.day')) {
        this.queryInfo.startDate = date[0]
        this.queryInfo.endDate = date[1]
      } else if (this.dateType == this.$t('date.month')) {
        this.queryInfo.startDate = date
        this.queryInfo.endDate = date
      } else if (this.dateType == this.$t('date.year')) {
        this.queryInfo.startDate = date
        this.queryInfo.endDate = date
      }
      this.getList()
    },
    changeDateType() {
      let startDate = ''
      let endDate = ''
      if (this.dateType == this.$t('date.day')) {
        this.date = [this.$moment(new Date()).startOf('day').format('YYYY-MM-DD'), this.$moment(new Date()).endOf('day').format('YYYY-MM-DD')]
        startDate = this.date[0]
        endDate = this.date[1]
        this.date = [
          startDate,
          endDate
        ]
      } else if (this.dateType == this.$t('date.month')) {
        this.date = [this.$moment().startOf('month').format("YYYY-MM"), this.$moment().endOf('month').format("YYYY-MM")]
        startDate = this.date[0]
        endDate = this.date[1]
        this.date = startDate
      } else if (this.dateType == this.$t('date.year')) {
        this.date = [this.$moment().startOf('year').format("YYYY"), this.$moment().endOf('year').format("YYYY")]
        startDate = this.date[0]
        endDate = this.date[1]
        this.date = startDate
      }
      this.queryInfo = {
        ...this.queryInfo,
        startDate,
        endDate
      }
      this.getList()
    },
    getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = this.$t(`bill['合计']`);
            return;
          }
          if (index == 1) {
            sums[index] = ''
            return
          }
          if (index == 2) {
            sums[index] = ''
            return
          }
          if (index == 7) {
            sums[index] = ''
            return
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0).toFixed(2);
            sums[index] += ' kWh';
          } else {
            sums[index] = '';
          }
        });

        return sums;
    },
    handleExportClick() {
      this.$modal.loading(`${this.$t(`oss['正在下载文件，请稍候']`)}...`);
      electricExport({
        ac: this.queryInfo.ac,
        startDate: this.queryInfo.startDate,
        endDate: this.queryInfo.endDate,
      }).then(res => {
        if (res.code) if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let fileName = ''
        if (this.dateType == this.$t('date.day')) {
          fileName = `${this.queryInfo.startDate}-${this.queryInfo.endDate}_${this.$t(`log['电量统计报表']`)}`
        } else if (this.dateType == this.$t('date.month')) {
           fileName = `${this.queryInfo.startDate}${this.$t('date.month')}_${this.$t(`log['电量统计报表']`)}`
        } else if (this.dateType == this.$t('date.year')) {
          fileName = `${this.queryInfo.startDate}${this.$t('date.year')}_${this.$t(`log['电量统计报表']`)}`
        }
        handleExport(res, fileName)
         this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    getDeviceTypeFn(type, needGroup) {
      return getDeviceType(type, needGroup)
    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  max-height: calc(100vh - 80px);
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;

  .table_box {
    margin-top: 30px;
    width: 100%;
    background-color: white;
    padding-bottom: 10px;
    border-radius: 12px;

    .solve {
      display: flex;
      align-items: center;
    }
  }
}

::v-deep .alarm-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-table__footer-wrapper tbody td.el-table__cell {
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}
</style>

