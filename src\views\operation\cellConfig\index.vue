<template>
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t(`device['Please enter device serial number']`)" style="width: 200px;"
            v-model="queryInfo.ac" clearable></el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="getList()">{{ $t('common.search')
            }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{
            $t('common.add') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="ac" :label="$t('device.sn')" width="240" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.ac }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.ac"
              v-clipboard:success="copySuccess"></i>
          </template>
        </el-table-column>
        <el-table-column prop="cellNumber" :label="$t('电芯数量')" show-overflow-tooltip align="center" />
        <el-table-column prop="voltageNumber" :label="$t('电压数量')" show-overflow-tooltip align="center" />
        <el-table-column prop="temperatureNumber" :label="$t('温度数量')" show-overflow-tooltip align="center" />
        <el-table-column prop="temperatureRiseNumber" :label="$t('温升数量')" show-overflow-tooltip align="center" />
        <el-table-column prop="electricityNumber" :label="$t('电流数量')" show-overflow-tooltip align="center" />
        <el-table-column prop="impedanceNumber" :label="$t('阻抗数量')" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button @click="handleEditClick(scope.row)" type="text" size="small">{{ $t('common.edit') }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small">{{ $t('common.delete')
              }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>

    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px" :title="dialogTitle">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
        <el-form-item :label="$t('device.sn')" prop="ac">
          <el-input v-model="ruleForm.ac" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('电芯数量')" prop="cellNumber">
          <el-input v-model="ruleForm.cellNumber" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('电压数量')" prop="voltageNumber">
          <el-input v-model="ruleForm.voltageNumber" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('温度数量')" prop="temperatureNumber">
          <el-input v-model="ruleForm.temperatureNumber" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('温升数量')" prop="temperatureRiseNumber">
          <el-input v-model="ruleForm.temperatureRiseNumber" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('电流数量')" prop="electricityNumber">
          <el-input v-model="ruleForm.electricityNumber" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('阻抗数量')" prop="impedanceNumber">
          <el-input v-model="ruleForm.impedanceNumber" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { cellConfigList, addCellConfig, editCellConfig, deleteCellConfig } from '@/api/operation/cellConfig'

export default {
  data() {
    const equalToCellNumber = (rule, value, callback) => {
      if (this.ruleForm.cellNumber < value) {
        callback(new Error(this.$t('不能高于电芯数量')));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      ruleForm: {
        ac: '',
        cellNumber: '',
        electricityNumber: '',
        impedanceNumber: '',
        temperatureNumber: '',
        temperatureRiseNumber: '',
        voltageNumber: '',
      },
      rules: {
        ac: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        cellNumber: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        electricityNumber: [
          { validator: equalToCellNumber, trigger: "blur" }
        ],
        impedanceNumber: [
          { validator: equalToCellNumber, trigger: "blur" }
        ],
        temperatureNumber: [
          { validator: equalToCellNumber, trigger: "blur" }
        ],
        temperatureRiseNumber: [
          { validator: equalToCellNumber, trigger: "blur" }
        ],
        voltageNumber: [
          { validator: equalToCellNumber, trigger: "blur" }
        ],
      },
      dialogTitle: '添加配置'
    };
  },
  mounted() {
    this.getList()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      cellConfigList(this.queryInfo).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == this.$t('添加配置')) {
            this.addCellConfigFn()
          } else {
            this.editCellConfigFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogVisible = true;
      this.dialogTitle = this.$t('添加配置')
      this.ruleForm = {
        ac: '',
        cellNumber: '',
        electricityNumber: '',
        impedanceNumber: '',
        temperatureNumber: '',
        temperatureRiseNumber: '',
        voltageNumber: '',
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    addCellConfigFn() {
      addCellConfig({
        ac: this.ruleForm.ac,
        cellNumber: this.ruleForm.cellNumber,
        electricityNumber: this.ruleForm.electricityNumber,
        impedanceNumber: this.ruleForm.impedanceNumber,
        temperatureNumber: this.ruleForm.temperatureNumber,
        temperatureRiseNumber: this.ruleForm.temperatureRiseNumber,
        voltageNumber: this.ruleForm.voltageNumber
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Addition Failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Added successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    handleEditClick(row) {
      this.dialogTitle = this.$t('修改配置')
      this.dialogVisible = true;
      this.ruleForm = {
        ...row
      }
    },
    editCellConfigFn() {
      editCellConfig({
        ac: this.ruleForm.ac,
        cellNumber: this.ruleForm.cellNumber,
        electricityNumber: this.ruleForm.electricityNumber,
        impedanceNumber: this.ruleForm.impedanceNumber,
        temperatureNumber: this.ruleForm.temperatureNumber,
        temperatureRiseNumber: this.ruleForm.temperatureRiseNumber,
        voltageNumber: this.ruleForm.voltageNumber,
        id: this.ruleForm.id
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Change failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Modify successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteCellConfig({
          ids: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    }
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;
}
</style>
