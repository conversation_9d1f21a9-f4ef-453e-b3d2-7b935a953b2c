<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-17 15:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-20 12:04:26
 * @FilePath: \办公文档\代码\新建文件夹\src\views\system\index\index_2.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="home">
    <div class="home-tree elevation-4" v-loading="treeLoading">
      <div class="tree-select">
        <span>{{ $t('device.type') }}：</span>
        <el-select v-model="deviceType" @change="handleChange" :placeholder="$t('common.select')" style="flex: 1">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
        <el-tab-pane :name="$t('home.pieRadio2')">
          <span slot="label">{{ $t('home.pieRadio2') }}（{{ deviceNum }}）</span>
        </el-tab-pane>
        <el-tab-pane :name="$t('contrast.param')">
          <span slot="label">{{ $t('contrast.param') }}（{{ paramNum }}）</span>
        </el-tab-pane>
      </el-tabs>
      <el-tree ref="treeDeviceRef" :data="data" show-checkbox node-key="treeId" :props="defaultProps" default-expand-all
        check-on-click-node check-strictly v-show="activeName == $t('home.pieRadio2')" @check="handleDeviceCheck" />
      <el-tree ref="treeParamRef" :data="paramOptions" show-checkbox node-key="name" :props="defaultProps"
        default-expand-all check-on-click-node v-show="activeName == $t('contrast.param')" @check="handleParamCheck" />
      <div class="tree-btn">
        <el-button @click="handleClearClick">{{ $t('清空') }}</el-button>
        <el-button type="primary" @click="handleConfirmClick">{{ $t('common.confirm') }}</el-button>
      </div>
    </div>
    <div class="home-content elevation-4">
      <div class="input_box">
        <div class="header-title">
          {{ $t('Data Analysis') }}
          <el-tooltip class="item" effect="dark" :content="$t('tagsView.refresh')">
            <i class="el-icon-refresh" style="font-size: 18px;margin-left: 3px;cursor: pointer;"
              @click="getDataAnalyseInfoFn"></i>
          </el-tooltip>
        </div>
        <div style="display: flex;align-items: center">
          <div style="display: flex;align-items: center;margin-right: 20px;">
            <div class="el-icon-arrow-left" style="cursor: pointer;" @click=handleLast()></div>
            <div class="data">
              <el-date-picker v-model="date" size="medium" :clearable=false type="date" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" append-to-body style="width: 120px" @change="handleDateChange">
              </el-date-picker>
            </div>
            <div class="el-icon-arrow-right" style="cursor: pointer;" @click=handleNext()></div>
          </div>
          <el-tooltip class="item" effect="dark" :content="$t('导出的数据为你自己选择的数据')">
            <el-button type="primary" @click="handleExportClick">{{ $t('common.exportReport') }}</el-button>
          </el-tooltip>
        </div>
      </div>
      <div class="echarts_box" v-loading="loading">
        <line-chart :xData="lineTimes" :yData="lineValues" v-if="lineValues.length" />
        <el-empty style="height: 100%;" v-else></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from '../../../dashboard/LineChart'

import { getProjectTree, getDataAnalyseInfo, getDataModule, exportAnalyse } from '@/api/test/dataanalysis'
import { handleExport } from '@/utils/export'
import { deviceTypeSingleOptions, deviceTypeGroupOptions } from '@/hook/useDeviceType'
import { isEmpty } from 'lodash'

export default {
  components: {
    LineChart
  },
  data() {
    return {
      deviceType: 1,
      params: [],
      paramOptions: [],
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      date: '',
      activeName: this.$t('home.pieRadio2'),
      treeData: [],
      queryInfo: {
        acs: []
      },
      lineTimes: [],
      lineValues: [],
      loading: false,
      paramData: [
        { name: this.$t(`contrast['jk_1025']`), id: 0, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1025' },
        { name: this.$t(`contrast['jk_1026']`), id: 1, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1026' },
        { name: this.$t(`contrast['jk_1027']`), id: 2, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1027' },
        { name: this.$t(`contrast['jk_1028']`), id: 3, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1028' },
        { name: this.$t(`contrast['jk_1029']`), id: 4, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1029' },
        { name: this.$t(`contrast['jk_1030']`), id: 5, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1030' },
        { name: this.$t(`contrast['jk_1031']`), id: 6, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1031' },
        { name: this.$t(`contrast['jk_1032']`), id: 7, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1032' },
        { name: this.$t(`contrast['jk_1033']`), id: 8, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1033' },
        { name: this.$t(`contrast['jk_1034']`), id: 9, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1034' },
        { name: this.$t(`contrast['jk_1035']`), id: 10, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1035' },
        { name: this.$t(`contrast['jk_1036']`), id: 11, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1036' },
        { name: this.$t(`contrast['jk_1037']`), id: 12, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1037' },
        { name: this.$t(`contrast['jk_1038']`), id: 13, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1038' },
        { name: this.$t(`contrast['jk_1039']`), id: 14, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1039' },
        { name: this.$t(`contrast['jk_1040']`), id: 15, parent: 'MonitorDisplay', unit: '', point: 'jk_1040' },
        { name: this.$t(`contrast['jk_1041']`), id: 16, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1041' },
        { name: this.$t(`contrast['jk_1042']`), id: 17, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1042' },
        { name: this.$t(`contrast['jk_1043']`), id: 18, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1043' },
        { name: this.$t(`contrast['jk_1044']`), id: 19, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1044' },
        { name: this.$t(`contrast['jk_1045']`), id: 20, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1045' },
        { name: this.$t(`contrast['jk_1046']`), id: 21, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1046' },
        { name: this.$t(`contrast['jk_1047']`), id: 22, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1047' },
        { name: this.$t(`contrast['jk_1048']`), id: 23, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1048' },
        { name: this.$t(`contrast['jk_1049']`), id: 24, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1049' },
        { name: this.$t(`contrast['jk_1050']`), id: 25, parent: 'MonitorDisplay', unit: '', point: 'jk_1050' },
        { name: this.$t(`contrast['jk_1051']`), id: 26, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1051' },
        { name: this.$t(`contrast['jk_1052']`), id: 27, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1052' },
        { name: this.$t(`contrast['jk_1053']`), id: 28, parent: 'MonitorDisplay', unit: 'kVA', point: 'jk_1053' },
        { name: this.$t(`contrast['jk_1072']`), id: 29, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1072' },
        { name: this.$t(`contrast['jk_1073']`), id: 30, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1073' },
        { name: this.$t(`contrast['jk_1074']`), id: 31, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1074' },
        { name: this.$t(`contrast['bms_4038']`), id: 0, parent: 'BMS', unit: '℃', point: 'bms_4038' },
        { name: this.$t(`contrast['bms_4055']`), id: 1, parent: 'BMS', unit: '%', point: 'bms_4055' },
        { name: this.$t(`contrast['bms_4027']`), id: 2, parent: 'BMS', unit: '℃', point: 'bms_4027' },
        { name: this.$t(`contrast['bms_4028']`), id: 3, parent: 'BMS', unit: '℃', point: 'bms_4028' },
        { name: this.$t(`contrast['bms_4024']`), id: 4, parent: 'BMS', unit: 'V', point: 'bms_4024' },
        { name: this.$t(`contrast['bms_4025']`), id: 5, parent: 'BMS', unit: 'V', point: 'bms_4025' },
        { name: this.$t(`contrast['bms_4022']`), id: 6, parent: 'BMS', unit: '%', point: 'bms_4022' },
        { name: this.$t(`contrast['bms_4020']`), id: 7, parent: 'BMS', unit: 'V', point: 'bms_4020' },
        { name: this.$t(`contrast['bms_4021']`), id: 8, parent: 'BMS', unit: 'A', point: 'bms_4021' },
        { name: this.$t(`contrast['bms_4056']`), id: 9, parent: 'BMS', unit: 'kW', point: 'bms_4056' },
        { name: this.$t(`contrast['bms_4030']`), id: 10, parent: 'BMS', unit: 'A', point: 'bms_4030' },
        { name: this.$t(`contrast['bms_4031']`), id: 11, parent: 'BMS', unit: 'A', point: 'bms_4031' },
        { name: this.$t(`contrast['AC_2039']`), id: 0, parent: 'AC', unit: 'V', point: 'AC_2039' },
        { name: this.$t(`contrast['AC_2040']`), id: 1, parent: 'AC', unit: 'A', point: 'AC_2040' },
        { name: this.$t(`contrast['AC_2041']`), id: 2, parent: 'AC', unit: 'kW', point: 'AC_2041' },
        { name: this.$t(`contrast['AC_2042']`), id: 3, parent: 'AC', unit: 'V', point: 'AC_2042' },
        { name: this.$t(`contrast['AC_2043']`), id: 4, parent: 'AC', unit: 'V', point: 'AC_2043' },
        { name: this.$t(`contrast['AC_2044']`), id: 5, parent: 'AC', unit: 'V', point: 'AC_2044' },
        { name: this.$t(`contrast['AC_2045']`), id: 6, parent: 'AC', unit: 'A', point: 'AC_2045' },
        { name: this.$t(`contrast['AC_2046']`), id: 7, parent: 'AC', unit: 'A', point: 'AC_2046' },
        { name: this.$t(`contrast['AC_2047']`), id: 8, parent: 'AC', unit: 'A', point: 'AC_2047' },
        { name: this.$t(`contrast['AC_2048']`), id: 9, parent: 'AC', unit: 'Hz', point: 'AC_2048' },
        { name: this.$t(`contrast['AC_2049']`), id: 10, parent: 'AC', unit: 'kW', point: 'AC_2049' },
        { name: this.$t(`contrast['AC_2050']`), id: 11, parent: 'AC', unit: 'kVar', point: 'AC_2050' },
        { name: this.$t(`contrast['AC_2051']`), id: 12, parent: 'AC', unit: '', point: 'AC_2051' },
        { name: this.$t(`contrast['AC_2056']`), id: 13, parent: 'AC', unit: '℃', point: 'AC_2056' },
        { name: this.$t(`contrast['AC_2061']`), id: 14, parent: 'AC', unit: 'kVA', point: 'AC_2061' },
        { name: this.$t(`contrast['DC_3012']`), id: 0, parent: 'DC', unit: 'V', point: 'DC_3012' },
        { name: this.$t(`contrast['DC_3013']`), id: 1, parent: 'DC', unit: 'A', point: 'DC_3013' },
        { name: this.$t(`contrast['DC_3014']`), id: 2, parent: 'DC', unit: 'kW', point: 'DC_3014' },
        { name: this.$t(`contrast['DC_3015']`), id: 3, parent: 'DC', unit: 'V', point: 'DC_3015' },
        { name: this.$t(`contrast['DC_3016']`), id: 4, parent: 'DC', unit: 'A', point: 'DC_3016' },
        { name: this.$t(`contrast['DC_3017']`), id: 5, parent: 'DC', unit: 'kW', point: 'DC_3017' },
        { name: this.$t(`contrast['DC_3018']`), id: 6, parent: 'DC', unit: 'V', point: 'DC_3018' },
        { name: this.$t(`contrast['DC_3019']`), id: 7, parent: 'DC', unit: 'A', point: 'DC_3019' },
        { name: this.$t(`contrast['DC_3020']`), id: 8, parent: 'DC', unit: 'kW', point: 'DC_3020' },
        { name: this.$t(`contrast['AC_2056']`), id: 9, parent: 'DC', unit: '℃', point: 'DC_3025' },
        { name: this.$t(`contrast['em_10003']`), id: 0, parent: this.$t('电表'), unit: '', point: 'em_10003' },
        { name: this.$t(`contrast['em_10005']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10005' },
        { name: this.$t(`contrast['em_10006']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10006' },
        { name: this.$t(`contrast['em_10007']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10007' },
        { name: this.$t(`contrast['em_10008']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10008' },
        { name: this.$t(`contrast['em_10009']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10009' },
        { name: this.$t(`contrast['em_10010']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10010' },
        { name: this.$t(`contrast['em_10011']`), id: 0, parent: this.$t('电表'), unit: '', point: 'em_10011' },
        { name: this.$t(`contrast['em_10012']`), id: 0, parent: this.$t('电表'), unit: 'kW', point: 'em_10012' },
        { name: this.$t(`contrast['em_10013']`), id: 0, parent: this.$t('电表'), unit: 'kVar', point: 'em_10013' },
        { name: this.$t(`contrast['em_10014']`), id: 0, parent: this.$t('电表'), unit: 'kVA', point: 'em_10014' },
        { name: this.$t(`contrast['em_10035']`), id: 0, parent: this.$t('电表'), unit: 'Hz', point: 'em_10035' },
        { name: this.$t(`contrast['em_10036']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10036' },
        { name: this.$t(`contrast['em_10037']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10037' },
        { name: this.$t(`contrast['em_10041']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10041' },
        { name: this.$t(`contrast['em_10042']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10042' },
        { name: this.$t(`contrast['em_10043']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10043' },
        { name: this.$t(`contrast['em_10015']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10015' },
        { name: this.$t(`contrast['em_10016']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10016' },
        { name: this.$t(`contrast['em_10017']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10017' },
        { name: this.$t(`contrast['em_10018']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10018' },
        { name: this.$t(`contrast['em_10019']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10019' },
        { name: this.$t(`contrast['em_10020']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10020' },
        { name: this.$t(`contrast['em_10021']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10021' },
        { name: this.$t(`contrast['em_10022']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10022' },
        { name: this.$t(`contrast['em_10023']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10023' },
        { name: this.$t(`contrast['em_10024']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10024' },
        { name: this.$t(`contrast['em_10025']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10025' },
        { name: this.$t(`contrast['em_10026']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10026' },
        { name: this.$t(`contrast['em_10027']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10027' },
        { name: this.$t(`contrast['em_10028']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10028' },
        { name: this.$t(`contrast['em_10029']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10029' },
        { name: this.$t(`contrast['em_10030']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10030' },
        { name: this.$t(`contrast['em_10031']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10031' },
        { name: this.$t(`contrast['em_10032']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10032' },
        { name: this.$t(`contrast['em_10033']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10033' },
        { name: this.$t(`contrast['em_10034']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10034' },
        { name: this.$t(`contrast['em_10038']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10038' },
        { name: this.$t(`contrast['em_10039']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10039' },
        { name: this.$t(`contrast['em_10040']`), id: 0, parent: this.$t('电表'), unit: 'kW', point: 'em_10040' },
        { name: this.$t(`contrast['chargingPile_19003']`), id: 0, parent: this.$t('充电桩'), unit: 'kW', point: 'chargingPile_19003' },
        { name: this.$t(`contrast['chargingPile_19004']`), id: 1, parent: this.$t('充电桩'), unit: 'V', point: 'chargingPile_19004' },
        { name: this.$t(`contrast['chargingPile_19005']`), id: 2, parent: this.$t('充电桩'), unit: 'A', point: 'chargingPile_19005' },
        { name: this.$t(`contrast['chargingPile_19006']`), id: 3, parent: this.$t('充电桩'), unit: 'kW', point: 'chargingPile_19006' },
        { name: this.$t(`contrast['chargingPile_19007']`), id: 4, parent: this.$t('充电桩'), unit: '%', point: 'chargingPile_19007' },
        { name: this.$t(`contrast['chargingPile_19008']`), id: 5, parent: this.$t('充电桩'), unit: 'kWh', point: 'chargingPile_19008' },
        { name: this.$t(`contrast['chargingPile_19009']`), id: 6, parent: this.$t('充电桩'), unit: 'CNY', point: 'chargingPile_19009' },
        { name: this.$t(`contrast['chargingPile_19010']`), id: 7, parent: this.$t('充电桩'), unit: '℃', point: 'chargingPile_19010' },
        { name: this.$t(`contrast['chargingPile_19015']`), id: 8, parent: this.$t('充电桩'), unit: 'V', point: 'chargingPile_19015' },
        { name: this.$t(`contrast['chargingPile_19016']`), id: 9, parent: this.$t('充电桩'), unit: 'A', point: 'chargingPile_19016' },
        { name: this.$t(`contrast['chargingPile_19017']`), id: 10, parent: this.$t('充电桩'), unit: 'kW', point: 'chargingPile_19017' },
        { name: this.$t(`contrast['chargingPile_19018']`), id: 11, parent: this.$t('充电桩'), unit: '%', point: 'chargingPile_19018' },
        { name: this.$t(`contrast['chargingPile_19019']`), id: 12, parent: this.$t('充电桩'), unit: 'kWh', point: 'chargingPile_19019' },
        { name: this.$t(`contrast['chargingPile_19020']`), id: 13, parent: this.$t('充电桩'), unit: 'CNY', point: 'chargingPile_19020' },
        { name: this.$t(`contrast['chargingPile_19021']`), id: 14, parent: this.$t('充电桩'), unit: '℃', point: 'chargingPile_19021' },
        { name: this.$t(`contrast['sts_3507']`), id: 0, parent: 'STS', unit: 'V', point: 'sts_3507' },
        { name: this.$t(`contrast['sts_3508']`), id: 1, parent: 'STS', unit: 'V', point: 'sts_3508' },
        { name: this.$t(`contrast['sts_3509']`), id: 2, parent: 'STS', unit: 'V', point: 'sts_3509' },
        { name: this.$t(`contrast['sts_3510']`), id: 3, parent: 'STS', unit: '℃', point: 'sts_3510' },
        { name: this.$t(`contrast['sts_3511']`), id: 4, parent: 'STS', unit: 'A', point: 'sts_3511' },
        { name: this.$t(`contrast['sts_3512']`), id: 5, parent: 'STS', unit: 'A', point: 'sts_3512' },
        { name: this.$t(`contrast['sts_3513']`), id: 6, parent: 'STS', unit: 'A', point: 'sts_3513' },
        { name: this.$t(`contrast['sts_3514']`), id: 7, parent: 'STS', unit: '℃', point: 'sts_3514' },
        { name: this.$t(`contrast['sts_3515']`), id: 8, parent: 'STS', unit: 'V', point: 'sts_3515' },
        { name: this.$t(`contrast['sts_3516']`), id: 9, parent: 'STS', unit: 'V', point: 'sts_3516' },
        { name: this.$t(`contrast['sts_3517']`), id: 10, parent: 'STS', unit: 'V', point: 'sts_3517' },
        { name: this.$t(`contrast['sts_3518']`), id: 11, parent: 'STS', unit: 'Hz', point: 'sts_3518' },
        { name: this.$t(`contrast['sts_3519']`), id: 12, parent: 'STS', unit: 'kW', point: 'sts_3519' },
        { name: this.$t(`contrast['sts_3520']`), id: 13, parent: 'STS', unit: 'kW', point: 'sts_3520' },
        { name: this.$t(`contrast['sts_3521']`), id: 14, parent: 'STS', unit: 'kW', point: 'sts_3521' },
        { name: this.$t(`contrast['sts_3522']`), id: 15, parent: 'STS', unit: 'kW', point: 'sts_3522' },
        { name: this.$t(`contrast['sts_3523']`), id: 16, parent: 'STS', unit: 'kvar', point: 'sts_3523' },
        { name: this.$t(`contrast['sts_3524']`), id: 17, parent: 'STS', unit: 'kvar', point: 'sts_3524' },
        { name: this.$t(`contrast['sts_3525']`), id: 18, parent: 'STS', unit: 'kvar', point: 'sts_3525' },
        { name: this.$t(`contrast['sts_3526']`), id: 19, parent: 'STS', unit: 'kvar', point: 'sts_3526' },
        { name: this.$t(`contrast['sts_3527']`), id: 20, parent: 'STS', unit: '', point: 'sts_3527' },
        { name: this.$t(`contrast['sts_3528']`), id: 21, parent: 'STS', unit: '', point: 'sts_3528' },
        { name: this.$t(`contrast['sts_3529']`), id: 22, parent: 'STS', unit: '', point: 'sts_3529' },
        { name: this.$t(`contrast['sts_3530']`), id: 23, parent: 'STS', unit: '', point: 'sts_3530' },
        { name: this.$t(`contrast['sts_3531']`), id: 24, parent: 'STS', unit: 'kVA', point: 'sts_3531' },
        { name: this.$t(`contrast['sts_3532']`), id: 25, parent: 'STS', unit: 'kVA', point: 'sts_3532' },
        { name: this.$t(`contrast['sts_3533']`), id: 26, parent: 'STS', unit: 'kVA', point: 'sts_3533' },
        { name: this.$t(`contrast['sts_3534']`), id: 27, parent: 'STS', unit: 'kVA', point: 'sts_3534' },
        { name: this.$t(`contrast['bms_4038']`), id: 0, parent: 'BMS-BAU', unit: '℃', point: 'bms_4038' },
        { name: this.$t(`contrast['bms_4055']`), id: 1, parent: 'BMS-BAU', unit: '%', point: 'bms_4055' },
        { name: this.$t(`contrast['bms_4027']`), id: 2, parent: 'BMS-BAU', unit: '℃', point: 'bms_4027' },
        { name: this.$t(`contrast['bms_4028']`), id: 3, parent: 'BMS-BAU', unit: '℃', point: 'bms_4028' },
        { name: this.$t(`contrast['bms_4024']`), id: 4, parent: 'BMS-BAU', unit: 'V', point: 'bms_4024' },
        { name: this.$t(`contrast['bms_4025']`), id: 5, parent: 'BMS-BAU', unit: 'V', point: 'bms_4025' },
        { name: this.$t(`contrast['bms_4022']`), id: 6, parent: 'BMS-BAU', unit: '%', point: 'bms_4022' },
        { name: this.$t(`contrast['bms_4020']`), id: 7, parent: 'BMS-BAU', unit: 'V', point: 'bms_4020' },
        { name: this.$t(`contrast['bms_4021']`), id: 8, parent: 'BMS-BAU', unit: 'A', point: 'bms_4021' },
        { name: this.$t(`contrast['bms_4056']`), id: 9, parent: 'BMS-BAU', unit: 'kW', point: 'bms_4056' },
        { name: this.$t(`contrast['bms_4030']`), id: 10, parent: 'BMS-BAU', unit: 'A', point: 'bms_4030' },
        { name: this.$t(`contrast['bms_4031']`), id: 11, parent: 'BMS-BAU', unit: 'A', point: 'bms_4031' },
      ],
      deviceNum: 0,
      paramNum: 0,
      isEmpty: false,
      control1: {
        name: this.$t('monitor.control'),
        id: 'MonitorDisplay',
        children: [
          { name: this.$t(`contrast['jk_1025']`), id: 0, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1025' },
          { name: this.$t(`contrast['jk_1026']`), id: 1, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1026' },
          { name: this.$t(`contrast['jk_1027']`), id: 2, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1027' },
          { name: this.$t(`contrast['jk_1028']`), id: 3, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1028' },
          { name: this.$t(`contrast['jk_1029']`), id: 4, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1029' },
          { name: this.$t(`contrast['jk_1030']`), id: 5, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1030' },
          { name: this.$t(`contrast['jk_1031']`), id: 6, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1031' },
          { name: this.$t(`contrast['jk_1032']`), id: 7, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1032' },
          { name: this.$t(`contrast['jk_1033']`), id: 8, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1033' },
          { name: this.$t(`contrast['jk_1034']`), id: 9, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1034' },
          { name: this.$t(`contrast['jk_1035']`), id: 10, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1035' },
          { name: this.$t(`contrast['jk_1036']`), id: 11, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1036' },
          { name: this.$t(`contrast['jk_1037']`), id: 12, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1037' },
          { name: this.$t(`contrast['jk_1038']`), id: 13, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1038' },
          { name: this.$t(`contrast['jk_1039']`), id: 14, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1039' },
          { name: this.$t(`contrast['jk_1040']`), id: 15, parent: 'MonitorDisplay', unit: '', point: 'jk_1040' },
          { name: this.$t(`contrast['jk_1041']`), id: 16, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1041' },
          { name: this.$t(`contrast['jk_1042']`), id: 17, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1042' },
          { name: this.$t(`contrast['jk_1043']`), id: 18, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1043' },
          { name: this.$t(`contrast['jk_1044']`), id: 19, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1044' },
          { name: this.$t(`contrast['jk_1045']`), id: 20, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1045' },
          { name: this.$t(`contrast['jk_1046']`), id: 21, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1046' },
          { name: this.$t(`contrast['jk_1047']`), id: 22, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1047' },
          { name: this.$t(`contrast['jk_1048']`), id: 23, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1048' },
          { name: this.$t(`contrast['jk_1049']`), id: 24, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1049' },
          { name: this.$t(`contrast['jk_1050']`), id: 25, parent: 'MonitorDisplay', unit: '', point: 'jk_1050' },
          { name: this.$t(`contrast['jk_1051']`), id: 26, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1051' },
          { name: this.$t(`contrast['jk_1052']`), id: 27, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1052' },
          { name: this.$t(`contrast['jk_1053']`), id: 28, parent: 'MonitorDisplay', unit: 'kVA', point: 'jk_1053' },
          { name: this.$t(`contrast['jk_1072']`), id: 29, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1072' },
          { name: this.$t(`contrast['jk_1073']`), id: 30, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1073' },
          { name: this.$t(`contrast['jk_1074']`), id: 31, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1074' }
        ]
      },
      control2: {
        name: this.$t('monitor.control'),
        id: 'MonitorDisplay',
        children: [
          { name: this.$t(`contrast['jk_1025']`), id: 0, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1025' },
          { name: this.$t(`contrast['jk_1026']`), id: 1, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1026' },
          { name: this.$t(`contrast['jk_1027']`), id: 2, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1027' },
          { name: this.$t(`contrast['jk_1028']`), id: 3, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1028' },
          { name: this.$t(`contrast['jk_1029']`), id: 4, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1029' },
          { name: this.$t(`contrast['jk_1030']`), id: 5, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1030' },
          { name: this.$t(`contrast['jk_1031']`), id: 6, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1031' },
          { name: this.$t(`contrast['jk_1032']`), id: 7, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1032' },
          { name: this.$t(`contrast['jk_1033']`), id: 8, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1033' },
          { name: this.$t(`contrast['jk_1034']`), id: 9, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1034' },
          { name: this.$t(`contrast['jk_1035']`), id: 10, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1035' },
          { name: this.$t(`contrast['jk_1036']`), id: 11, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1036' },
          { name: this.$t(`contrast['jk_1037']`), id: 12, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1037' },
          { name: this.$t(`contrast['jk_1038']`), id: 13, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1038' },
          { name: this.$t(`contrast['jk_1039']`), id: 14, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1039' },
          { name: this.$t(`contrast['jk_1040']`), id: 15, parent: 'MonitorDisplay', unit: '', point: 'jk_1040' },
          { name: this.$t(`contrast['jk_1041']`), id: 16, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1041' },
          { name: this.$t(`contrast['jk_1042']`), id: 17, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1042' },
          { name: this.$t(`contrast['jk_1043']`), id: 18, parent: 'MonitorDisplay', unit: 'V', point: 'jk_1043' },
          { name: this.$t(`contrast['jk_1044']`), id: 19, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1044' },
          { name: this.$t(`contrast['jk_1045']`), id: 20, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1045' },
          { name: this.$t(`contrast['jk_1046']`), id: 21, parent: 'MonitorDisplay', unit: 'A', point: 'jk_1046' },
          { name: this.$t(`contrast['jk_1047']`), id: 22, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1047' },
          { name: this.$t(`contrast['jk_1048']`), id: 23, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1048' },
          { name: this.$t(`contrast['jk_1049']`), id: 24, parent: 'MonitorDisplay', unit: 'Hz', point: 'jk_1049' },
          { name: this.$t(`contrast['jk_1050']`), id: 25, parent: 'MonitorDisplay', unit: '', point: 'jk_1050' },
          { name: this.$t(`contrast['jk_1051']`), id: 26, parent: 'MonitorDisplay', unit: 'kVar', point: 'jk_1051' },
          { name: this.$t(`contrast['jk_1052']`), id: 27, parent: 'MonitorDisplay', unit: 'kW', point: 'jk_1052' },
          { name: this.$t(`contrast['jk_1053']`), id: 28, parent: 'MonitorDisplay', unit: 'kVA', point: 'jk_1053' },
        ]
      },
      bmsChild: [
        { name: this.$t(`contrast['bms_4038']`), id: 0, parent: 'BMS', unit: '℃', point: 'bms_4038' },
        { name: this.$t(`contrast['bms_4055']`), id: 1, parent: 'BMS', unit: '%', point: 'bms_4055' },
        { name: this.$t(`contrast['bms_4027']`), id: 2, parent: 'BMS', unit: '℃', point: 'bms_4027' },
        { name: this.$t(`contrast['bms_4028']`), id: 3, parent: 'BMS', unit: '℃', point: 'bms_4028' },
        { name: this.$t(`contrast['bms_4024']`), id: 4, parent: 'BMS', unit: 'V', point: 'bms_4024' },
        { name: this.$t(`contrast['bms_4025']`), id: 5, parent: 'BMS', unit: 'V', point: 'bms_4025' },
        { name: this.$t(`contrast['bms_4022']`), id: 6, parent: 'BMS', unit: '%', point: 'bms_4022' },
        { name: this.$t(`contrast['bms_4020']`), id: 7, parent: 'BMS', unit: 'V', point: 'bms_4020' },
        { name: this.$t(`contrast['bms_4021']`), id: 8, parent: 'BMS', unit: 'A', point: 'bms_4021' },
        { name: this.$t(`contrast['bms_4056']`), id: 9, parent: 'BMS', unit: 'kW', point: 'bms_4056' },
        { name: this.$t(`contrast['bms_4030']`), id: 10, parent: 'BMS', unit: 'A', point: 'bms_4030' },
        { name: this.$t(`contrast['bms_4031']`), id: 11, parent: 'BMS', unit: 'A', point: 'bms_4031' },
      ],
      ['bms-bauChild']: [
        { name: this.$t(`contrast['bms_4038']`), id: 0, parent: 'BMS-BAU', unit: '℃', point: 'bms_4038' },
        { name: this.$t(`contrast['bms_4055']`), id: 1, parent: 'BMS-BAU', unit: '%', point: 'bms_4055' },
        { name: this.$t(`contrast['bms_4027']`), id: 2, parent: 'BMS-BAU', unit: '℃', point: 'bms_4027' },
        { name: this.$t(`contrast['bms_4028']`), id: 3, parent: 'BMS-BAU', unit: '℃', point: 'bms_4028' },
        { name: this.$t(`contrast['bms_4024']`), id: 4, parent: 'BMS-BAU', unit: 'V', point: 'bms_4024' },
        { name: this.$t(`contrast['bms_4025']`), id: 5, parent: 'BMS-BAU', unit: 'V', point: 'bms_4025' },
        { name: this.$t(`contrast['bms_4022']`), id: 6, parent: 'BMS-BAU', unit: '%', point: 'bms_4022' },
        { name: this.$t(`contrast['bms_4020']`), id: 7, parent: 'BMS-BAU', unit: 'V', point: 'bms_4020' },
        { name: this.$t(`contrast['bms_4021']`), id: 8, parent: 'BMS-BAU', unit: 'A', point: 'bms_4021' },
        { name: this.$t(`contrast['bms_4056']`), id: 9, parent: 'BMS-BAU', unit: 'kW', point: 'bms_4056' },
        { name: this.$t(`contrast['bms_4030']`), id: 10, parent: 'BMS-BAU', unit: 'A', point: 'bms_4030' },
        { name: this.$t(`contrast['bms_4031']`), id: 11, parent: 'BMS-BAU', unit: 'A', point: 'bms_4031' },
      ],
      macChild: [
        { name: this.$t(`contrast['AC_2039']`), id: 0, parent: 'MAC', unit: 'V', point: 'AC_2039' },
        { name: this.$t(`contrast['AC_2040']`), id: 1, parent: 'MAC', unit: 'A', point: 'AC_2040' },
        { name: this.$t(`contrast['AC_2041']`), id: 2, parent: 'MAC', unit: 'kW', point: 'AC_2041' },
        { name: this.$t(`contrast['AC_2042']`), id: 3, parent: 'MAC', unit: 'V', point: 'AC_2042' },
        { name: this.$t(`contrast['AC_2043']`), id: 4, parent: 'MAC', unit: 'V', point: 'AC_2043' },
        { name: this.$t(`contrast['AC_2044']`), id: 5, parent: 'MAC', unit: 'V', point: 'AC_2044' },
        { name: this.$t(`contrast['AC_2045']`), id: 6, parent: 'MAC', unit: 'A', point: 'AC_2045' },
        { name: this.$t(`contrast['AC_2046']`), id: 7, parent: 'MAC', unit: 'A', point: 'AC_2046' },
        { name: this.$t(`contrast['AC_2047']`), id: 8, parent: 'MAC', unit: 'A', point: 'AC_2047' },
        { name: this.$t(`contrast['AC_2048']`), id: 9, parent: 'MAC', unit: 'Hz', point: 'AC_2048' },
        { name: this.$t(`contrast['AC_2049']`), id: 10, parent: 'MAC', unit: 'kW', point: 'AC_2049' },
        { name: this.$t(`contrast['AC_2050']`), id: 11, parent: 'MAC', unit: 'kVar', point: 'AC_2050' },
        { name: this.$t(`contrast['AC_2051']`), id: 12, parent: 'MAC', unit: '', point: 'AC_2051' },
        { name: this.$t(`contrast['AC_2056']`), id: 13, parent: 'MAC', unit: '℃', point: 'AC_2056' },
        { name: this.$t(`contrast['AC_2061']`), id: 14, parent: 'MAC', unit: 'kVA', point: 'AC_2061' },
      ],
      mdcChild: [
        { name: this.$t(`contrast['DC_3012']`), id: 0, parent: 'MDC', unit: 'V', point: 'DC_3012' },
        { name: this.$t(`contrast['DC_3013']`), id: 1, parent: 'MDC', unit: 'A', point: 'DC_3013' },
        { name: this.$t(`contrast['DC_3014']`), id: 2, parent: 'MDC', unit: 'kW', point: 'DC_3014' },
        { name: this.$t(`contrast['DC_3015']`), id: 3, parent: 'MDC', unit: 'V', point: 'DC_3015' },
        { name: this.$t(`contrast['DC_3016']`), id: 4, parent: 'MDC', unit: 'A', point: 'DC_3016' },
        { name: this.$t(`contrast['DC_3017']`), id: 5, parent: 'MDC', unit: 'kW', point: 'DC_3017' },
        { name: this.$t(`contrast['DC_3018']`), id: 6, parent: 'MDC', unit: 'V', point: 'DC_3018' },
        { name: this.$t(`contrast['DC_3019']`), id: 7, parent: 'MDC', unit: 'A', point: 'DC_3019' },
        { name: this.$t(`contrast['DC_3020']`), id: 8, parent: 'MDC', unit: 'kW', point: 'DC_3020' },
        { name: this.$t(`contrast['AC_2056']`), id: 9, parent: 'MDC', unit: '℃', point: 'DC_3025' },
      ],
      electricmeter1Child: [ // pcc电表、储能电表、光伏电表、负载电表
        { name: this.$t(`contrast['em_10003']`), id: 0, parent: this.$t('电表'), unit: '', point: 'em_10003' },
        { name: this.$t(`contrast['em_10005']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10005' },
        { name: this.$t(`contrast['em_10006']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10006' },
        { name: this.$t(`contrast['em_10007']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10007' },
        { name: this.$t(`contrast['em_10008']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10008' },
        { name: this.$t(`contrast['em_10009']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10009' },
        { name: this.$t(`contrast['em_10010']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10010' },
        { name: this.$t(`contrast['em_10011']`), id: 0, parent: this.$t('电表'), unit: '', point: 'em_10011' },
        { name: this.$t(`contrast['em_10012']`), id: 0, parent: this.$t('电表'), unit: 'kW', point: 'em_10012' },
        { name: this.$t(`contrast['em_10013']`), id: 0, parent: this.$t('电表'), unit: 'kVar', point: 'em_10013' },
        { name: this.$t(`contrast['em_10014']`), id: 0, parent: this.$t('电表'), unit: 'kVA', point: 'em_10014' },
        { name: this.$t(`contrast['em_10035']`), id: 0, parent: this.$t('电表'), unit: 'Hz', point: 'em_10035' },
        { name: this.$t(`contrast['em_10036']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10036' },
        { name: this.$t(`contrast['em_10037']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10037' },
        { name: this.$t(`contrast['em_10041']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10041' },
        { name: this.$t(`contrast['em_10042']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10042' },
        { name: this.$t(`contrast['em_10043']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10043' },
      ],
      electricmeter6Child: [ // 直流电表
        { name: this.$t(`contrast['em_10003']`), id: 0, parent: this.$t('电表'), unit: '', point: 'em_10003' },
        { name: this.$t(`contrast['em_10038']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10038' },
        { name: this.$t(`contrast['em_10039']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10039' },
        { name: this.$t(`contrast['em_10040']`), id: 0, parent: this.$t('电表'), unit: 'kW', point: 'em_10040' },
      ],
      electricmeter4Child: [ //计量点电表
        { name: this.$t(`contrast['em_10003']`), id: 0, parent: this.$t('电表'), unit: '', point: 'em_10003' },
        { name: this.$t(`contrast['em_10005']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10005' },
        { name: this.$t(`contrast['em_10006']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10006' },
        { name: this.$t(`contrast['em_10007']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10007' },
        { name: this.$t(`contrast['em_10008']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10008' },
        { name: this.$t(`contrast['em_10009']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10009' },
        { name: this.$t(`contrast['em_10010']`), id: 0, parent: this.$t('电表'), unit: 'A', point: 'em_10010' },
        { name: this.$t(`contrast['em_10011']`), id: 0, parent: this.$t('电表'), unit: '', point: 'em_10011' },
        { name: this.$t(`contrast['em_10012']`), id: 0, parent: this.$t('电表'), unit: 'kW', point: 'em_10012' },
        { name: this.$t(`contrast['em_10013']`), id: 0, parent: this.$t('电表'), unit: 'kVar', point: 'em_10013' },
        { name: this.$t(`contrast['em_10014']`), id: 0, parent: this.$t('电表'), unit: 'kVA', point: 'em_10014' },
        { name: this.$t(`contrast['em_10035']`), id: 0, parent: this.$t('电表'), unit: 'Hz', point: 'em_10035' },
        { name: this.$t(`contrast['em_10036']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10036' },
        { name: this.$t(`contrast['em_10037']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10037' },
        { name: this.$t(`contrast['em_10041']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10041' },
        { name: this.$t(`contrast['em_10042']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10042' },
        { name: this.$t(`contrast['em_10043']`), id: 0, parent: this.$t('电表'), unit: 'V', point: 'em_10043' },
        { name: this.$t(`contrast['em_10015']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10015' },
        { name: this.$t(`contrast['em_10016']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10016' },
        { name: this.$t(`contrast['em_10017']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10017' },
        { name: this.$t(`contrast['em_10018']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10018' },
        { name: this.$t(`contrast['em_10019']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10019' },
        { name: this.$t(`contrast['em_10020']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10020' },
        { name: this.$t(`contrast['em_10021']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10021' },
        { name: this.$t(`contrast['em_10022']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10022' },
        { name: this.$t(`contrast['em_10023']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10023' },
        { name: this.$t(`contrast['em_10024']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10024' },
        { name: this.$t(`contrast['em_10025']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10025' },
        { name: this.$t(`contrast['em_10026']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10026' },
        { name: this.$t(`contrast['em_10027']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10027' },
        { name: this.$t(`contrast['em_10028']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10028' },
        { name: this.$t(`contrast['em_10029']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10029' },
        { name: this.$t(`contrast['em_10030']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10030' },
        { name: this.$t(`contrast['em_10031']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10031' },
        { name: this.$t(`contrast['em_10032']`), id: 0, parent: this.$t('电表'), unit: 'kWh', point: 'em_10032' },
        { name: this.$t(`contrast['em_10033']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10033' },
        { name: this.$t(`contrast['em_10034']`), id: 0, parent: this.$t('电表'), unit: 'kVarh', point: 'em_10034' },
      ],
      chargingpileChild: [
        { name: this.$t(`contrast['chargingPile_19003']`), id: 0, parent: this.$t('充电桩'), unit: 'kW', point: 'chargingPile_19003' },
        { name: this.$t(`contrast['chargingPile_19004']`), id: 1, parent: this.$t('充电桩'), unit: 'V', point: 'chargingPile_19004' },
        { name: this.$t(`contrast['chargingPile_19005']`), id: 2, parent: this.$t('充电桩'), unit: 'A', point: 'chargingPile_19005' },
        { name: this.$t(`contrast['chargingPile_19006']`), id: 3, parent: this.$t('充电桩'), unit: 'kW', point: 'chargingPile_19006' },
        { name: this.$t(`contrast['chargingPile_19007']`), id: 4, parent: this.$t('充电桩'), unit: '%', point: 'chargingPile_19007' },
        { name: this.$t(`contrast['chargingPile_19008']`), id: 5, parent: this.$t('充电桩'), unit: 'kWh', point: 'chargingPile_19008' },
        { name: this.$t(`contrast['chargingPile_19009']`), id: 6, parent: this.$t('充电桩'), unit: 'CNY', point: 'chargingPile_19009' },
        { name: this.$t(`contrast['chargingPile_19010']`), id: 7, parent: this.$t('充电桩'), unit: '℃', point: 'chargingPile_19010' },
        { name: this.$t(`contrast['chargingPile_19015']`), id: 8, parent: this.$t('充电桩'), unit: 'V', point: 'chargingPile_19015' },
        { name: this.$t(`contrast['chargingPile_19016']`), id: 9, parent: this.$t('充电桩'), unit: 'A', point: 'chargingPile_19016' },
        { name: this.$t(`contrast['chargingPile_19017']`), id: 10, parent: this.$t('充电桩'), unit: 'kW', point: 'chargingPile_19017' },
        { name: this.$t(`contrast['chargingPile_19018']`), id: 11, parent: this.$t('充电桩'), unit: '%', point: 'chargingPile_19018' },
        { name: this.$t(`contrast['chargingPile_19019']`), id: 12, parent: this.$t('充电桩'), unit: 'kWh', point: 'chargingPile_19019' },
        { name: this.$t(`contrast['chargingPile_19020']`), id: 13, parent: this.$t('充电桩'), unit: 'CNY', point: 'chargingPile_19020' },
        { name: this.$t(`contrast['chargingPile_19021']`), id: 14, parent: this.$t('充电桩'), unit: '℃', point: 'chargingPile_19021' },
      ],
      stsChild: [
        { name: this.$t(`contrast['sts_3507']`), id: 0, parent: 'STS', unit: 'V', point: 'sts_3507' },
        { name: this.$t(`contrast['sts_3508']`), id: 1, parent: 'STS', unit: 'V', point: 'sts_3508' },
        { name: this.$t(`contrast['sts_3509']`), id: 2, parent: 'STS', unit: 'V', point: 'sts_3509' },
        { name: this.$t(`contrast['sts_3510']`), id: 3, parent: 'STS', unit: '℃', point: 'sts_3510' },
        { name: this.$t(`contrast['sts_3511']`), id: 4, parent: 'STS', unit: 'A', point: 'sts_3511' },
        { name: this.$t(`contrast['sts_3512']`), id: 5, parent: 'STS', unit: 'A', point: 'sts_3512' },
        { name: this.$t(`contrast['sts_3513']`), id: 6, parent: 'STS', unit: 'A', point: 'sts_3513' },
        { name: this.$t(`contrast['sts_3514']`), id: 7, parent: 'STS', unit: '℃', point: 'sts_3514' },
        { name: this.$t(`contrast['sts_3515']`), id: 8, parent: 'STS', unit: 'V', point: 'sts_3515' },
        { name: this.$t(`contrast['sts_3516']`), id: 9, parent: 'STS', unit: 'V', point: 'sts_3516' },
        { name: this.$t(`contrast['sts_3517']`), id: 10, parent: 'STS', unit: 'V', point: 'sts_3517' },
        { name: this.$t(`contrast['sts_3518']`), id: 11, parent: 'STS', unit: 'Hz', point: 'sts_3518' },
        { name: this.$t(`contrast['sts_3519']`), id: 12, parent: 'STS', unit: 'kW', point: 'sts_3519' },
        { name: this.$t(`contrast['sts_3520']`), id: 13, parent: 'STS', unit: 'kW', point: 'sts_3520' },
        { name: this.$t(`contrast['sts_3521']`), id: 14, parent: 'STS', unit: 'kW', point: 'sts_3521' },
        { name: this.$t(`contrast['sts_3522']`), id: 15, parent: 'STS', unit: 'kW', point: 'sts_3522' },
        { name: this.$t(`contrast['sts_3523']`), id: 16, parent: 'STS', unit: 'kvar', point: 'sts_3523' },
        { name: this.$t(`contrast['sts_3524']`), id: 17, parent: 'STS', unit: 'kvar', point: 'sts_3524' },
        { name: this.$t(`contrast['sts_3525']`), id: 18, parent: 'STS', unit: 'kvar', point: 'sts_3525' },
        { name: this.$t(`contrast['sts_3526']`), id: 19, parent: 'STS', unit: 'kvar', point: 'sts_3526' },
        { name: this.$t(`contrast['sts_3527']`), id: 20, parent: 'STS', unit: '', point: 'sts_3527' },
        { name: this.$t(`contrast['sts_3528']`), id: 21, parent: 'STS', unit: '', point: 'sts_3528' },
        { name: this.$t(`contrast['sts_3529']`), id: 22, parent: 'STS', unit: '', point: 'sts_3529' },
        { name: this.$t(`contrast['sts_3530']`), id: 23, parent: 'STS', unit: '', point: 'sts_3530' },
        { name: this.$t(`contrast['sts_3531']`), id: 24, parent: 'STS', unit: 'kVA', point: 'sts_3531' },
        { name: this.$t(`contrast['sts_3532']`), id: 25, parent: 'STS', unit: 'kVA', point: 'sts_3532' },
        { name: this.$t(`contrast['sts_3533']`), id: 26, parent: 'STS', unit: 'kVA', point: 'sts_3533' },
        { name: this.$t(`contrast['sts_3534']`), id: 27, parent: 'STS', unit: 'kVA', point: 'sts_3534' },
      ],
      treeLoading: false
    };
  },
  mounted() {
    this.date = this.$moment(new Date()).format('yyyy-MM-DD')
    this.getProjectTreeFn()
  },
  computed: {
    defaultDeviceCheckedKeys() {
      if (this.data.length && this.data[0].children[0]) {
        let currentDeviceNode = null
        const mapDeviceFn = (list) => {
          list.forEach(item => {
            if (item.deviceType == this.deviceType && !currentDeviceNode) currentDeviceNode = item
            if (item.children.length) mapDeviceFn(item.children)
          })
        }
        mapDeviceFn(this.data)
        if (!currentDeviceNode) {
          this.queryInfo.acs = []
          this.lineValues = []
          return []
        }
        this.queryInfo.acs = [currentDeviceNode.ac]
        return [currentDeviceNode.treeId]
      } else {
        return []
      }
    },
    defaultParamCheckedKeys() {
      if (this.paramOptions.length && this.paramOptions[0].children) {
        let paramChildren = this.paramOptions[0].children
        return [paramChildren[0].name, paramChildren[1].name, paramChildren[2].name]
      } else {
        return []
      }
    },
    typeOptions() {
      return [...deviceTypeSingleOptions, ...deviceTypeGroupOptions]
    },
    getEleType() {
      return (type) => {
        if (type == '1') {
          return this.$t('计量点电表')
        } else if (type == '2') {
          return this.$t('储能电表')
        } else if (type == '3') {
          return this.$t('PCC电表')
        } else if (type == '4') {
          return this.$t('光伏电表')
        } else if (type == '5') {
          return this.$t('负载电表')
        } else if (type == '6') {
          return this.$t('直流电表')
        } else if (type == '7') {
          return this.$t('市电电表')
        }
      }
    }
  },
  methods: {
    getProjectTreeFn() {
      this.data = []
      this.treeLoading = true
      getProjectTree({
        deviceType: this.deviceType
      }).then(res => {
        if (res.code !== 200) {
          this.treeLoading = false
          return this.$message({
            type: 'error',
            message: res.msg
          })
        }
        let items = res.data
        items.forEach(item => {
          item.disabled = true
        });
        this.data = res.data
        this.treeData = this.data
        if (!this.treeData.length) {
          this.paramOptions = []
          this.lineValues = []
          this.deviceNum = 0
          this.paramNum = 0
          this.isEmpty = true
          this.treeLoading = false
          return this.$message({
            type: 'warn',
            message: this.$t('暂无设备')
          })
        } else {
          this.isEmpty = false
        }
        this.treeLoading = false
        // if (this.defaultDeviceCheckedKeys.length) this.getDataModuleFn()
      }).catch(() => {
        this.treeLoading = false
      })
    },
    getDataAnalyseInfoFn() {
      this.loading = true
      if (!this.queryInfo.acs.length) {
        this.loading = false
        return this.$message({
          type: 'error',
          message: this.$t('请选择设备')
        })
      } else {
        this.isEmpty = false
      }
      let checkedParams = this.$refs.treeParamRef.getCheckedNodes()
      if (!checkedParams.length) {
        this.isEmpty = true
        this.loading = false
        return this.$message({
          type: 'error',
          message: this.$t('请选择参数')
        })
      } else {
        this.isEmpty = false
      }
      let MonitorDisplay = {
        [this.controlDc]: checkedParams.filter(item => item.parent == 'MonitorDisplay').map(item => item.id)
      }
      const getData = (type) => {
        let obj = _.groupBy(checkedParams.filter(item => item.parent == type), 'dc')
        for (const key in obj) {
          obj[key] = obj[key].map(item => item.id)
        }

        return obj
      }
      let electricMeterList = []
      let obj = _.groupBy(checkedParams.filter(item => item.parent == this.$t('电表')), 'dc')
      for (const key in obj) {
        electricMeterList.push({
          dc: key,
          point: obj[key].map(item => item.point)
        })
      }
      /**
       * 最多30条数据
       */
      let acsLength = this.queryInfo.acs.length
      let paramLength = MonitorDisplay.length + getData('BMS').length
      if ((acsLength * paramLength) > 30) return this.$message({
        type: 'warning',
        message: this.$t('最多只能对比30条数据哦')
      })
      this.deviceNum = this.queryInfo.acs.length
      this.paramNum = checkedParams.length
      getDataAnalyseInfo({
        date: this.date,
        ...this.queryInfo,
        map: {
          MonitorDisplay,
          BMS: getData('BMS'),
          ['BMS-BAU']: getData('BMS-BAU'),
          MAC: getData('MAC'),
          MDC: getData('MDC'),
          chargingPile: getData(this.$t('充电桩')),
          STS: getData('STS')
        },
        electricMeterList
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let data = res.data
        this.lineTimes = data[0].vos.map(item => item.sdt)
        this.lineValues = []
        data.forEach((item, index) => {
          item.values = item.vos.map(item => item.value)
          this.lineValues.push({
            name: this.$t(`contrast['${item.point}']`),
            values: item.values,
            ac: item.ac,
            unit: this.paramData.find(paramItem => paramItem.point == item.point).unit,
            module: item.module,
            moduleName: item.moduleName
          })
        })
        this.loading = false
      }).catch(err => {
        this.loading = false
      })
    },
    handleChange(v) {
      this.getProjectTreeFn()
    },
    handleTabClick() {
      if (this.activeName == this.$t('home.pieRadio2')) {
        this.treeData = this.data
      } else {
        this.treeData = this.paramOptions
      }
    },
    handleLast() {
      this.date = this.$moment(this.date).subtract(1).format('yyyy-MM-DD')
      this.getDataAnalyseInfoFn()
    },
    handleNext() {
      this.date = this.$moment(this.date).add(1, 'day').format('yyyy-MM-DD')
      this.getDataAnalyseInfoFn()
    },
    handleDateChange() {
      this.getDataAnalyseInfoFn()
    },
    handleDeviceCheck(v, data) {
      this.deviceNum = data.checkedNodes.length
      this.queryInfo.acs = data.checkedNodes.map(item => item.ac)
      if (!this.queryInfo.acs.length) {
        this.paramNum = 0
        this.paramOptions = []
        return
      }
      this.getDataModuleFn()
    },
    handleParamCheck(v, data) {
      this.paramNum = data.checkedNodes.length
      // this.getDataAnalyseInfoFn()
    },
    // 获取模块
    getDataModuleFn() {
      this.treeLoading = true
      this.paramNum = 0
      getDataModule({
        acs: this.queryInfo.acs
      }).then(res => {
        if (res.code !== 200) {
          this.treeLoading = false
          return this.$message({
            type: 'error',
            message: res.msg
          })
        }
        this.controlDc = res.data.MonitorDisplay[0]
        const getData = (type) => {
          let typeName = null
          if (type == 'ElectricMeter') {
            typeName = this.$t('电表')
          } else if (type == 'chargingPile') {
            typeName = this.$t('充电桩')
          } else {
            typeName = type
          }
          let obj = {
            name: typeName,
            id: typeName,
            children: []
          }
          if (Object.keys(res.data).findIndex(item => item == type) !== -1) {
            res.data[type].forEach(item => {
              let name = null
              if (type == 'MAC') {
                name = `${parseInt(item) - 131000 + 1}#Monet-AC`
              } else if (type == 'MDC') {
                name = `${parseInt(item) - 141000 + 1}#Monet-DC`
              } else if (type == 'BMS') {
                name = `${parseInt(item) - 161000 + 1}#BMS`
              } else if (type == 'ElectricMeter') {
                name = Number(item.dcName) !== NaN ? `${this.getEleType(item.dcType)}_${item.dc}` : item.dcName
              } else if (type == 'chargingPile') {
                name = `${parseInt(item) - 191000 + 1}#${this.$t('充电桩')}`
              } else if (type == 'STS') {
                name = `${parseInt(item) - 151000 + 1}#Monet-STS`
              } else if (type == 'BMS-BAU') {
                name = `${parseInt(item) - 241000 + 1}#BMS-BAU`
              }
              obj.children.push({
                name,
                id: name,
                children: type == 'ElectricMeter' ? this[`${type.toLocaleLowerCase()}${item.dcType !== 4 || item.dcType !== 6 ? 1 : item.dcType}Child`].map(child => {
                  let childObj = { ...child }
                  childObj.dc = item.dc
                  childObj.moduleName = item.dcName
                  return childObj
                }) : this[`${type.toLocaleLowerCase()}Child`].map(child => {
                  let childObj = { ...child }
                  childObj.dc = item
                  childObj.moduleName = name
                  return childObj
                })
              })
            })

            return obj
          } else {
            return null
          }
        }
        let type = this.deviceType
        if (type == 1 || type == 6 || type == 3 || type == 4 || type == 10000 || type == 10001 || type == 11 || type == 12) {
          this.paramOptions = [
            this.control1,
            getData('MAC'),
            getData('MDC'),
            getData('BMS'),
            getData('BMS-BAU'),
            getData('chargingPile'),
            getData('STS')
          ]
        } else if (type == 2 || type == 5) {
          this.paramOptions = [
            this.control1,
            getData('MAC'),
            getData('MDC'),
            getData('STS')
          ]
        } else if (type == 7 || type == 8 || type == 9 || type == 10 || type == 10002) {
          this.paramOptions = [
            this.control2,
            getData('MAC'),
            getData('BMS'),
            getData('BMS-BAU'),
            getData('STS')
          ]
        }
        if (res.data.ElectricMeter) {
          this.paramOptions.push(getData('ElectricMeter'))
        }
        this.paramOptions = this.paramOptions.filter(item => item !== null)
        this.treeLoading = false
        // if (this.defaultParamCheckedKeys.length && this.defaultDeviceCheckedKeys.length) {
        //   this.$nextTick(() => {
        //     this.getDataAnalyseInfoFn()
        //   })
        // }
      }).catch(err => {
        this.treeLoading = false
      })
    },
    getPreCheck() {
      if (!this.queryInfo.acs.length) {
        this.loading = false
        return this.$message({
          type: 'error',
          message: this.$t('请选择设备')
        })
      } else {
        this.isEmpty = false
      }
      let checkedParams = this.$refs.treeParamRef.getCheckedNodes()
      if (!checkedParams.length) {
        this.isEmpty = true
        this.loading = false
        return this.$message({
          type: 'error',
          message: this.$t('请选择参数')
        })
      } else {
        this.isEmpty = false
      }
      let MonitorDisplay = {
        [this.controlDc]: checkedParams.filter(item => item.parent == 'MonitorDisplay').map(item => item.id)
      }
      const getData = (type) => {
        let obj = _.groupBy(checkedParams.filter(item => item.parent == type), 'dc')
        for (const key in obj) {
          obj[key] = obj[key].map(item => item.id)
        }

        return obj
      }

      return {
        MonitorDisplay,
        getData
      }
    },
    // 导出
    handleExportClick() {
      const { MonitorDisplay, getData } = this.getPreCheck()
      let checkedParams = this.$refs.treeParamRef.getCheckedNodes()
      let electricMeterList = []
      let obj = _.groupBy(checkedParams.filter(item => item.parent == this.$t('电表')), 'dc')
      for (const key in obj) {
        electricMeterList.push({
          dc: key,
          point: obj[key].map(item => item.point)
        })
      }
      let pointList = checkedParams.map(item => {
        if (item.parent) {
          return {
            point: item.point,
            pointName: item.moduleName ? `${item.moduleName}_${item.name}_${item.unit}` : `${item.name}_${item.unit}`,
            // dc: item.parent == this.$t('电表') ? item.dc : null,
            dc: item.dc,
          }
        }
      }).filter(item => item)
      this.$modal.loading(`${this.$t(`oss['正在下载文件，请稍候']`)}...`);
      exportAnalyse({
        date: this.date,
        ...this.queryInfo,
        map: {
          MonitorDisplay,
          BMS: getData('BMS'),
          ['BMS-BAU']: getData('BMS-BAU'),
          MAC: getData('MAC'),
          MDC: getData('MDC'),
          chargingPile: getData(this.$t('充电桩')),
          STS: getData('STS')
        },
        pointList,
        electricMeterList
      }).then(res => {
        if (res.code) if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let fileName = ''
        fileName = `${this.date}_${this.$t('数据分析报表')}`
        handleExport(res, fileName)
        this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    // 清空
    handleClearClick() {
      this.queryInfo.acs = []
      this.deviceNum = 0
      this.paramNum = 0
      this.paramOptions = []
      this.lineTimes = []
      this.lineValues = []
      this.$refs.treeDeviceRef.setCheckedKeys([])
      this.$refs.treeParamRef.setCheckedKeys([])
      this.$message({
        type: 'success',
        message: this.$t('清空成功')
      })
    },
    handleConfirmClick() {
      this.getDataAnalyseInfoFn()
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-tabs--card>.el-tabs__header .el-tabs__nav {
  border-left: 0;
  border-right: 0;
  display: flex;
  width: 100%;
}

::v-deep .el-tabs__item {
  flex: 1;
  text-align: center;
}

::v-deep .el-tree {
  height: calc(83% - 60px);
  overflow: auto;

  .el-tree-node__content {
    height: 40px;
    font-size: 16px;
    /* border-radius: 8px; */
    /* margin-bottom: 10px; */
  }

  .is-current>.el-tree-node__content {
    background-color: #f6f6f6;
    font-weight: 600;
    color: var(--base-color);
  }

  .el-tree-node__content:hover,
  .el-upload-list__item:hover {
    background-color: #f6f6f6;
  }
}

.home {
  display: flex;
  width: 100%;
  height: 100%;
}

.header-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.home-tree {
  width: 320px;
  background-color: #fff;
  margin-right: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  position: relative;

  .tree-select {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 15px 20px;
  }

  .tree-btn {
    height: 60px;
    border-top: 1px solid #d8dce5;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.home-content::-webkit-scrollbar {
  width: 0;
}

.home-content {
  background: #fff;
  border-radius: 8px;
  flex: 1;
  overflow: auto;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

  .echarts_box {
    height: 92%;
    margin-top: 20px;
  }

  ::v-deep .el-input--suffix .el-input__inner {
    padding-right: 0;
  }
}
</style>
