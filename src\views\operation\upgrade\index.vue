<template>
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-select v-model="queryInfo.upgradeObject" clearable :placeholder="$t(`oss['升级对象']`)"
            @change="handleSearchClick()">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option label="ARM" value="ARM" />
            <el-option label="DSP" value="DSP" />
          </el-select>
        </div>
        <div class="input_ment">
          <el-select v-model="queryInfo.endpoint" clearable :placeholder="$t(`oss['上传地区']`)"
            @change="handleSearchClick()">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option :label="$t('深圳')" :value="1" />
            <el-option :label="$t('新加坡')" :value="2" />
          </el-select>
        </div>
        <div class="input_ment">
          <el-select v-model="queryInfo.fileType" clearable :placeholder="$t(`oss['文件类型']`)"
            @change="handleSearchClick()">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="$t(`oss['文件名称']`)" value="fileName"></el-option>
              <el-option :label="$t('创建人员')" value="userName"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
          }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{
            $t(`oss['上传文件']`) }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handlePullClick()">
            {{ $t('获取文件') }}
            <el-tooltip class="item" effect="dark" placement="bottom" :content="$t('拉取oss服务器上的所有升级文件')">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60">
        </el-table-column>
        <el-table-column prop="fileName" :label="$t(`oss['文件名称']`)" show-overflow-tooltip />
        <el-table-column prop="fileSize" :label="`${$t(`oss['文件大小']`)}（MB）`" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="fileType" :label="$t(`oss['文件类型']`)" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag type="primary">
              {{ getFileTypeText(scope.row.fileType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="upgradeObject" :label="$t(`oss['升级对象']`)" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.upgradeObject || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="filePath" :label="$t(`oss['文件路径']`)" show-overflow-tooltip />
        <el-table-column prop="endpoint" :label="$t(`oss['上传地区']`)" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag type="primary">
              {{ getEndpointText(scope.row.endpoint) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="userName" :label="$t('创建人员')" show-overflow-tooltip />
        <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150">
          <template slot-scope="scope">
            <el-button @click="handleDownClick(scope.row)" type="text" size="small">{{ $t(`oss['下载']`) }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small">{{ $t('common.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>
    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px" :title="dialogTitle">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
        <el-form-item :label="$t(`oss['上传地区']`)" prop="endpoint" v-if="!isPull">
          <el-radio-group v-model="ruleForm.endpoint" style="width: 100%" @input="handleRadioInput"
            :disabled="isShowRadio">
            <el-radio :label="1">{{ $t('深圳') }}</el-radio>
            <el-radio :label="2">{{ $t('新加坡') }}</el-radio>
          </el-radio-group>
          <span v-if="isShowRadio">{{ '请先删除已上传了的文件在重新选择要上传的地区' }}</span>
        </el-form-item>
        <el-form-item :label="$t('文件地区')" prop="bucketName" v-if="isPull">
          <el-select ref="elSelect" v-model="ruleForm.bucketName" :placeholder="$t(`common['select']`)"
            style="width: 100%;">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option :label="$t('深圳')" value="elecod-oss" />
            <el-option :label="$t('新加坡')" value="elecod-oss-global" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t(`oss['文件类型']`)" prop="fileType" :rules="!isPull ? [
          { required: true, message: this.$t(`oss['请选择文件版本类型']`), trigger: 'blur' }
        ] : []">
          <el-select ref="elSelect" v-model="ruleForm.fileType" :placeholder="$t(`common['select']`)"
            style="width: 100%;">
            <el-option :label="$t('common.all')" :value="undefined" v-if="isPull" />
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t(`oss['升级对象']`)" prop="upgradeObject"
          v-if="(ruleForm.fileType == 3 || ruleForm.fileType == 4 || ruleForm.fileType == 5) && !isPull">
          <el-select ref="elSelect" v-model="ruleForm.upgradeObject" :placeholder="$t(`common['select']`)"
            style="width: 100%;">
            <el-option v-for="item in objOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t(`oss['选择文件']`)" prop="filePath" v-if="!isPull && ruleForm.fileType != 6">
          <file-upload :limit="1" :fileSize="20" :fileType="fileType" :uploadFileUrl="uploadFileUrl"
            :data="{ type: ruleForm.fileType, upgradeObject: ruleForm.upgradeObject }" @upload="fileUpload" :type="1"
            @input="fileInput" ref="fileUploadRef"></file-upload>
        </el-form-item>
        <el-form-item :label="$t('选择TAR文件')" prop="filePath" v-if="!isPull && ruleForm.fileType == 6">
          <file-upload :limit="1" :fileSize="20" :fileType="['tar']" :uploadFileUrl="uploadFileUrl"
            :data="{ type: ruleForm.fileType, fileType: 'tar' }" @upload="fileBMSTarUpload" :type="3" @input="fileInput"
            ref="fileUploadTarRef"></file-upload>
        </el-form-item>
        <el-form-item :label="$t('选择INI文件')" prop="bmsFilePathIni" v-if="!isPull && ruleForm.fileType == 6">
          <file-upload :limit="1" :fileSize="20" :fileType="['ini']" :uploadFileUrl="uploadFileUrl"
            :data="{ type: ruleForm.fileType, fileType: 'ini' }" @upload="fileBMSIniUpload" :type="3" @input="fileIniInput"
            ref="fileUploadIniRef"></file-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ossList, addOss, editOss, deleteOss, downloadOss, downloadOSSSingapore, pullOSSFileName } from '@/api/operation/upgrade'
import { handleExport } from '@/utils/export'

export default {
  data() {
    return {
      dialogVisible: false,
      dialogTitle: this.$t(`oss['上传文件']`),
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      ruleForm: {
        fileType: '',
        filePath: '',
        fileName: ''
      },
      rules: {
        filePath: [
          { required: true, message: this.$t(`oss['请选择文件']`), trigger: 'blur' }
        ],
        bmsFilePathini: [
          { required: true, message: this.$t(`oss['请选择文件']`), trigger: 'blur' }
        ],
        endpoint: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        upgradeObject: [
          { required: true, message: this.$t(`common['select']`), trigger: 'blur' }
        ],
      },
      typeOptions: [
        // {
        //   value: 1,
        //   label: 'HMI_Data'
        // },
        {
          value: 2,
          label: this.$t(`oss['HMI升级文件']`)
        },
        {
          value: 3,
          label: this.$t(`oss['MAC升级文件']`)
        },
        {
          value: 4,
          label: this.$t(`oss['MDC升级文件']`)
        },
        {
          value: 5,
          label: this.$t(`oss['STS升级文件']`)
        },
      ],
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/uploadOSS", // 上传文件服务器地址
      // 搜索
      searchKey: 'fileName',
      searchValue: '',
    };
  },
  mounted() {
    this.getList()
    this.origin = window.location.origin
  },
  computed: {
    getFileTypeText() {
      return (type) => {
        if (type == 1) {
          return 'HMI_Data'
        } else if (type == 2) {
          return this.$t(`oss['HMI升级文件']`)
        } else if (type == 3) {
          return this.$t(`oss['MAC升级文件']`)
        } else if (type == 4) {
          return this.$t(`oss['MDC升级文件']`)
        } else if (type == 5) {
          return this.$t(`oss['STS升级文件']`)
        } else if (type == 6) {
          return this.$t('BMS升级文件')
        }
      }
    },
    isShowRadio() {
      return this.ruleForm.filePath ? true : false
    },
    objOptions() {
      let type = this.ruleForm.fileType
      if (type == 3 || type == 4) {
        return [
          {
            value: 'DSP',
            label: 'DSP',
          },
          {
            value: 'ARM',
            label: 'ARM',
          },
        ]
      } else if (type == 5) {
        return [
          {
            value: 'DSP',
            label: 'DSP',
          }
        ]
      } else {
        return []
      }
    },
    fileType() {
      let type = this.ruleForm.fileType
      if (type == 2) {
        return []
      } else if (type == 6) {
        return ['tar']
      } else {
        return ['hex']
      }
    },
    getEndpointText() {
      return (type) => {
        return type == 1 ? this.$t('深圳') : this.$t('新加坡')
      }
    },
    isPull() {
      return this.dialogTitle == this.$t('获取文件')
    }
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      ossList({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        upgradeObject: this.queryInfo.upgradeObject,
        endpoint: this.queryInfo.endpoint,
        fileType: this.queryInfo.fileType,
        [this.searchKey]: this.searchValue
      }).then(res => {
        let data = res.rows
        data.forEach(item => {
          item.fileSize = Number(item.fileSize).toFixed(2)
        })
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == this.$t('获取文件')) {
            this.pullOSSFileNameFn()
          } else {
            this.addOssFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogVisible = true;
      this.dialogTitle = this.$t(`oss['上传文件']`)
      this.ruleForm = {
        fileType: 2,
        filePath: undefined,
        fileSize: '',
        fileName: '',
        endpoint: 1,
        upgradeObject: undefined,
        bucketName: undefined
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
        this.$refs.fileUploadRef?.handleDelete(0)
        this.$refs.fileUploadTarRef?.handleDelete(0)
        this.$refs.fileUploadIniRef?.handleDelete(0)
      })
    },
    addOssFn() {
      addOss({
        fileType: this.ruleForm.fileType,
        filePath: this.ruleForm.filePath,
        fileSize: this.ruleForm.fileSize,
        fileName: this.ruleForm.fileName,
        endpoint: this.ruleForm.endpoint,
        upgradeObject: this.ruleForm.upgradeObject
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`oss['上传失败']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`oss['上传成功']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteOss({
          id: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    fileUpload(data) {
      this.ruleForm.filePath = data.filePath
      this.ruleForm.fileSize = data.fileSize
      this.ruleForm.fileName = data.fileName
    },
    fileInput() {
      this.ruleForm.filePath = undefined
    },
    fileIniInput() {
      this.ruleForm.bmsFilePathIni = undefined
    },
    handleDownClick(row) {
      let api = row.endpoint == 1 ? downloadOss : downloadOSSSingapore
      this.$modal.loading(`${this.$t(`oss['正在下载文件，请稍候']`)}...`);
      api({ filePath: row.filePath }).then(res => {
        if (res.code) if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        handleExport(res, row.fileName, '')
        this.$modal.closeLoading()
        this.$message({
          type: 'success',
          message: this.$t(`oss['下载文件成功']`)
        })
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    handleRadioInput(e) {
      if (e == 1) {
        this.uploadFileUrl = process.env.VUE_APP_BASE_API + "/common/uploadOSS"
      } else if (e == 2) {
        this.uploadFileUrl = process.env.VUE_APP_BASE_API + "/common/uploadOSSSingapore"
      }
    },
    // 拉取文件
    handlePullClick() {
      this.dialogVisible = true;
      this.dialogTitle = this.$t('获取文件')
      this.ruleForm = {
        fileType: undefined,
        bucketName: undefined
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    pullOSSFileNameFn() {
      pullOSSFileName({
        bucketName: this.ruleForm.bucketName,
        fileType: this.ruleForm.fileType
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t('获取失败')
        });
        this.getList()
        this.dialogVisible = false
      })
    },
    fileBMSTarUpload(data) {
      this.ruleForm.filePath = data.filePath
      this.ruleForm.fileSize = data.fileSize / 1024 / 1024
      this.ruleForm.fileName = data.fileName
    },
    fileBMSIniUpload(data) {
      this.ruleForm.bmsFilePathIni = data.filePath
      this.ruleForm.bmsFileNameIni = data.fileName
    }
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;
}
</style>
