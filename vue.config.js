'use strict'
const path = require('path')

const webpack = require('webpack')

// 基于时间的分析工具 - SMP  统计项目构建过程中在编译阶段的耗时情况
const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");
const smp = new SpeedMeasurePlugin();

// 基于产物内容的分析工具 - WBA  找出对产物包体积影响最大的包的构成，从而找到那些冗余的、可以被优化的依赖项。不仅能减小最后的包体积大小，也能提升构建模块时的效率
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

function resolve(dir) {
  return path.join(__dirname, dir)
}

const CompressionPlugin = require('compression-webpack-plugin')

// const name = process.env.VUE_APP_TITLE || 'elecloud云平台' // 网页标题
const name = '' // 网页标题

const webpackConfig = smp.wrap({
  name: name,
  resolve: {
    alias: {
      '@': resolve('src'),
      'assets': resolve('src/assets')
    }
  },
  plugins: [
    new CompressionPlugin({
      cache: false,                   // 不启用文件缓存
      test: /\.(js|css|html)?$/i,     // 压缩文件格式
      filename: '[path].gz[query]',   // 压缩后的文件名
      algorithm: 'gzip',              // 使用gzip压缩
      minRatio: 0.8                   // 压缩率小于1才会压缩
    }),
    // new BundleAnalyzerPlugin()
  ],
  devtool: 'source-map'
})

const port = process.env.port || process.env.npm_config_port || 80 // 端口

// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === 'development',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    // https: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        // target: `http://**************:8080/`,//国内线上
        target: `http://***********:8080/`,//国外线上
        // target: `http://*************:8080/`,//本地测试
        // target: `http://************:8080/`,//本地测试
        // target: `http://************:8080/`,//测试线上
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },
    disableHostCheck: true
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: "expanded" }
      }
    }
  },
  configureWebpack: webpackConfig,
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config.plugin('ignore')
      //忽略/moment/locale下的所有文件
      .use(new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/))

    config.when(process.env.NODE_ENV !== 'development', config => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [{
          // `runtime` must same as runtimeChunk name. default is `runtime`
          inline: /runtime\..*\.js$/
        }])
        .end()

      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial' // only package third parties that are initially dependent
          },
          elementUI: {
            name: 'chunk-elementUI', // split elementUI into a single package
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
            priority: 20 // the weight needs to be larger than libs and app or it will be packaged into libs or app
          },
          echarts: {
            name: 'chunk-echarts', // split elementUI into a single package
            test: /[\\/]node_modules[\\/]_?echarts(.*)/, // in order to adapt to cnpm
            priority: 30 // the weight needs to be larger than libs and app or it will be packaged into libs or app
          },
          quill: {
            name: 'chunk-quill', // split elementUI into a single package
            test: /[\\/]node_modules[\\/]_?quill(.*)/, // in order to adapt to cnpm
            priority: 30 // the weight needs to be larger than libs and app or it will be packaged into libs or app
          },
          api: {
            name: 'chunk-api',
            test: /[\\/]api[\\/]/,
            priority: 0,
          },
          commons: {
            name: 'chunk-commons',
            test: /src\/components/, // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true
          }
        }
      })

      config.optimization.runtimeChunk('single'),
      {
        from: path.resolve(__dirname, './public/robots.txt'), //防爬虫文件
        to: './' //到根目录下
      }
    })
  }
}
