<template>
  <div class="position_box">
    <div style="position: relative;left: 23px;">
      <!-- 电网 -->
      <div class="ac-flow">
        <div class="dianzhan">
          <svg-icon icon-class="flow_ac" class-name="icon" class="svg-img"
            :style="{ color: $store.state.common.flowImg.flow_ac }" />
          <div class="flow-detail" style="margin-right: 10px;">
            <div>
              {{ $t('monitor.flowItem1') }}：<span>{{ flowData.power ? flowData.power : 0 }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="100px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-left: 4px;">
          <!-- 右流 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 70 10 L 280 10 Q 290 10 290 20 L 290 80" v-if="flowData.power < -1" />
          <!-- 左流 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 290 80 L 290 20 Q 290 10 280 10 L 70 10" v-else-if="flowData.power > 1" />
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 70 10 L 280 10 Q 290 10 290 20 L 290 80" v-else />

          <defs>
            <linearGradient id="grad1" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#F76655" />
              <stop offset="100%" stop-color="#F7A11A" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('power')">
            <animate attributeName="fill" :values="colorCircle('power')" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('power')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
        </svg>
      </div>
      <!-- 负载 -->
      <div class="load-flow">
        <div class="load">
          <svg-icon icon-class="flow_load" class-name="icon" class="svg-img"
            :style="{ color: $store.state.common.flowImg.flow_load }" />
          <div class="flow-detail" style="margin: 0 10px 0 0">
            <div>
              {{ $t('monitor.flowItem3') }}：<span>{{ flowData.load }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="100px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-left: 4px;">
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M 290 20 L 290 80 Q 290 90 280 90 L 70 90" v-if="flowData.load > 1" />
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M 70 90 L 280 90 Q 290 90 290 80 L 290 20" v-else-if="flowData.load < -1" />
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M 70 90 L 280 90 Q 290 90 290 80 L 290 20" v-else />

          <defs>
            <linearGradient id="grad2" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#14B9AE" />
              <stop offset="100%" stop-color="#F4A21B" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('load')">
            <animate attributeName="fill" :values="colorCircle('load')" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('load')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
    <div class="dianchi">
      <svg-icon icon-class="flow_de" class-name="icon" class="svg-img"
        :style="{ color: $store.state.common.flowImg.flow_de }" />
    </div>
    <div style="position: relative;right: 25px;">
      <!-- 电池 -->
      <div class="cell-flow">
        <div class="transformer">
          <Bms :value="soc"></Bms>
          <div class="flow-detail" style="margin-left: 10px;">
            <div>
              {{ $t('monitor.flowItem4') }}：<span>{{ flowData.cell }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="100px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m200,55 l-200,0  0,1"
            v-if="flowData.cell > 1" />
          <!-- 反  -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m0,55 l200,1  0,1"
            v-else-if="flowData.cell < -1" />
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m0,55 l200,1  0,1" v-else />

          <defs>
            <linearGradient id="grad4" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#436EE3" />
              <stop offset="100%" stop-color="#F1A121" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('cell')">
            <animate attributeName="fill" :values="colorCircle('cell')" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('cell')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
        </svg>
      </div>
      <!-- 光伏 -->
      <div class="photovoltaic-flow">
        <div class="transformer1">
          <svg-icon icon-class="flow_pv" class-name="icon" class="svg-img"
            :style="{ color: $store.state.common.flowImg.flow_pv }" />
          <div class="flow-detail" style="margin: 0 0 0 10px;">
            <div>
              {{ $t('monitor.flowItem2') }}：<span>{{ flowData.photovoltaic ? flowData.photovoltaic : 0 }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="100px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-left: 4px;">
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad3')"
            d="M 230 10 L 20 10 Q 10 10 10 20 L 10 80" v-if="flowData.photovoltaic > 1" />
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad3')"
            d="M 10 80 L 10 20 Q 10 10 20 10 L 230 10" v-else-if="flowData.photovoltaic < -1" />
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad3')"
            d="M 230 10 L 20 10 Q 10 10 10 20 L 10 80" v-else />

          <defs>
            <linearGradient id="grad3" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#9a60b4" />
              <stop offset="100%" stop-color="#ea7ccc" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('photovoltaic')">
            <animate attributeName="fill" :values="colorCircle('photovoltaic')" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path3" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('photovoltaic')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path3" />
            </animateMotion>
          </circle>
        </svg>
      </div>
      <div class="cp-flow">
        <div class="cp">
          <svg-icon icon-class="flow_pe" class-name="icon" class="svg-img"
            :style="{ color: $store.state.common.flowImg.flow_pe }" />
          <div class="flow-detail" style="line-height: 22px;padding-top: 7px;padding-bottom: 4px;margin-left: 10px;"
            v-if="flowData?.chargingPiles && flowData?.chargingPiles.length">
            <template v-if="flowData?.chargingPiles.length > 1">
              <div class="swiper-container" ref="swiperRef">
                <div class="swiper-wrapper">
                  <div class="swiper-slide" v-for="item in flowData?.chargingPiles" :key="item.dc">
                    <div>
                      {{ item.name }}
                      <div>
                        {{ `1#${$t('枪功率')}` }}：<span>{{ item.chargingPile_19006 }}</span>&nbsp;kW
                      </div>
                      <div>
                        {{ `2#${$t('枪功率')}` }}：<span>{{ item.chargingPile_19017 }}</span>&nbsp;kW
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else-if="flowData?.chargingPiles.length == 1">
              <div>
                {{ flowData?.chargingPiles[0].name }}
                <div>
                  {{ `1#${$t('枪功率')}` }}：<span>{{ flowData?.chargingPiles[0].chargingPile_19006 }}</span>&nbsp;kW
                </div>
                <div>
                  {{ `2#${$t('枪功率')}` }}：<span>{{ flowData?.chargingPiles[0].chargingPile_19017 }}</span>&nbsp;kW
                </div>
              </div>
            </template>
            <template v-else>
              <div>
                {{ $t('充电桩功率') }}：--&nbsp;kW
              </div>
            </template>
          </div>
          <div class="flow-detail" style="margin: 0 0 0 10px;" v-else>
            <div>
              {{ $t('充电桩功率') }}：--&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="100px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-left: 4px;">
          <path id="path5" stroke-width="2" fill="transparent" stroke="url('#grad5')"
            d="M 230 90 L 20 90 Q 10 90 10 80 L 10 20" v-if="flowData.pile < 0" />
          <path id="path5" stroke-width="2" fill="transparent" stroke="url('#grad5')"
            d="M 10 20 L 10 80 Q 10 90 20 90 L 230 90" v-else-if="flowData.pile > 0" />
          <path id="path5" stroke-width="2" fill="transparent" stroke="url('#grad5')"
            d="M 10 20 L 10 80 Q 10 90 20 90 L 230 90" v-else />

          <defs>
            <linearGradient id="grad5" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#d87a80" />
              <stop offset="100%" stop-color="#ffb980" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('pile')">
            <animate attributeName="fill" :values="colorCircle('pile')" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path5" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('pile')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path5" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>

  </div>
</template>

<script>
import Bms from './bms.vue'
// import Swiper JS
import Swiper from 'swiper';
// import Swiper styles
import 'swiper/swiper-bundle.css';
import SwiperCore, { Autoplay } from 'swiper/core';
// configure Swiper to use modules
SwiperCore.use([Autoplay]);

export default {
  components: { Bms },
  data() {
    return {
      swiper: null
    }
  },
  computed: {
    flowData() {
      let data = this.$store.state.monitor.flowData
      this.$nextTick(() => {
        this.initSwiper()
      })
      if (data.chargingPiles && data.chargingPiles.length) {
        data.pile = data.chargingPiles.reduce((pre, current) => pre + Number(current.chargingPile_19003), 0)
      } else {
        data.pile = null
      }
      return data
    },
    // 流动拓补图展示圆
    showCircle() {
      let control = this.$store.state.monitor.control
      let flowData = this.flowData
      return (name) => {
        if (!control) return false
        if (control.onLineState == '离线') return false
        if (-1 <= flowData[name] && flowData[name] <= 1) {
          return false
        } else if (flowData[name] == 0) {
          return false
        } else {
          return true
        }
      }
    },
    // 流动的颜色
    colorCircle() {
      return (type) => {
        if (type == 'power') {
          return this.flowData.power < -1 ? '#F7A11A;#F76655' : '#F76655;#F7A11A'
        } else if (type == 'load') {
          return this.flowData.load < -1 ? '#F4A21B;#14B9AE' : '#14B9AE;#F4A21B'
        } else if (type == 'cell') {
          return this.flowData.cell < -1 ? '#F1A121;#436EE3' : '#436EE3;#F1A121'
        } else if (type == 'photovoltaic') {
          return this.flowData.photovoltaic < -1 ? '#ea7ccc;#9a60b4' : '#ea7ccc;#9a60b4'
        } else if (type == 'pile') {
          return this.flowData.pile < 0 ? '#d87a80;#ffb980' : '#ffb980;#d87a80'
        }
      }
    },
    soc() {
      let bms = this.$store.state.monitor.pcs_bms
      let bmsBau = this.$store.state.monitor.pcs_bmsBau
      if (bms.length) return (bms.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bms.length).toFixed(1)
      else if (bmsBau.length) return (bmsBau.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bmsBau.length).toFixed(1)
      return 0
    }
  },
  methods: {
    initSwiper() {
      this.swiper = new Swiper('.swiper-container', {
        // Optional parameters
        // direction: 'horizontal',
        direction: 'vertical',
        loop: true,
        slidesPerView: 1,
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        },

        // Navigation arrows
        // navigation: {
        //   nextEl: '.swiper-button-next',
        //   prevEl: '.swiper-button-prev',
        // },
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.position_box {
  position: relative;
  height: 240px;
  width: 670px;
  margin: auto;
  display: flex;
  align-items: center;
  transform: scale(0.85);

  .img {
    width: 70px;
  }

  .dianchi {
    z-index: 1000;
  }

  .cell-flow {
    position: absolute;
    top: 50px;
    left: 23px;

    .dianzhan {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      right: 180px;
      top: 0px;
      padding: 8px;
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      width: 100%;
    }

    .flow-detail {
      padding: 0 10px;
      border: 1px solid #d6d6d6;
      border-radius: 10px;
      /* height: 35px; */
      display: flex;
      font-size: 14px;
      line-height: 35px;

      span {
        font-weight: 600;
      }
    }

    .transformer {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      left: 200px;
      top: 20px;
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
    }
  }

  .ac-flow {
    position: relative;

    .dianzhan {
      position: absolute;
      right: 216px;
      top: -30px;
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      width: 100%;
    }
  }

  .flow-detail {
    padding: 0 10px;
    border: 1px solid #d6d6d6;
    border-radius: 10px;
    /* height: 35px; */
    display: flex;
    font-size: 14px;
    line-height: 35px;

    span {
      font-weight: 600;
    }
  }

  .photovoltaic-flow {
    position: relative;

    .transformer1 {
      position: absolute;
      top: -30px;
      left: 220px;
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
    }
  }

  .load-flow {
    position: relative;
    margin-top: 2px;

    .load {
      position: absolute;
      top: 55px;
      right: 210px;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      width: 100%;
    }
  }

  .cp-flow {
    position: relative;
    margin-top: 2px;

    .cp {
      position: absolute;
      top: 55px;
      left: 225px;
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
    }
  }
}

.swiper-container {
  width: 100%;
  height: 70px;

  .swiper-slide {
    display: flex;
    align-items: center;
  }

  .swiper-button-prev {
    left: 0px !important;
    top: 60%;
  }

  .swiper-button-prev::after {
    font-size: 20px;
  }

  .swiper-button-next {
    right: 0px !important;
    top: 60%;
  }

  .swiper-button-next::after {
    font-size: 20px;
  }
}

.svg-img {
  width: 70px;
  height: 70px;
}
</style>
