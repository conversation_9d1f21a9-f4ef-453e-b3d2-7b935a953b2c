<template>
  <div class="box">
    <div class="input_box">
      <!-- <div class="header-title">
        {{ '测点绑定' }}
      </div> -->
      <div>
        <div class="input_ment">
          <el-select v-model="queryInfo.type" clearable :placeholder="$t('common.select')"
            @change="handleSearchClick()">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option :label="$t('外设')" :value="0" />
            <el-option :label="$t('电表')" :value="1" />
          </el-select>
        </div>
        <div class="input_ment">
          <el-select v-model="queryInfo.enable" clearable :placeholder="$t('common.select')"
            @change="handleSearchClick()">
            <el-option :label="$t('common.all')" :value="undefined" />
            <el-option :label="$t('失效')" :value="0" />
            <el-option :label="$t('生效')" :value="1" />
          </el-select>
        </div>
        <div class="input_ment">
          <el-input :placeholder="$t(`device['Please enter device serial number']`)" style="width: 200px;"
            v-model="queryInfo.ac" clearable></el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
          }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{ $t('common.add') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="ac" :label="`ac`" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.ac }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.ac"
              v-clipboard:success="copySuccess"></i>
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="$t('测点类型')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag type="primary">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="point" :label="$t('测点编号')" show-overflow-tooltip align="center" />
        <el-table-column prop="alias" :label="$t('测点别名')" show-overflow-tooltip align="center" />
        <el-table-column prop="enable" :label="$t(`是否生效`)" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag :type="getEnableText(scope.row.enable).type">
              {{ getEnableText(scope.row.enable).text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleEditClick(scope.row)">{{
              $t('common.edit') }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small">{{ $t('common.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>

    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" :width="$convertPx(600, 'rem')"
      :title="dialogTitle">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
        <el-form-item :label="$t('project.sn')" prop="ac">
          <el-input v-model="form.ac" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('测点类型')" prop="type">
          <el-radio-group v-model="form.type" style="width: 100%" @input="handleTypeChange">
            <el-radio :label="0">{{ $t('外设') }}</el-radio>
            <el-radio :label="1">{{ $t('电表') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('外设类型')" prop="mode" v-if="form.type == 0">
          <el-radio-group v-model="form.mode" style="width: 100%" @input="handleModeChange">
            <el-radio :label="0">{{ $t('默认') }}</el-radio>
            <el-radio :label="1">{{ 'STS' }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('测点别名')" prop="pointId">
          <el-select v-model="form.pointId" clearable :placeholder="$t('common.select')" style="width: 100%">
            <el-option v-for="item in pointOptions" :label="item.alias" :value="item.id" :key="item.id">
              <span style="float: left">{{ item.alias }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ `${item.point}` }}<span v-if="form.type == 1">{{ '_' + (item.electricQuantityPoint == 0 ? $t('正向') : $t('反向')) }}</span></span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('是否生效')" prop="enable">
          <el-radio-group v-model="form.enable" style="width: 100%">
            <el-radio :label="1">{{ $t('生效') }}</el-radio>
            <el-radio :label="0">{{ $t('失效') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('formRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, computed } from 'vue'
import { bindAliasList, getPointAll, addBindAlias, editBindAlias, deleteBindAlias } from '@/api/operation/deviceBindAlias'

const { proxy } = getCurrentInstance()

const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const queryInfo = ref({
  pageNum: 1,
  pageSize: 10
})

// 获取列表
const getList = () => {
  loading.value = true
  bindAliasList(queryInfo.value).then(res => {
    let data = res.rows
    data.forEach(item => {
      if (item.type == 0) {
        item.pointTitle = proxy.$t('测点编号')
        item.aliasTitle = proxy.$t('测点别名')
      } else if (item.type == 1) {
        item.pointTitle = proxy.$t('测点值')
        item.aliasTitle = proxy.$t('测点值别名')
      }
    })
    tableData.value = data
    total.value = res.total
    loading.value = false
  });
}
getList()
const getTypeText = computed(() => {
  return (type) => {
    if (type == 0) {
      return proxy.$t('外设')
    } else if (type == 1) {
      return proxy.$t('电表')
    }
  }
})
const getEnableText = computed(() => {
  return (type) => {
    if (type == 0) {
      return {
        text: proxy.$t('失效'),
        type: 'danger'
      }
    } else if (type == 1) {
      return {
        text: proxy.$t('生效'),
        type: 'primary'
      }
    }
  }
})
//搜索按键
const handleSearchClick = () => {
  getList()
}

// 表单
const dialogVisible = ref(false)
const dialogTitle = ref(proxy.$t('新建设备绑定测点'))
const form = ref({
  enable: 1,
  pointId: undefined,
  ac: undefined,
  type: 0,
  mode: 0
})
const rules = ref({
  enable: [
    { required: true, message: proxy.$t(`common.select`), trigger: 'blur' }
  ],
  pointId: [
    { required: true, message: proxy.$t(`common.select`), trigger: 'blur' }
  ],
  ac: [
    { required: true, message: proxy.$t(`common['Please enter']`), trigger: 'blur' }
  ],
  type: [
    { required: true, message: proxy.$t('请选择测点类型'), trigger: 'blur' }
  ],
  mode: [
    { required: true, message: proxy.$t('请选择外设类型'), trigger: 'blur' }
  ]
})
const pointOptions = ref([])
const handleTypeChange = (e) => {
  form.value.mode = 0
  if (e == 0) {
    getPointAllFn(0, form.value.mode)
  } else if (e == 1) {
    getPointAllFn(1, form.value.mode)
  }
}
const handleModeChange = (e) => {
  if (e == 0) {
    getPointAllFn(form.value.type, 0)
  } else if (e == 1) {
    getPointAllFn(form.value.type, 1)
  }
}
const getPointAllFn = (type, mode) => {
  getPointAll({ type, mode }).then(res => {
    pointOptions.value = res.data
  })
}
const handleConfirm = (formName) => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      if (dialogTitle.value == proxy.$t('新建设备绑定测点')) { // 添加
        addBindAliasFn()
      } else { // 修改
        editBindAliasFn()
      }
    }
  });
}
// 添加
const handleAddClick = () => {
  dialogTitle.value = proxy.$t('新建设备绑定测点')
  form.value = {
    enable: 1,
    pointId: undefined,
    ac: undefined,
    type: 0,
    mode: 0
  }
  getPointAllFn(form.value.type, form.value.mode)
  nextTick(() => {
    proxy.resetForm('form')
  })
  dialogVisible.value = true;
}
// 修改
const handleEditClick = (row) => {
  dialogTitle.value = proxy.$t('设备修改绑定测点')
  form.value = {
    ...row
  }
  getPointAllFn(form.value.type, form.value.mode)
  dialogVisible.value = true;
}
const addBindAliasFn = () => {
  addBindAlias({
    enable: form.value.enable,
    pointId: form.value.pointId,
    ac: form.value.ac,
    type: form.value.type,
    mode: form.value.mode
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Addition Failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Added successfully']`)
    })
    getList()
    dialogVisible.value = false
  })
}
const editBindAliasFn = () => {
  editBindAlias({
    id: form.value.id,
    enable: form.value.enable,
    pointId: form.value.pointId,
    ac: form.value.ac,
    type: form.value.type,
    mode: form.value.mode
  }).then(res => {
    if (res.code !== 200) return proxy.$message({
      type: 'error',
      message: proxy.$t(`common['Change failed']`)
    });
    proxy.$message({
      type: 'success',
      message: proxy.$t(`common['Modify successfully']`)
    })
    getList()
    dialogVisible.value = false
  })
}
// 删除
const handleDeleteClick = (row) => {
  proxy.$confirm(proxy.$t(`menu['Are you sure to delete the data item?']`), proxy.$t('common.systemPrompt'), {
    confirmButtonText: proxy.$t('common.confirm'),
    cancelButtonText: proxy.$t('common.cancel'),
    type: 'warning'
  }).then(() => {
    deleteBindAlias({
      devicePointIds: row.id
    }).then(res => {
      if (res.code !== 200) return proxy.$message({
        type: 'error',
        message: proxy.$t(`common['Deleted Failed']`)
      });
      getList()
      proxy.$message({
        type: 'success',
        message: proxy.$t(`common['Deleted successfully']`)
      });
    })
  }).catch(() => {
    proxy.$message({
      type: 'info',
      message: proxy.$t(`common['Deletion Cancelled']`)
    });
  });
}

// 复制成功
const copySuccess = () => {
  proxy.$modal.msgSuccess(proxy.$t('复制成功'))
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;

  .table_box {
    margin-top: 30px;
    width: 100%;
    background-color: white;
    padding-bottom: 10px;
    border-radius: 12px;
  }
}
</style>
