<template>
  <!-- 设备列表 -->
  <div class="page-two-box">
    <div style="position: absolute;left: 0;top: 45%;" @click="handleTreeExpand">
      <svg-icon icon-class="tree-expand" class-name="icon" style="height: 50px;"
        :style="{ transform: treeExpand ? 'rotate(0deg)' : 'rotate(180deg)' }" />
    </div>
    <DeptTree @nodeClick="nodeClick" v-show="treeExpand" />
  <div class="page-two-box-content">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-select v-model="queryInfo.deviceType" :placeholder="$t('common.select')" @change="handleSearchClick">
            <el-option :label="$t('common.all')" :value="undefined">
            </el-option>
            <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="'SN'" value="deviceSerialNumber"></el-option>
              <el-option :label="$t('device.name')" value="deviceName"></el-option>
              <el-option :label="$t('创建人员')" value="nickName"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleSearchClick()" icon="el-icon-search">{{ $t('common.search')
          }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick('ruleForm')" icon="el-icon-plus"
            v-hasPermi="['system:device:add']">{{ $t('device.AddDevice')
            }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table v-loading="loading" :data="tableData" style="width: 100%;">
        <el-table-column fixed="left" type="index" label="#" width="60" align="center" />
        <el-table-column prop="deviceName" :label="$t('device.name')" show-overflow-tooltip align="center" />
        <el-table-column prop="deviceSerialNumber" :label="$t('device.screenId')" show-overflow-tooltip width="200" align="center">
          <template slot-scope="scope">
            {{ scope.row.deviceSerialNumber }}<i class="el-icon-copy-document copy"
              v-clipboard:copy="scope.row.deviceSerialNumber" v-clipboard:success="copySuccess"></i>
          </template>
        </el-table-column>
        <el-table-column prop="deviceModel" :label="$t('device.model')" show-overflow-tooltip align="center" />
        <el-table-column prop="deviceType" :label="$t('device.type')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <span class="primary pointer">{{ getDeviceTypeFn(scope.row.deviceType, true, true) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deviceRatedPower" :label="`${$t('monitor.topItem2')}（kW）`" show-overflow-tooltip align="center" />
        <el-table-column prop="photovoltaicInstalledCapacity" :label="`${$t('monitor.topItem6')}（kWp）`"
          show-overflow-tooltip align="center" />
        <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip align="center" />
        <el-table-column prop="nickName" :label="$t('创建人员')" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" align="center">
          <template slot-scope="scope">
            <el-button @click="handleEditClick(scope.row)" type="text" size="small" v-hasPermi="['system:device:edit']"
              :disabled="isShowEmsFn(scope.row.deviceType)" style="padding: 0;">{{ $t('common.edit') }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small"
              v-hasPermi="['system:device:remove']" :disabled="isShowEmsFn(scope.row.deviceType)" style="padding: 0;">{{ $t('common.delete')
              }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>
    <!-- 弹窗 -->
    <el-dialog :visible.sync="dialogVisible" :title="dialogName" center :modal-append-to-body="false">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="`${$t('device.name')}`" prop="deviceName">
              <el-input v-model="ruleForm.deviceName" :placeholder="$t(`device['Please enter device name']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('device.screenId')}`" prop="deviceSerialNumber">
              <el-input v-model="ruleForm.deviceSerialNumber"
                :placeholder="$t(`device['Please enter machine serial number (screen)']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('device.model')}`" prop="deviceModel">
              <el-input v-model="ruleForm.deviceModel" :placeholder="$t(`device['Please enter device model']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('device.type')}`" prop="deviceType">
              <el-select ref="elSelect" v-model="ruleForm.deviceType" placeholder="请选择" style="width: 100%;"
                @change="deviceTypeChange">
                <!-- :popper-append-to-body="false" @visible-change="focusFixDateSelectPosition" @change="onChange"> -->
                <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12"
            v-if="ruleForm.deviceType == 1 || ruleForm.deviceType == 2 || ruleForm.deviceType == 4 || ruleForm.deviceType == 5 || ruleForm.deviceType == 6 || ruleForm.deviceType == 11 || ruleForm.deviceType == 12 || isGroup">
            <el-form-item :label="`${$t('monitor.topItem6')}`" prop="photovoltaicInstalledCapacity">
              <el-input-number v-model="ruleForm.photovoltaicInstalledCapacity" :precision="0"
                :placeholder="$t(`device['Please enter photovoltaic installed capacity']`)"></el-input-number>
              <span class="suffix">kWp</span>
            </el-form-item>
          </el-col>
          <el-col :span="12"
            v-if="ruleForm.deviceType == 2 || ruleForm.deviceType == 5 || ruleForm.deviceType == 8 || ruleForm.deviceType == 10 || ruleForm.deviceType == 11 || ruleForm.deviceType == 12 || isGroup">
            <el-form-item :label="`${$t('device.cellCapacity')}`" prop="deviceBatteryCapacity">
              <el-input-number v-model="ruleForm.deviceBatteryCapacity" :precision="0"
                :placeholder="$t(`device['Please enter battery capacity']`)"></el-input-number>
              <span class="suffix">kWh</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`${$t('monitor.topItem2')}`" prop="deviceRatedPower">
              <el-input-number v-model="ruleForm.deviceRatedPower" :precision="0"
                :placeholder="$t(`device['Please enter rated power']`)"></el-input-number>
              <span class="suffix">kW</span>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="ruleForm.deviceType == 9">
            <el-form-item :label="`${$t('是否显示柴油机')}`" prop="showDiesel">
              <el-radio-group v-model="ruleForm.showDiesel" style="width: 100%">
                <el-radio :label="1">{{ $t('menu.yes') }}</el-radio>
                <el-radio :label="0">{{ $t('menu.no') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isGroup">
            <el-form-item :label="$t(`monitor['组合类型']`)" prop="combinationDeviceType">
              <el-tag :key="tagIndex" v-for="(tag, tagIndex) in ruleForm.combinationDeviceType" closable
                :disable-transitions="false" @close="handleTypeClose(tag)">
                {{ getTypeLabel(tag) }}
              </el-tag>
              <el-select ref="elTypeSelect" v-model="ruleForm.inputTypeValue" v-if="ruleForm.inputTypeVisible"
                placeholder="请选择" @change="handleInputTypeConfirm">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <el-button v-else class="button-new-tag" size="small" @click="showTypeInput">+ </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isGroup">
            <el-form-item :label="$t(`monitor['组合序列号']`)" prop="combinationDeviceSerialNumber">
              <el-tag :key="tag" v-for="tag in ruleForm.combinationDeviceSerialNumber" closable
                :disable-transitions="false" @close="handleClose(tag)">
                {{ tag }}
              </el-tag>
              <el-input class="input-new-tag" v-if="ruleForm.inputVisible" v-model="ruleForm.inputValue"
                ref="saveTagInput" size="small" @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
              </el-input>
              <el-button v-else class="button-new-tag" size="small" @click="showInput">+ </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" v-if="dialogName == $t('device.AddDevice') || dialogQueryVisible"
          @click="handleConfirm('ruleFormRef')">{{ dialogQueryVisible ? $t(`common['Confirm addition']`) :
            $t('common.confirm') }}</el-button>
        <el-button type="primary" v-if="dialogName == $t('device.ModifyDevice')"
          @click="handleEditConfirm('ruleFormRef')">{{ $t('common.save') }}</el-button>
      </span>
    </el-dialog>
    <!-- 弹窗 -->
    <el-dialog :visible.sync="dialogQueryVisible" :title="$t('device.QueryingDevice')" center
      :modal-append-to-body="false">
      <el-input v-model="searchAc" :placeholder="$t(`device['Please enter machine serial number (screen)']`)" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogQueryVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleQueryConfirm()">{{ $t('common.search') }}</el-button>
      </span>
    </el-dialog>
  </div>
  </div>
</template>

<script>
import { deviceTypeSingleOptions, deviceTypeGroupOptions, getDeviceType, isGroupFn, isEmsFn } from '@/hook/useDeviceType'
import {
  deviceList,
  addDevice,
  editDevice,
  getDeviceInfo,
  deleteDevice,
  getAdminDeviceByAcInfo
} from '@/api/property/sn'

import DeptTree from '@/components/DeptTree'

export default {
  components: { DeptTree }, 
  data() {
    return {
      options: deviceTypeSingleOptions,
      groupOptions: deviceTypeGroupOptions,
      dialogName: '',
      dialogVisible: false,
      total: 10,
      tableData: [],
      ruleForm: {
        deviceSerialNumber: '',//整机序列号
        deviceModel: '',//设备型号
        deviceFactoryVersion: '',//出厂软件版本
        deviceUpdateFactoryVersion: '',//出厂硬件版本
        deviceBatteryCapacity: '',//电池容量
        customerOrderNumber: '',//客户单号
        orderNumber: '',//订单号
        orderExplain: '',//订单特殊说明
        deviceType: 1,
        serialNumber: [],//模块序列号
        inputVisible: false,
        inputValue: '',
        deviceName: '', // 设备名称
        deviceRatedPower: '', // 额定功率
        photovoltaicInstalledCapacity: '', // 光伏装机容量
        combinationDeviceSerialNumber: [],
        combinationDeviceType: [],
        inputTypeVisible: false,
        inputTypeValue: '',
        showDiesel: 0
      },
      rules: {
        deviceName: [
          { required: true, message: this.$t(`device['Please enter device name']`), trigger: 'blur' }
        ],
        deviceSerialNumber: [
          { required: true, message: this.$t(`device['Please enter machine serial number (screen)']`), trigger: 'blur' }
        ],
        deviceModel: [
          { required: true, message: this.$t(`device['Please enter device model']`), trigger: 'blur' }
        ],
        serialNumber: [
          { required: true, message: this.$t(`device['Please enter module serial number']`), trigger: 'blur' }
        ],
        deviceFactoryVersion: [
          { required: true, message: this.$t(`device['Please enter factory software version']`), trigger: 'blur' }
        ],
        deviceUpdateFactoryVersion: [
          { required: true, message: this.$t(`device['Please enter factory hardware version']`), trigger: 'blur' }
        ],
        deviceBatteryCapacity: [
          { required: true, message: this.$t(`device['Please enter battery capacity']`), trigger: 'blur' }
        ],
        customerOrderNumber: [
          { required: true, message: this.$t(`device['Please enter customer order number']`), trigger: 'blur' }
        ],
        orderNumber: [
          { required: true, message: this.$t(`device['Please enter order number']`), trigger: 'blur' }
        ],
        deviceRatedPower: [
          { required: true, message: this.$t(`device['Please enter rated power']`), trigger: 'blur' }
        ],
        photovoltaicInstalledCapacity: [
          { required: true, message: this.$t(`device['Please enter photovoltaic installed capacity']`), trigger: 'blur' }
        ],
        deviceType: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        combinationDeviceType: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        combinationDeviceSerialNumber: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        showDiesel: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
      },
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        vncDevice: 0
      },
      loading: false,
      /**
       * 查询设备
       */
      dialogQueryVisible: false,
      searchAc: '',
      /**
       * 组合类型
       */
      isGroup: false,
      // 搜索
      searchKey: 'deviceSerialNumber',
      searchValue: '',
      treeExpand: false
    };
  },
  mounted() {
    const user = JSON.parse(localStorage.getItem('users'));
    this.user_id = user.userId
    this.getList()
    if (process.env.VUE_APP_SCREEN != 'zh') this.rules.deviceName.push({
      pattern: /^[^\u4e00-\u9fa5]+$/,
      message: this.$t('不允许有中文字符'),
      trigger: 'blur'
    })
  },
  computed: {
    adminRole() {
      let roles = this.$store.state.user.roles
      return roles.findIndex(item => item == 'admin') !== -1
    },
    deviceTypeOptions() {
      // if (this.adminRole) {
      return [...deviceTypeSingleOptions, ...deviceTypeGroupOptions]
      // } else {
      //   return deviceTypeSingleOptions
      // }
    },
    getTypeLabel() {
      return (type) => {
        return getDeviceType(type, false)
      }
    }
  },
  methods: {
    /**
      * 解决Select定位问题
      */
    focusFixDateSelectPosition() {
      let e = this.$refs['elSelect'].$refs['popper'];
      console.log(e, this.$refs['elSelect']);
      this.selectCallback(e);
    },
    // 使选择框失去焦点
    onChange() {
      this.$nextTick(() => {
        this.$refs['elSelect'].blur();
      })
    },
    /**
     * 回调函数
     */
    selectCallback(e) {
      setTimeout(() => {
        if ('undefined' === typeof e['popperJS'] || null == e['popperJS']) {
          this.selectCallback(e);
        } else {
          e.popperJS.state.position = 'absolute';
          e.popperJS.update();
          setTimeout(() => {
            e.$el.style.top = 'inherit';
            e.$el.style.left = '0';
          }, 20);
        }
      }, 20);
    },
    // 删除模块序列号
    handleClose(tag) {
      this.ruleForm.combinationDeviceSerialNumber.splice(this.ruleForm.combinationDeviceSerialNumber.indexOf(tag), 1);
    },
    // 添加模块序列号
    showInput() {
      this.ruleForm.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 成功添加模块序列号
    handleInputConfirm() {
      let inputValue = this.ruleForm.inputValue;
      if (inputValue) {
        if (this.ruleForm.combinationDeviceSerialNumber.length < 10) {
          this.ruleForm.combinationDeviceSerialNumber.push(inputValue);
        } else {
          this.$notify({
            message: this.$t('device.exceed'),
            type: 'warning'
          });
        }
      }
      this.ruleForm.inputVisible = false;
      this.ruleForm.inputValue = '';
    },
    // 获取设备列表
    getList() {
      this.loading = true
      // let api = this.adminRole ? deviceAdminList: deviceList
      let api = deviceList
      api({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        deviceType: this.queryInfo.deviceType,
        [this.searchKey]: this.searchValue,
        deptId: this.queryInfo.deptId
      }).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    //新建设备
    handleAddClick() {
      // if (!this.adminRole) return this.dialogQueryVisible = true
      this.dialogName = this.$t('device.AddDevice')
      this.dialogVisible = true;
      this.ruleForm = {
        deviceSerialNumber: '',//整机序列号
        deviceModel: '',//设备型号
        deviceFactoryVersion: '',//出厂软件版本
        deviceUpdateFactoryVersion: '',//出厂硬件版本
        deviceBatteryCapacity: undefined,//电池容量
        customerOrderNumber: '',//客户单号
        orderNumber: '',//订单号
        orderExplain: '',//订单特殊说明
        deviceType: 1,
        serialNumber: [],//模块序列号
        inputVisible: false,
        inputValue: '',
        deviceRatedPower: undefined,
        photovoltaicInstalledCapacity: undefined,
        combinationDeviceSerialNumber: [],
        combinationDeviceType: [],
        inputTypeVisible: false,
        inputTypeValue: '',
        showDiesel: 0
      }
      this.isGroup = false
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    //添加
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let api = addDevice
          api({
            customerOrderNumber: this.ruleForm.customerOrderNumber,
            deviceBatteryCapacity: this.ruleForm.deviceBatteryCapacity,
            deviceFactoryVersion: this.ruleForm.deviceFactoryVersion,
            deviceFactoryProtocolVersion: this.ruleForm.deviceFactoryProtocolVersion,
            deviceModel: this.ruleForm.deviceModel,
            deviceSerialNumber: this.ruleForm.deviceSerialNumber,
            deviceType: this.ruleForm.deviceType,
            deviceUpdateFactoryAgreement: this.ruleForm.deviceUpdateFactoryAgreement,
            deviceUpdateFactoryVersion: this.ruleForm.deviceUpdateFactoryVersion,
            orderExplain: this.ruleForm.orderExplain,
            orderNumber: this.ruleForm.orderNumber,
            deviceName: this.ruleForm.deviceName,
            serialNumber: this.ruleForm.serialNumber.join(','),
            deviceRatedPower: this.ruleForm.deviceRatedPower,
            photovoltaicInstalledCapacity: this.ruleForm.photovoltaicInstalledCapacity,
            combinationDeviceSerialNumber: this.ruleForm.combinationDeviceSerialNumber.join(','),
            combinationDeviceType: this.ruleForm.combinationDeviceType.join(','),
            showDiesel: this.ruleForm.showDiesel,
          }).then(response => {
            if (response.code !== 200) return this.$message({
              type: 'error',
              message: this.$t(`common['Addition Failed']`)
            });
            this.$message({
              type: 'success',
              message: this.$t(`common['Added successfully']`)
            })
            this.getList()
            this.dialogVisible = false
            this.dialogQueryVisible = false
          })
        }
      });
    },
    //取消
    handleCancelClick() {
      this.ruleForm.deviceType = 1
      this.dialogVisible = false;
    },
    //SN修改
    handleEditConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let api = editDevice
          api({
            customerOrderNumber: this.ruleForm.customerOrderNumber,
            deviceBatteryCapacity: this.ruleForm.deviceBatteryCapacity,
            deviceFactoryVersion: this.ruleForm.deviceFactoryVersion,
            deviceFactoryProtocolVersion: this.ruleForm.deviceFactoryProtocolVersion,
            deviceModel: this.ruleForm.deviceModel,
            deviceSerialNumber: this.ruleForm.deviceSerialNumber,
            deviceType: this.ruleForm.deviceType,
            deviceUpdateFactoryAgreement: this.ruleForm.deviceUpdateFactoryAgreement,
            deviceUpdateFactoryVersion: this.ruleForm.deviceUpdateFactoryVersion,
            orderExplain: this.ruleForm.orderExplain,
            orderNumber: this.ruleForm.orderNumber,
            deviceName: this.ruleForm.deviceName,
            serialNumber: this.ruleForm.serialNumber.join(','),
            deviceId: this.ruleForm.deviceId,
            deviceRatedPower: this.ruleForm.deviceRatedPower,
            photovoltaicInstalledCapacity: this.ruleForm.photovoltaicInstalledCapacity,
            combinationDeviceSerialNumber: this.ruleForm.combinationDeviceSerialNumber.join(','),
            combinationDeviceType: this.ruleForm.combinationDeviceType.join(','),
            showDiesel: this.ruleForm.showDiesel,
          }).then(res => {
            if (res.code !== 200) return this.$message({
              type: 'error',
              message: this.$t(`common['Change failed]`)
            });
            this.$message({
              type: 'success',
              message: this.$t(`common['Modify successfully']`)
            })
            this.getList()
            this.dialogVisible = false
          })
        }
      });

    },
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    //列表注销按键
    handleDeleteClick(row) {
      this.$confirm(this.$t('device.deleteDeviceHint'), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        // let api = this.adminRole ? deleteAdminDevice: deleteDevice
        let api = deleteDevice
        api({ deviceIds: row.deviceId }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    //列表里修改按键
    handleEditClick(row) {
      this.dialogName = this.$t('device.ModifyDevice')
      this.dialogVisible = true;
      // let api = this.adminRole ? getAdminDeviceInfo: getDeviceInfo
      let api = getDeviceInfo
      api({ deviceId: row.deviceId }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        });
        this.isGroup = isGroupFn(res.data.deviceType)
        this.ruleForm = {
          ...this.ruleForm,
          ...res.data,
          serialNumber: res.data.serialNumber.split(','),
          combinationDeviceType: res.data.combinationDeviceType ? res.data.combinationDeviceType.split(',').map(item => Number(item)) : [],
          combinationDeviceSerialNumber: res.data.combinationDeviceSerialNumber ? res.data.combinationDeviceSerialNumber.split(',') : []
        }
      })
    },
    /**
     * 查询设备
     */
    handleQueryConfirm() {
      getAdminDeviceByAcInfo({ ac: this.searchAc }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        this.ruleForm = {
          ...this.ruleForm,
          ...res.data,
          serialNumber: res.data.serialNumber.split(',')
        }
        this.dialogVisible = true
        this.dialogName = res.data.deviceName
      })
    },
    /**
     * 类型切换为组合类型
     */
    deviceTypeChange(type) {
      this.isGroup = isGroupFn(type)
    },
    // 删除模块序列号
    handleTypeClose(tag) {
      this.ruleForm.combinationDeviceType.splice(this.ruleForm.combinationDeviceType.indexOf(tag), 1);
    },
    // 添加模块序列号
    showTypeInput() {
      this.ruleForm.inputTypeVisible = true;
    },
    // 成功添加模块序列号
    handleInputTypeConfirm() {
      let inputTypeValue = this.ruleForm.inputTypeValue;
      if (inputTypeValue) {
        if (this.ruleForm.combinationDeviceType.length < 10) {
          this.ruleForm.combinationDeviceType.push(inputTypeValue);
        } else {
          this.$notify({
            message: this.$t('device.exceed'),
            type: 'warning'
          });
        }
      }
      this.ruleForm.inputTypeVisible = false;
      this.ruleForm.inputTypeValue = '';
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    },
    getDeviceTypeFn(type, needGroup, needEms) {
      return getDeviceType(type, needGroup, needEms)
    },
    isShowEmsFn(deviceType) {
      return isEmsFn(deviceType)
    },
    nodeClick(id) {
      this.queryInfo.deptId = id
      this.getList()
    },
    handleTreeExpand() {
      this.treeExpand = !this.treeExpand
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea__inner {
  height: 40px;
}

::v-deep .el-table__fixed-right-patch {
  background: #F8F8F9;
}

.el-tag+.el-tag {
  margin-right: 10px;
}

.el-tag:nth-child(1) {
  margin-right: 10px !important;
}

.button-new-tag {
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 200px;
  vertical-align: bottom;
}
</style>
