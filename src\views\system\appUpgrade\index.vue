<template>
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t('monitor.acVersion')" style="width: 200px;" v-model="queryInfo.version" clearable></el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
          }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{ $t('common.add') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="name" :label="$t('应用名称')" show-overflow-tooltip align="center" />
        <el-table-column prop="version" :label="$t('monitor.acVersion')" show-overflow-tooltip align="center" />
        <el-table-column prop="forceupdate" :label="$t('强制升级')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag type="primary" v-if="scope.row.forceupdate == 1">{{ $t('是') }}</el-tag>
            <el-tag type="warning" v-else>{{ $t('否') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="platform" :label="$t('平台')" show-overflow-tooltip align="center" />
        <el-table-column prop="size" :label="$t('包大小(bytes)')" show-overflow-tooltip align="center" />
        <el-table-column prop="updateUrl" :label="$t('包地址')" show-overflow-tooltip align="center" />
        <el-table-column prop="wgtFlag" :label="$t('增量升级')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-tag type="primary" v-if="scope.row.wgtFlag == 1">{{ $t('是') }}</el-tag>
            <el-tag type="warning" v-else>{{ $t('否') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="wgtUrl" :label="$t('增量包地址')" show-overflow-tooltip align="center" />
        <el-table-column prop="updateTips" :label="$t('升级说明')" show-overflow-tooltip align="center" />
        <el-table-column prop="createTime" :label="$t('common.createTime')" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button @click="handleEditClick(scope.row)" type="text" size="small">{{ $t('common.edit') }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small">{{ $t('common.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>
    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px" :title="dialogTitle">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
        <el-form-item :label="$t(`应用名称`)" prop="name">
          <el-input v-model="ruleForm.name" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t(`平台`)" prop="platform">
          <el-radio-group v-model="ruleForm.platform" style="width: 100%">
            <el-radio label="ios">ios</el-radio>
            <el-radio label="android">android</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`强制升级`)" prop="forceupdate">
          <el-radio-group v-model="ruleForm.forceupdate" style="width: 100%">
            <el-radio :label="1">{{ $t('是') }}</el-radio>
            <el-radio :label="0">{{ $t('否') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`版本号`)" prop="version">
          <el-input v-model="ruleForm.version" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t(`增量升级`)" prop="wgtFlag">
          <el-radio-group v-model="ruleForm.wgtFlag" style="width: 100%">
            <el-radio :label="1">{{ $t('是') }}</el-radio>
            <el-radio :label="0">{{ $t('否') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t(`包地址`)" prop="updateUrl" v-if="ruleForm.wgtFlag == 0">
          <el-input v-model="ruleForm.updateUrl" :placeholder="$t(`common['Please enter']`)" />
          <file-upload :value="ruleForm.updateUrl" @upload="fileUpload" @input="fileInput" size="130 x 65px" :limit="1"
            :fileSize="20" :fileType="['apk', 'ipa']"></file-upload>
        </el-form-item>
        <el-form-item :label="$t(`增量包地址`)" prop="wgtUrl" v-else>
          <el-input v-model="ruleForm.wgtUrl" :placeholder="$t(`common['Please enter']`)" />
          <file-upload :value="ruleForm.wgtUrl" @upload="fileUpload" @input="fileInput" size="128 x 128px" :limit="1"
            :fileSize="20" :fileType="['apk', 'ipa']"></file-upload>
        </el-form-item>
        <el-form-item :label="$t(`升级说明`)" prop="updateTips">
          <el-input v-model="ruleForm.updateTips" type="textarea" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { list, addAppUpgrade, editAppUpgrade, deleteAppUpgrade } from '@/api/system/appUpgrade'

export default {
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '添加包',
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      ruleForm: {},
      rules: {
        name: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        forceupdate: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        platform: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        wgtFlag: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        updateTips: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        version: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        wgtUrl: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        updateUrl: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
      }
    };
  },
  mounted() {
    this.getList()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      list(this.queryInfo).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      console.log(this.ruleForm);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == this.$t('添加包')) { // 添加
            this.addAppUpgradeFn()
          } else { // 修改
            this.editAppUpgradeFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogTitle = this.$t('添加包')
      this.dialogVisible = true;
      this.ruleForm = {
        forceupdate: undefined,
        name: undefined,
        platform: undefined,
        updateTips: undefined,
        updateUrl: undefined,
        version: undefined,
        wgtFlag: 0,
        wgtUrl: undefined,
        size: undefined,
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    // 修改
    handleEditClick(row) {
      this.dialogTitle = this.$t('修改包')
      this.dialogVisible = true;
      this.ruleForm = {
        ...row
      }
    },
    addAppUpgradeFn() {
      addAppUpgrade({
        forceupdate: this.ruleForm.forceupdate,
        name: this.ruleForm.name,
        platform: this.ruleForm.platform,
        updateTips: this.ruleForm.updateTips,
        updateUrl: this.ruleForm.updateUrl,
        version: this.ruleForm.version,
        wgtFlag: this.ruleForm.wgtFlag,
        wgtUrl: this.ruleForm.wgtUrl,
        size: this.ruleForm.size,
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Addition Failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Added successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    editAppUpgradeFn() {
      editAppUpgrade({
        forceupdate: this.ruleForm.forceupdate,
        name: this.ruleForm.name,
        platform: this.ruleForm.platform,
        updateTips: this.ruleForm.updateTips,
        updateUrl: this.ruleForm.updateUrl,
        version: this.ruleForm.version,
        wgtFlag: this.ruleForm.wgtFlag,
        wgtUrl: this.ruleForm.wgtUrl,
        size: this.ruleForm.size,
        id: this.ruleForm.id
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Change failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Modify successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    fileUpload(data) {
      console.log(data);
      if (this.ruleForm.wgtFlag == 1) {
        this.ruleForm.wgtUrl = data.filePath
      } else {
        this.ruleForm.updateUrl = data.filePath
      }
      this.ruleForm.size = data.fileSize
    },
    fileInput(res) {
      console.log(res);
      this.ruleForm.wgtUrl = undefined
      this.ruleForm.updateUrl = undefined
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteAppUpgrade({
          ids: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;
}
</style>
