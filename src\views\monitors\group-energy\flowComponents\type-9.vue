<template>
  <div class="position_box">
    <div class="flow_box">
      <!-- 电网 -->
      <div class="flow">
        <div class="dianzhan">
          <svg-icon icon-class="flow_ac" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_ac }" />
          <div class="flow-detail">
            <!-- <div>电网</div> -->
            <div>
              {{ $t('monitor.flowItem1') }}：<span>{{ flowData.power ? flowData.power : 0 }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <div class="dianchi">
          <svg-icon icon-class="flow_de" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_de }" />
        </div>
        <svg width="300px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 40 56 L40 100 Q40 110 50 110 L264 110" v-if="flowData.power < -1" />
          <!-- 反 -->
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 264 110 L50 110 Q40 110 40 100  L40 56 " v-else-if="flowData.power > 1" />
          <path id="path1" stroke-width="2" fill="transparent" stroke="url('#grad1')"
            d="M 264 110 L50 110 Q40 110 40 100  L40 56 " v-else />

          <defs>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stop-color="#436EE3" />
              <stop offset="100%" stop-color="#F1A121" />
            </linearGradient>
          </defs>
          <!-- 空心圆 -->
          <circle r="10" class="circle_1" v-if="showCircle('power')">
            <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('power')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path1" />
            </animateMotion>
          </circle>
        </svg>
      </div>
      <!-- 负载 -->
      <div class="flow">
        <div class="diandeng">
          <svg-icon icon-class="flow_load" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_load }" />
          <div class="flow-detail" style="line-height: 22px;padding-top: 7px;padding-bottom: 7px;margin-top: 0px">
            <!-- <div>负载</div> -->
            <div v-if="!isDeviceMasterTypeRT07">
              {{ $t('monitor.flowItem3') }}
              <div v-for="item in flowData.groupData" :key="item.label">
                {{ item.label }}：<span>{{ item.load }}</span>&nbsp;kW
              </div>
            </div>
            <div v-else>
              {{ $t('monitor.flowItem3') }}：<span>{{ flowData.load }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M40,50 L40,20 Q40 10 50 10  L264,10" v-if="flowData.load < -1" />
          <!-- 反 -->
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M264,10 L50,10 Q40 10 40 20  L40 50" v-else-if="flowData.load > 1" />
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M264,10 L50,10 Q40 10 40 20  L40 50" v-else />
          <defs>
            <linearGradient id="grad2" x1="0%" y1="100%" x2="100%" y2="0%" gradientUnits="userSpaceOnUse">
              <stop offset="0%" stop-color="#34BE76" />
              <stop offset="100%" stop-color="#F1A31C" />
            </linearGradient>
          </defs>
          <!-- 空心圆 -->
          <circle r="10" class="circle_1" v-if="showCircle('load')">
            <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('load')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
    <div class="flow_box">
      <!-- 电池 -->
      <div class="flow">
        <div class="transformer">
          <Bms :value="soc"></Bms>
          <div class="flow-detail" style="line-height: 22px;padding-top: 7px;padding-bottom: 7px;margin: 10px 0 0 0">
            <!-- <div>电池</div> -->
            <div v-if="!isShowV54154" style="height: 70px;overflow-y: auto;padding-right: 10px;">
              {{ $t('monitor.flowItem4') }}
              <div v-for="item in flowData.groupData" :key="item.label">
                {{ item.label }}：<span>{{ item.cell }}</span>&nbsp;kW
              </div>
            </div>
            <div v-else>
              {{ $t('monitor.flowItem4') }}：<span v-if="flowData.groupData[groupId[0]]">{{
                flowData.groupData[groupId[0]].cell }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="240px" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <!-- 正 -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m236,125 l-210,0  0,1"
            v-if="flowData.cell > 1" />
          <!-- 反  -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m25,125 l210,1  0,1"
            v-else-if="flowData.cell < -1" />
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')" d="m25,125 l210,1  0,1" v-else />

          <defs>
            <linearGradient id="grad4" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#F76655" />
              <stop offset="100%" stop-color="#F7A11A" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('cell')">
            <animate attributeName="fill" values="#F7A11A;#F76655" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('cell')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import { emTypeOptions } from '@/constant'

import Bms from './bms.vue'

export default {
  components: { Bms },
  computed: {
    groupId() {
      return this.$route.query.groupId.split(',')
    },
    control() {
      let controlFn = () => {
        let currentNodeKey = this.$store.state.monitor.currentNodeKey
        let data = this.$store.state.monitor.groupList[0]
        if (data && Object.keys(data).length == 11) return data.control
        return {
          ac: "null",
          createTime: null,
          isAnalysis: 0,
          jk_1000: null,
          jk_1001: null,
          jk_1002: null,
          jk_1003: null,
          jk_1004: null,
          jk_1005: null,
          jk_1015: null,
          jk_1016: null,
          jk_1017: null,
          jk_1031: null,
          jk_1032: null,
          jk_1033: null,
          jk_1051: null,
          jk_1056: null,
          jk_1074: null,
          jk_1077: null,
          jk_1092: null,
          jk_1093: null,
          jk_1094: null,
          jk_1095: null,
          jk_1105: null,
          jk_1106: null,
          onLineState: "离线",
          sdt: null,
        }
      }
      let controlData = controlFn()
      return controlData
    },
    flowData() {
      let data = this.$store.state.monitor.groupFlowData
      if (this.isDeviceMasterTypeRT07) {
        data.power = data.em_10012s
        data.load = (data.jk103133 - data.em_10012s).toFixed(2)
      }
      return data
    },
    // 流动拓补图展示圆
    showCircle() {
      let flowData = this.flowData
      return (name) => {
        if (!this.control) return false
        if (this.control.onLineState == '离线') return false
        if (-1 <= flowData[name] && flowData[name] <= 1) {
          return false
        } else if (flowData[name] == 0) {
          return false
        } else {
          return true
        }
      }
    },
    isShowV54154() {
      let versionStart = this.control?.jk_1000?.split('V')[1].split('.')[0]
      let versionTwo = this.control?.jk_1000?.split('V')[1].split('.')[1]
      let versionThere = this.control?.jk_1000?.split('V')[1].split('.')[2]
      if (versionStart == 5) if (versionTwo == 4154) return true
      return false
    },
    // 组合设备类型，主机为RT07储能系统(纯并网)、PCC电表，电池功率-电表有功功率,取绝对值
    isDeviceMasterTypeRT07() {
      let groupType = this.$route.query.groupType.split(',')
      let data = this.$store.state.monitor.groupList[0]
      if (data && Object.keys(data).length == 11) {
        let isEmTypePCC = data.ele.some(item => {
          return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
        })
        if (groupType[0] == 7 && isEmTypePCC) return true
      }
      return false
    },
    soc() {
      let groupList = this.$store.state.monitor.groupList
      let data = 0
      let bmsLength = 0
      groupList.forEach(item => {
        if (!item || Object.keys(item).length != 11) {
          data += 0
        } else {
          if (item.bms.length) {
            data += Number((item.bms.reduce((pre, item1) => pre += Number(item1.bms_4022), 0) / item.bms.length).toFixed(1))
            bmsLength += 1
          } else if (item.bmsBau.length) {
            data += Number((item.bmsBau.reduce((pre, item1) => pre += Number(item1.bms_4022), 0) / item.bmsBau.length).toFixed(1))
            bmsLength += 1
          } else data += 0
        }
      })
      return bmsLength ? (data / bmsLength).toFixed(2) : 0
    }
  }
}
</script>

<style lang="scss" scoped>
.position_box {
  position: absolute;
  height: 240px;
  width: 600px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;


  .flow_box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .flow {
    flex: 1;
    position: relative;

    .dianzhan {
      position: absolute;
      left: -4px;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      display: flex;
      align-items: center;
    }

    .flow-detail {
      margin-left: 10px;
      padding: 0 10px;
      border: 1px solid #d6d6d6;
      border-radius: 10px;
      /* height: 35px; */
      display: flex;
      font-size: 14px;
      line-height: 35px;

      span {
        font-weight: 600;
      }
    }

    .dianchi {
      position: absolute;
      right: -36px;
      top: 81px;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      z-index: 1000;
    }

    .diandeng {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      left: -4px;
      bottom: 4px;
      display: flex;
      align-items: center;
    }

    .transformer {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      right: 0;
      top: 85px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .img {
      width: 70px
    }
  }
}
.svg-img {
  width: 70px;
  height: 70px;
}
</style>
