/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-22 18:12:06
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-01-24 10:33:26
 * @FilePath: \elecloud_platform-main\src\api\test\dataanalysis.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取项目-设备树列表
export function getProjectTree(queryInfo) {
  return request({
    url: `/system/project/projectTree/${queryInfo.deviceType}`,
    method: 'get'
  })
}

// 获取数据对比数据
export function getDataAnalyseInfo(data) {
  return request({
    url: '/system/analyse/getDataAnalyseInfo',
    method: 'post',
    data
  })
}

// 获取数据对比单设备模块
export function getDataModule(data) {
  return request({
    url: '/system/analyse/getModules',
    method: 'post',
    data
  })
}

// 导出
export function exportAnalyse(data) {
  return request({
    url: '/system/analyse/export',
    method: 'post',
    data,
    responseType: "blob",
  })
}
