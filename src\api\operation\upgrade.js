/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-03-05 16:51:49
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-07-29 19:23:53
 * @FilePath: \elecloud_platform-main\src\api\operation\backupScheme.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取列表
export function ossList(queryInfo) {
  return request({
    url: '/system/OSSFile/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addOss(data) {
  return request({
    url: '/system/OSSFile',
    method: 'post',
    data
  })
}

// 修改
export function editOss(data) {
  return request({
    url: '/system/OSSFile',
    method: 'put',
    data
  })
}

// 删除
export function deleteOss(queryInfo) {
  return request({
    url: `/system/OSSFile/${queryInfo.id}`,
    method: 'delete'
  })
}

// 上传文件
export function uploadOss(data) {
  return request({
    url: '/common/uploadOSS',
    method: 'post',
    data
  })
}

// 下载文件
export function downloadOss(queryInfo) {
  return request({
    url: '/common/downloadOSS',
    method: 'get',
    params: queryInfo,
    responseType: "blob",
    timeout: 240000
  })
}
// 下载文件
export function downloadOSSSingapore(queryInfo) {
  return request({
    url: '/common/downloadOSSSingapore',
    method: 'get',
    params: queryInfo,
    responseType: "blob",
    timeout: 240000
  })
}

export function pullOSSFileName(data) {
  return request({
    url: '/system/OSSFile/pullOSSFileName',
    method: 'post',
    data
  })
}
