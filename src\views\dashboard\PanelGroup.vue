<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-16 09:37:32
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-18 09:15:16
 * @FilePath: \办公文档\代码\新建文件夹\src\views\dashboard\PanelGroup.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="top">
    <div class="top_box1 elevation-4">
      <div class="top_box1_item1">
        <svg-icon icon-class="capacity" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.capacity }" />
        <div class="p">{{ $t('home.item1') }}</div>
      </div>
      <div class="top_box1_item2">
        <count-to :startVal="0" :endVal="sumData.deviceBatteryCapacitySum" :duration="2000" :decimals="2" />
      </div>
      <div class="unit">kWh</div>
    </div>
    <div class="top_box1 elevation-4">
      <div class="top_box1_item1">
        <svg-icon icon-class="capacityPower" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.capacityPower }" />
        <div class="p">{{ $t('home.item2') }}</div>
      </div>
      <div class="top_box1_item2">
        <count-to :startVal="0" :endVal="sumData.deviceRatedPowerSum" :duration="2000" :decimals="2" />
      </div>
      <div class="unit">kW</div>
    </div>
    <div class="top_box1 elevation-4" style="justify-content: center;width: 14%;padding: 0;">
      <template v-if="sumData.totalIncome?.length == 1">
        <div class="top_box1_item1">
          <svg-icon icon-class="moneySum" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.moneySum }" />
          <div class="p">{{ $t(`home['总收益']`) }}</div>
        </div>
        <div class="top_box1_item2">
          <span>{{ sumData.totalIncome[0].income }} </span>
        </div>
        <div class="unit" style="margin-left: 59px">{{ sumData.totalIncome[0].currency }}</div>
      </template>
      <template v-else-if="sumData.totalIncome?.length > 1">
        <div class="swiper-container" ref="swiperRef">
          <!-- Additional required wrapper -->
          <div class="swiper-wrapper">
            <!-- Slides -->
            <div class="swiper-slide" v-for="item in sumData.totalIncome" :key="item.currency">
              <div class="top_box1_item1">
                <svg-icon icon-class="moneySum" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.moneySum }" />
                <div class="p">{{ $t(`home['总收益']`) }}</div>
              </div>
              <div class="top_box1_item2">
                <span>{{ item.income }} </span>
              </div>
              <div class="unit" style="margin-left: 59px">{{ item.currency }}</div>
            </div>
          </div>
          <!-- If we need navigation buttons -->
          <div class="swiper-button-prev" @click="handleOper('prev')"></div>
          <div class="swiper-button-next" @click="handleOper('next')"></div>
        </div>
      </template>

    </div>
    <div class="top_box1 elevation-4">
      <div class="top_box1_item1">
        <svg-icon icon-class="guangfu" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.guangfu }" />
        <div class="p">{{ $t('monitor.topItem7') }}</div>
      </div>
      <div class="top_box1_item2">
        <count-to :startVal="0" :endVal="formatThan10WFn(sumData.photovoltaicPowerCapacityCalculateSum).num"
          :duration="2000" :decimals="2" />
      </div>
      <div class="unit">{{ formatThan10WFn(sumData.photovoltaicPowerCapacityCalculateSum).unit }}</div>
    </div>
    <div class="top_box1 elevation-4">
      <div class="top_box1_item1">
        <svg-icon icon-class="charge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.charge }" />
        <div class="p">{{ $t('home.item3') }}</div>
      </div>
      <div class="top_box1_item2">
        <count-to :startVal="0" :endVal="formatThan10WFn(sumData.chargeCapacityCalculateSum).num" :duration="2000"
          :decimals="2" />
      </div>
      <div class="unit">{{ formatThan10WFn(sumData.chargeCapacityCalculateSum).unit }}</div>
    </div>
    <div class="top_box1 elevation-4">
      <div class="top_box1_item1">
        <svg-icon icon-class="discharge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.discharge }" />
        <div class="p">{{ $t('home.item4') }}</div>
      </div>
      <div class="top_box1_item2">
        <count-to :startVal="0" :endVal="formatThan10WFn(sumData.dischargeCapacityCalculateSum).num" :duration="2000"
          :decimals="2" />
      </div>
      <div class="unit">{{ formatThan10WFn(sumData.dischargeCapacityCalculateSum).unit }}</div>
    </div>
    <div class="top_box1 elevation-4">
      <div class="top_box1_item1">
        <svg-icon icon-class="device" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.device }" />
        <div class="p">{{ $t('home.pieRadioText2') }}</div>
      </div>
      <div class="top_box1_item2">
        <span>{{ sumData.deviceSum }}</span>
      </div>
      <div class="unit">--</div>
    </div>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
import { simplifyNum } from '@/utils'

import { getSum } from '@/api/system/home'

// import Swiper JS
import Swiper from 'swiper';
// import Swiper styles
import 'swiper/swiper-bundle.css';
import SwiperCore, { Autoplay } from 'swiper/core';
// configure Swiper to use modules
SwiperCore.use([Autoplay]);

export default {
  components: {
    CountTo,
  },
  data() {
    return {
      sumData: {},
      swiper: null
    }
  },
  mounted() {
    this.getSumFn()
  },
  computed: {
    simplifyNumFn: () => simplifyNum
  },
  methods: {
    getSumFn() {
      getSum().then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let data = res.data
        if (!data.totalIncome?.length) data.totalIncome.push({
          currency: this.$t(`price['元']`),
          income: 0
        })
        data.totalIncome.forEach(item => {
          item.income = item.income.toFixed(2)
        })
        this.sumData = data
        this.$nextTick(() => {
          this.initSwiper()
        })
      })
    },
    formatThan10WFn(num) {
      let isThan10W = num > 100000
      if (!isThan10W) return {
        num, unit: 'kWh'
      }
      num = num / 1000
      return {
        num, unit: 'MWh'
      }
    },
    initSwiper() {
      this.swiper = new Swiper('.swiper-container', {
        // Optional parameters
        direction: 'horizontal',
        loop: true,
        slidesPerView: 1,
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        autoplay: {
          delay: 5000,
          disableOnInteraction: false
        },

        // Navigation arrows
        // navigation: {
        //   nextEl: '.swiper-button-next',
        //   prevEl: '.swiper-button-prev',
        // },
      });
    },
    handleOper(type) {
      if (type == 'prev') {
        this.$refs.swiperRef.swiper.slidePrev()
      } else {
        this.$refs.swiperRef.swiper.slideNext()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  border-radius: 16px;
  display: flex;
  flex-wrap: wrap;

  .top_box1 {
    flex: 1;
    display: flex;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 14px;
    background-color: white;
    /* min-width: 222px; */
    padding-bottom: 15px;
    display: flex;
    flex-direction: column;
    padding: 15px 0 15px 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

    &_item1 {
      display: flex;
      align-items: center;

      img {
        height: 34px;
        width: 34px;
        margin-right: 10px;
      }

      .p {
        color: var(--base-color);
        font-size: 16px;
        text-align: left;
        display: flex;
        align-items: center;
      }
    }

    &_item2 {
      margin: 10px 0 10px 44px;

      span {
        font-size: 24px;
        font-weight: 600;
        color: var(--base-color);
      }
    }

    .unit {
      margin-left: 44px;
      font-size: 12px;
      font-weight: 400;
    }
  }

  .top_box1:last-child {
    margin-right: 0px !important;
  }

  .top_box:last-child {
    margin-right: 0px !important;
  }
}

.swiper-container {
  width: 100%;
  height: 100%;

  .swiper-slide {
    width: 100% !important;
    height: 100%;
    display: flex;
    flex-direction: column;

    .top_box1_item1 {
      display: flex;
      align-items: center;
      margin: 15px 0 0 15px;

      img {
        height: 34px;
        width: 34px;
        margin-right: 10px;
      }

      .p {
        color: var(--base-color);
        font-size: 16px;
        text-align: left;
        display: flex;
        align-items: center;
      }
    }

    .top_box1_item2 {
      margin: 10px 0 10px 59px;

      span {
        font-size: 24px;
        font-weight: 600;
        color: var(--base-color);
      }
    }

    .icon_box {
      flex: 0.4;

      img {
        height: 34px;
        width: 34px;
        float: right;
        margin-top: 20px;
        margin-right: 10px;
        max-width: 230px;
      }
    }
  }

  .swiper-button-prev {
    left: 0px !important;
    top: 60%;
  }

  .swiper-button-prev::after {
    font-size: 20px;
  }

  .swiper-button-prev,
  .swiper-button-next {
    color: var(--primary-color)
  }

  .swiper-button-next {
    right: 0px !important;
    top: 60%;
  }

  .swiper-button-next::after {
    font-size: 20px;
  }
}

.svg-img {
  height: 34px;
  width: 34px;
  min-width: 34px;
  margin-right: 10px;
}
</style>
