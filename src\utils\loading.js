/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-19 14:25:07
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-01-31 11:20:04
 * @FilePath: \elecloud_platform-main\src\utils\loading.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A
 */
import '@/assets/styles/loading.scss'

export const loading = {
  show: () => {
    const bodys = document.body
    const div = document.createElement('div')
    div.className = 'block-loading'
    div.innerHTML = `
            <div class="block-loading-box">
                <div class="block-loading-box-warp">
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                    <div class="block-loading-box-item"></div>
                </div>
            </div>
        `
    bodys.insertBefore(div, bodys.childNodes[0])
  },
  hide: () => {
    // $nextTick(() => {
    setTimeout(() => {
      const el = document.querySelector('.block-loading')
      el && el.parentNode?.removeChild(el)
    }, 1000)
    // })
  },
}
