/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-03-05 16:51:49
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-03-05 16:52:06
 * @FilePath: \elecloud_platform-main\src\api\operation\backupScheme.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取列表
export function backupList(queryInfo) {
  return request({
    url: '/system/reservePattern/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addBackup(data) {
  return request({
    url: '/system/reservePattern',
    method: 'post',
    data
  })
}

// 修改
export function editBackup(data) {
  return request({
    url: '/system/reservePattern',
    method: 'put',
    data
  })
}

// 删除
export function deleteBackup(queryInfo) {
  return request({
    url: `/system/reservePattern/${queryInfo.ids}`,
    method: 'delete'
  })
}

export function backupAll() {
  return request({
    url: '/system/reservePattern/list2',
    method: 'get'
  })
}
