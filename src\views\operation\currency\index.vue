<template>
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="$t(`国家`)" value="country"></el-option>
              <el-option :label="$t(`货币单位`)" value="currency"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
            }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{
            $t('common.add') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="country" :label="$t('国家')" show-overflow-tooltip align="center" />
        <el-table-column prop="countryEn" :label="$t('EN国家')" show-overflow-tooltip align="center" />
        <el-table-column prop="currency" :label="$t('货币单位')" show-overflow-tooltip align="center" />
        <el-table-column prop="currencyEn" :label="$t('EN货币单位')" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button @click="handleEditClick(scope.row)" type="text" size="small">{{ $t('common.edit') }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small">{{ $t('common.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>


    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px" :title="dialogTitle">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
        <el-form-item :label="$t('国家')" prop="country">
          <el-input v-model="ruleForm.country" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('EN国家')" prop="countryEn">
          <el-input v-model="ruleForm.countryEn" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('货币单位')" prop="currency">
          <el-input v-model="ruleForm.currency" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t('EN货币单位')" prop="currencyEn">
          <el-input v-model="ruleForm.currencyEn" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { currencyList, addCurrency, editCurrency, deleteCurrency } from '@/api/operation/currency'
import { languageOptions } from '@/lang'

export default {
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      ruleForm: {
        countryEn: '',
        country: '',
        currencyEn: '',
        currency: '',
      },
      rules: {
        countryEn: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        country: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        currencyEn: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        currency: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
      },
      dialogTitle: this.$t('添加货币'),
      // 搜索
      searchKey: 'currency',
      searchValue: '',
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      currencyList({...this.queryInfo, [this.searchKey]: this.searchValue, lang: languageOptions.find(item => item.value == this.$store.getters.language).api}).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == this.$t('添加货币')) {
            this.addCurrencyFn()
          } else {
            this.editCurrencyFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogVisible = true;
      this.dialogTitle = this.$t('添加货币')
      this.ruleForm = {
        countryEn: '',
        country: '',
        currencyEn: '',
        currency: '',
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    addCurrencyFn() {
      addCurrency({
        countryEn: this.ruleForm.countryEn,
        country: this.ruleForm.country,
        currencyEn: this.ruleForm.currencyEn,
        currency: this.ruleForm.currency,
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Addition Failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Added successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    handleEditClick(row) {
      this.dialogTitle = this.$t('修改货币')
      this.dialogVisible = true;
      this.ruleForm = {
        ...row
      }
    },
    editCurrencyFn() {
      editCurrency({
        countryEn: this.ruleForm.countryEn,
        country: this.ruleForm.country,
        currencyEn: this.ruleForm.currencyEn,
        currency: this.ruleForm.currency,
        id: this.ruleForm.id
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Change failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Modify successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteCurrency({
          ids: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;
}
</style>
