<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-13 09:35:31
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-28 19:04:27
 * @FilePath: \elecloud_platform-main\src\components\LangSelect\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dropdown ref="langRef" class="international" @command="handleSetLanguage" @visible-change="handelShowChange" trigger="click">
    <div style="display: flex;align-items: center;height: 100%;">
      <svg-icon class-name="international-icon" icon-class="translate" class="lang-country" />
      <span class="lang-text">{{ text }}</span>
      <svg-icon icon-class="chevron-down" v-if="!isShow" style="width: 24px;height: 24px;"></svg-icon>
      <svg-icon icon-class="chevron-up" v-else style="width: 24px;height: 24px;"></svg-icon>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="item in languageOptions" :disabled="language === item.value" :command="item.value">
        {{ item.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { languageOptions } from '@/lang'

export default {
  data() {
    return {
      isShow: false,
      languageOptions
    }
  },
  computed: {
    language() {
      return this.$store.getters.language
    },
    text() {
      return this.languageOptions.find(item => item.value == this.language).label
    }
  },
  methods: {
    handleSetLanguage(lang) {
      this.$i18n.locale = lang
      this.$store.dispatch('app/setLanguage', lang)
      this.$router.go(0)
    },
    handelShowChange(show) {
      this.isShow = show
    },
  }
}
</script>

<style scoped lang="scss">
.lang-text {
  margin-left: 3px;
  font-size: 16px
}
.lang-select {
  font-size: 16px;
  font-weight: 600;
}
.lang-country {
  width: 22px !important;
  height: 22px !important;
}
</style>
