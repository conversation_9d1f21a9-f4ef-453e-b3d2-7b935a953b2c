import { listNotice, getNotice, delNotice, addNotice, updateNotice, getUnreadNoticeCount, readNotice } from "@/api/system/notice";

const notice = {
  namespaced: true,
  state: {
    unreadCount: 0
  },

  mutations: {
    SET_UNREADCOUNT: (state, data) => {
      state.unreadCount = data
    }
  },

  actions: {
    async updateNoticeFn({ commit, dispatch }, notice) {
      await updateNotice(notice)
      dispatch('getUnreadNoticeCountFn')
      return
    },
    async addNoticeFn({ commit, dispatch }, notice) {
      await addNotice(notice)
      dispatch('getUnreadNoticeCountFn')
      return
    },
    async delNoticeFn({ commit, dispatch }, noticeId) {
      await delNotice(noticeId)
      await dispatch('getUnreadNoticeCountFn')
      return
    },
    async getUnreadNoticeCountFn({ commit }) {
      const res = await getUnreadNoticeCount()
      commit('SET_UNREADCOUNT', res.data)
      return
    },
    async readNoticeFn({ commit, dispatch }, ids) {
      await readNotice(ids)
      await dispatch('getUnreadNoticeCountFn')
      return
    },
  }
}

export default notice
