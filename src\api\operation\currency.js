import request from '@/utils/request'

// 获取列表
export function currencyList(queryInfo) {
  return request({
    url: '/system/countryCurrency/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addCurrency(data) {
  return request({
    url: '/system/countryCurrency',
    method: 'post',
    data
  })
}

// 修改
export function editCurrency(data) {
  return request({
    url: '/system/countryCurrency',
    method: 'put',
    data
  })
}

// 获取全部数据
export function allCurrency() {
  return request({
    url: '/system/countryCurrency/getAll',
    method: 'get'
  })
}

// 删除
export function deleteCurrency(queryInfo) {
  return request({
    url: `/system/countryCurrency/${queryInfo.ids}`,
    method: 'delete'
  })
}
