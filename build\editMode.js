/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-17 14:35:13
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-11-14 17:12:35
 * @FilePath: \elecloud_platform-main\build\editMode.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const inquirer = require('inquirer')
const { run } = require('runjs')
const fs = require('fs')

inquirer.prompt([{
  type: 'list',
  message: '请选择环境',
  name: 'env',
  choices: ['dev', 'prod']
},
{
  type: 'list',
  message: '请选择平台环境',
  name: 'platformEnv',
  choices: ['test', 'zh', 'en']
}
]).then(res => {
  let filePath = res.env == 'dev' ? '.env.development' : '.env.production'
  let newMode = res.platformEnv

  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      console.error(err)
      return
    }
    let str = data.replace(/[\n\t\s]+/g, '')
    let oldMode = str.split('VUE_APP_SCREEN=')[1].replace(/[\'\"]+/g, '')

    // 替换字符串
    const replacedData = data.replace(new RegExp(oldMode, 'g'), newMode);

    // console.log(newMode, oldMode)

    // 写回文件
    fs.writeFile(filePath, replacedData, 'utf8', (writeErr) => {
      if (writeErr) {
        return console.error(writeErr);
      }
      console.log('文件修改成功！');
      if (res.env == 'prod') {
        run('npm run build:prod')
      } else {
        run('npm run serve')
      }
    });
  })
})


