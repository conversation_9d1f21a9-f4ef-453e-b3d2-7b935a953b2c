<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-13 09:35:31
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-20 14:36:13
 * @FilePath: \elecloud_platform-main\src\components\LangSelect\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dropdown ref="langRef" class="international" @command="handleOpenManual" @visible-change="handelShowChange"
    trigger="click">
    <div style="display: flex;align-items: center;height: 100%;">
      <svg-icon class-name="international-icon" icon-class="text-box-outline" class="lang-country" />
      <span class="lang-text">{{ $t('操作手册') }}</span>
      <svg-icon icon-class="chevron-down" v-if="!isShow" style="width: 24px;height: 24px;"></svg-icon>
      <svg-icon icon-class="chevron-up" v-else style="width: 24px;height: 24px;"></svg-icon>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item command="zh">
        中文简体
      </el-dropdown-item>
      <el-dropdown-item command="en">
        English
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  data() {
    return {
      isShow: false
    }
  },
  methods: {
    handleOpenManual(lang) {
      if (lang == 'zh') {
        window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/doc.pdf', '_blank')
      } else if (lang == 'en') {
        window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/docEn.pdf', '_blank')
      }
    },
    handelShowChange(show) {
      this.isShow = show
    },
  }
}
</script>

<style scoped lang="scss">
.lang-text {
  margin-left: 3px;
  font-size: 14px
}

.lang-select {
  font-size: 14px;
  font-weight: 600;
}

.lang-country {
  width: 20px !important;
  height: 24px !important;
}
</style>
