<template>
  <div class="page-two-box">
    <div style="position: absolute;left: 0;top: 45%;" @click="handleTreeExpand">
      <svg-icon icon-class="tree-expand" class-name="icon" style="height: 50px;" :style="{ transform: treeExpand ? 'rotate(0deg)' : 'rotate(180deg)'}" />
    </div>
    <DeptTree @nodeClick="nodeClick" v-show="treeExpand" />
    <div class="page-two-box-content">
      <div class="input_box">
        <div class="header-title">
          {{ $route.meta.title }}
        </div>
        <div>
          <div class="input_ment">
            <span>{{ $t('common.status') }}：</span><el-select v-model="queryInfo.status"
              :placeholder="$t('common.select')" @change="handleSearchClick">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="input_ment">
            <span>{{ $t('device.type') }}：</span><el-select v-model="queryInfo.deviceType"
              :placeholder="$t('common.select')" @change="handleSearchClick">
              <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="input_ment">
            <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
              <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
                <el-option :label="'SN'" value="deviceSerialNumber"></el-option>
                <el-option :label="$t('device.name')" value="deviceName"></el-option>
                <el-option :label="$t('alarm.belong')" value="projectName"></el-option>
                <el-option :label="$t('创建人员')" value="nickName"></el-option>
                <el-option :label="$t('国家')" value="country"></el-option>
              </el-select>
            </el-input>
          </div>
          <div class="input_ment">
            <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
            }}</el-button>
          </div>
        </div>
      </div>
      <div class="table_box">
        <!-- table -->
        <el-table v-loading="loading" :data="tableData" style="width: 100%;">
          <el-table-column prop="deviceName" :label="$t('device.name')" show-overflow-tooltip min-width="140"
            align="center">
            <template slot-scope="scope">
              <span @click="handleClick(scope.row)" class="primary pointer">{{ scope.row.deviceName
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="projectName" :label="$t('alarm.belong')" show-overflow-tooltip min-width="160"
            align="center" />
          <el-table-column prop="country" :label="$t('国家')" show-overflow-tooltip min-width="160" align="center"
            v-if="headerChecked.country" />
          <el-table-column prop="deviceSerialNumber" :label="$t('device.sn')" min-width="220" show-overflow-tooltip
            align="center">
            <template slot-scope="scope">
              {{ scope.row.deviceSerialNumber }}<i class="el-icon-copy-document copy"
                v-clipboard:copy="scope.row.deviceSerialNumber" v-clipboard:success="copySuccess"></i>
            </template>
          </el-table-column>
          <el-table-column prop="power" :label="$t('device.power')" show-overflow-tooltip min-width="160"
            align="center" />
          <el-table-column prop="onLineState" :label="$t('monitor.OnlineStatus')" show-overflow-tooltip
            class-name="device-state" min-width="120" align="center">
            <template slot-scope="scope">
              <div :class="scope.row.onLineState == '在线' ? 'device-state-on' : 'device-state-off'"></div>
              <span v-if="scope.row.onLineState == '在线'">{{ $t('common.online') }}</span>
              <span v-if="scope.row.onLineState == '离线'">{{ $t('common.offline') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="workState" :label="$t('monitor.WorkingStatus')" show-overflow-tooltip min-width="130"
            align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.onLineState == '在线'">{{ getListWorkStateFn(scope.row.workState) }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="deviceModel" :label="$t('device.model')" show-overflow-tooltip min-width="120"
            align="center" />
          <el-table-column prop="deviceType" :label="$t('device.type')" show-overflow-tooltip min-width="320"
            align="center">
            <template slot-scope="scope">
              <el-tag type="primary">{{ getDeviceTypeFn(scope.row.deviceType, true) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nickName" :label="$t('创建人员')" show-overflow-tooltip min-width="160" align="center"
            v-if="headerChecked.nickName" />
          <el-table-column fixed="right" :label="$t('common.handle')"
            v-hasPermi="['system:deviceMonitoring:clearData', 'system:sendMqtt:argumentsJsonFrp']" min-width="260"
            align="center">
            <template slot="header">
              <el-dropdown trigger="hover">
                <div>
                  <span style="margin-right: 8px;">{{ $t('common.handle') }}</span>
                  <i class="el-icon-menu"></i>
                </div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <div>
                      <span style="margin-right: 8px;">{{ $t('国家') }}</span>
                      <el-checkbox v-model="headerChecked.country"></el-checkbox>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <div>
                      <span style="margin-right: 8px;">{{ $t('创建人员') }}</span>
                      <el-checkbox v-model="headerChecked.nickName"></el-checkbox>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <template slot-scope="scope">
              <el-button @click="handleControlClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:sendMqtt:argumentsJsonFrp']" :disabled="scope.row.onLineState == '离线'"
                style="padding: 0;">
                {{ $t('远程控制') }}
                <el-tooltip class="item" effect="dark" :content="$t('设备离线，不可操作')" placement="bottom"
                  v-if="scope.row.onLineState == '离线'">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </el-button>
              <el-button @click="handleRecoverClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:deviceMonitoring:clearData']" style="padding: 0;">{{ $t(`param['恢复出厂设置']`)
                }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
          @pagination="getList" style="margin-top: 20px;text-align: right;" />
      </div>

      <el-dialog :visible.sync="dialogControlVisible" center :modal-append-to-body="false" width="600px"
        :title="$t('开启远程控制')">
        <el-form :model="controlForm" :rules="controlRules" ref="controlFormRef" label-width="auto" :disabled="!isOpen">
          <el-form-item :label="`${$t('选择设备')}`" prop="ac" v-if="combinationSn.length">
            <el-select v-model="controlForm.ac" :placeholder="$t('common.select')" style="width: 100%;">
              <el-option v-for="item in combinationSn" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item :label="`${$t('设置开启密码')}`" prop="pwd">
            <el-input v-model="controlForm.pwd" type="password" :placeholder="$t('login.password')" />
          </el-form-item>
          <el-form-item :label="`${$t('VNC模式')}`" prop="vncMode">
            <el-select v-model="controlForm.vncMode" :placeholder="$t('common.select')" filterable style="width: 100%;">
              <el-option :label="$t('开启VNC')" :value="5" />
              <el-option :label="$t('开启SSH')" :value="1" v-if="$auth.hasRole('admin')" />
              <el-option :label="$t('开启SSH+VNC')" :value="3" v-if="$auth.hasRole('admin')" />
              <el-option :label="$t('开启内网VNC')" :value="7" />
            </el-select>
          </el-form-item>
          <el-form-item :label="`${$t('VNC端口')}`" prop="vncPortId"
            v-if="controlForm.vncMode == 5 || controlForm.vncMode == 3">
            <el-select v-model="controlForm.vncPortId" :placeholder="$t('common.select')" filterable
              style="width: 100%;">
              <el-option v-for="item in vncPortOptions" :key="item.id" :label="item.port" :value="item.id"
                :disabled="item.disabled" />
            </el-select>
          </el-form-item>
          <el-form-item :label="`${$t('SSH端口')}`" prop="sshPortId"
            v-if="controlForm.vncMode == 1 || controlForm.vncMode == 3">
            <el-select v-model="controlForm.sshPortId" :placeholder="$t('common.select')" filterable
              style="width: 100%;">
              <el-option v-for="item in sshPortOptions" :key="item.id" :label="item.port" :value="item.id"
                :disabled="item.disabled" />
            </el-select>
          </el-form-item>
        </el-form>
        <span v-if="controlForm.vncMode == 7">{{ $t('注：开启内网VNC的端口为5900。') }}</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleControlCancel()">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleControlConfirm('controlFormRef')" v-if="isOpen">{{
            $t('common.confirm')
          }}</el-button>
        </span>
      </el-dialog>
      <el-dialog :visible.sync="dialogControlLookVisible" center :modal-append-to-body="false" width="600px"
        :title="$t('查看远程控制信息')" style="z-index: 30000 !important">
        <el-form :model="controlLookForm" label-width="auto" disabled>
          <el-form-item :label="`${$t('设备序列号')}`" prop="ac">
            <el-input v-model="controlLookForm.ac" :placeholder="$t('login.password')" />
          </el-form-item>
          <el-form-item :label="`${$t('设置开启密码')}`" prop="pwd">
            <el-input v-model="controlLookForm.pwd" type="password" :placeholder="$t('login.password')" />
          </el-form-item>
          <el-form-item :label="`${$t('VNC模式')}`" prop="vncMode">
            <el-select v-model="controlLookForm.vncMode" :placeholder="$t('common.select')" filterable
              style="width: 100%;">
              <el-option :label="$t('开启VNC')" :value="5" />
              <el-option :label="$t('开启SSH')" :value="1" v-if="$auth.hasRole('admin')" />
              <el-option :label="$t('开启SSH+VNC')" :value="3" v-if="$auth.hasRole('admin')" />
              <el-option :label="$t('开启内网VNC')" :value="7" />
            </el-select>
          </el-form-item>
          <el-form-item :label="`${$t('VNC端口')}`" prop="vncPortId"
            v-if="controlLookForm.vncMode == 5 || controlLookForm.vncMode == 3">
            <el-select v-model="controlLookForm.vncPortId" :placeholder="$t('common.select')" filterable
              style="width: 100%;">
              <el-option v-for="item in vncPortOptions" :key="item.id" :label="item.port" :value="item.id"
                :disabled="item.disabled" />
            </el-select>
          </el-form-item>
          <el-form-item :label="`${$t('SSH端口')}`" prop="sshPortId"
            v-if="controlLookForm.vncMode == 1 || controlLookForm.vncMode == 3">
            <el-select v-model="controlLookForm.sshPortId" :placeholder="$t('common.select')" filterable
              style="width: 100%;">
              <el-option v-for="item in sshPortOptions" :key="item.id" :label="item.port" :value="item.id"
                :disabled="item.disabled" />
            </el-select>
          </el-form-item>
        </el-form>
        <span v-if="controlLookForm.vncMode == 7">{{ $t('注：开启内网VNC的端口为5900。') }}</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogControlLookVisible = false">{{ $t('common.cancel') }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getListWorkState } from '@/utils/parseBinaryToText'
import { getDeviceType, isGroupFn, deviceTypeSingleOptions, deviceTypeGroupOptions, isEmsFn } from '@/hook/useDeviceType'

import { deviceMonitoringList, listAll, vncParameterSettingByAc, argumentsJsonFrpOpen, argumentsJsonFrpClose } from '@/api/monitors/opticalstorage'
import {
  recover
} from '@/api/property/sn'

import DeptTree from '@/components/DeptTree'

export default {
  components: { DeptTree },
  data() {
    const equalToPort = (rule, value, callback) => {
      if (this.controlForm.sshPortId == value) {
        callback(new Error(this.$t('VNC和SSH的端口不能重复')));
      } else {
        callback();
      }
    };
    return {
      options: [{
        value: undefined,
        label: this.$t('common.all')
      }, {
        value: 2,
        label: this.$t('common.online')
      }, {
        value: 3,
        label: this.$t('common.offline')
      }],
      total: 10,
      tableData: [],
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      loading: false,
      /**
       * 远程控制
       */
      dialogControlVisible: false,
      controlForm: {
        ac: '',
        id: '',
        pwd: '',
        sshPortId: '',
        vncMode: '',
        vncPortId: ''
      },
      controlRules: {
        pwd: [
          { required: true, message: this.$t('请输入密码'), trigger: 'blur' },
          { min: 5, max: 20, message: this.$t(`user['5 to 20 characters in length']`), trigger: "blur" }
        ],
        vncMode: [
          { required: true, message: this.$t('common.select'), trigger: 'change' }
        ],
        ac: [
          { required: true, message: this.$t('common.select'), trigger: 'change' }
        ],
        sshPortId: [
          { required: true, message: this.$t('common.select'), trigger: 'change' }
        ],
        vncPortId: [
          { required: true, message: this.$t('common.select'), trigger: 'change' },
          { required: true, validator: equalToPort, trigger: "blur" }
        ]
      },
      vncPortOptions: [],
      sshPortOptions: [],
      acInfo: {},
      isOpen: false,
      combinationSn: [],
      dialogControlLookVisible: false,
      controlLookForm: {},
      // 搜索
      searchKey: 'deviceSerialNumber',
      searchValue: '',
      headerChecked: {
        country: false,
        nickName: false
      },
      treeExpand: false
    };
  },
  computed: {
    deviceTypeOptions() {
      return [{ label: this.$t('common.all'), value: undefined }, ...deviceTypeSingleOptions, ...deviceTypeGroupOptions]
    }
  },
  mounted() {
    if (this.$route.query.name) {
      this.searchKey = 'projectName'
      this.searchValue = this.$route.query.name
    }
    this.getList()
    this.getPortFn()
  },
  methods: {
    getListWorkStateFn(num) {
      return getListWorkState(num)
    },
    //获取设备列表
    getList() {
      this.loading = true
      deviceMonitoringList({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        status: this.queryInfo.status,
        deviceType: this.queryInfo.deviceType,
        [this.searchKey]: this.searchValue,
        deptId: this.queryInfo.deptId
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let data = res.rows
        data.forEach(item => {
          item.power = parseFloat(item.power).toFixed(2)
        })
        this.tableData = data
        this.total = res.total
        this.loading = false
      })
    },
    //查看
    handleClick(row) {
      if (isGroupFn(row.deviceType)) { // 组合设备
        let routeData = this.$router.resolve({
          path: '/monitors/group_energy',
          query: {
            id: row.deviceSerialNumber,
            type: row.deviceType,
            time: row.timeZone ? row.timeZone : '+08:00',
            groupType: row.combinationDeviceType,
            groupId: row.combinationDeviceSerialNumber,
            currency: row.countryCurrencyId
          }
        });
        window.open(routeData.href, '_blank');
      } else if (isEmsFn(row.deviceType)) {
        // EMS设备
        let routeData = this.$router.resolve({
          path: '/monitors/ems',
          query: {
            id: row.deviceSerialNumber,
            type: row.deviceType,
            time: row.timeZone ? row.timeZone : '+08:00',
            currency: row.countryCurrencyId
          }
        });
        window.open(routeData.href, '_blank');
      } else {
        // 储能
        let routeData = this.$router.resolve({
          path: '/monitors/energy',
          query: {
            id: row.deviceSerialNumber,
            type: row.deviceType,
            time: row.timeZone ? row.timeZone : '+08:00',
            currency: row.countryCurrencyId
          }
        });
        window.open(routeData.href, '_blank');
      }
    },
    //搜索
    handleSearchClick() {
      this.getList()
    },
    /**
    * 恢复出厂设置
    */
    handleRecoverClick(row) {
      this.$confirm(this.$t(`param['确定要恢复出厂设置吗？']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        recover({
          deviceSerialNumber: row.deviceSerialNumber,
          timeZone: row.timeZone,
          deviceType: row.deviceType
        }).then(res => {
          this.$message({
            type: 'success',
            message: this.$t(`param['恢复出厂设置成功，需要等 2 分钟后再查看']`)
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`param['已取消该操作']`)
        });
      });
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    },
    getDeviceTypeFn(type, needGroup) {
      return getDeviceType(type, needGroup)
    },
    /**
     * 远程控制
     */
    async handleControlClick(row) {
      this.getPortFn()
      if (isGroupFn(row.deviceType)) {
        this.combinationSn = row.combinationDeviceSerialNumber.split(',')
      } else {
        this.combinationSn = []
      }
      const res = await vncParameterSettingByAc({ ac: row.deviceSerialNumber })
      if (res.code !== 200) return this.$message({
        type: 'error',
        message: res.msg
      })
      if (res.data) {
        this.acInfo = res.data
        if (res.data.onOff == 0 && res.data.status == 1) { // 下发成功/已开启
          this.$confirm(this.$t('该设备已开启远程控制，是否需要关闭远程控制'), this.$t('common.systemPrompt'), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$t('确认关闭'),
            cancelButtonText: this.$t('查看端口'),
            type: 'warning'
          }).then(() => {
            this.$modal.loading(`${this.$t('正在关闭中')}...`);
            argumentsJsonFrpClose({ ac: row.deviceSerialNumber, id: res.data.id }).then((res) => {
              if (res.code !== 200) {
                this.$modal.closeLoading()
              }
              this.$modal.closeLoading()
              this.$message({
                type: 'success',
                message: this.$t('关闭远程成功')
              })
              this.getPortFn()
            }).catch(() => {
              this.$modal.closeLoading()
            })
          }).catch(action => {
            if (action === 'cancel') {
              this.controlForm = {
                ...res.data
              }
              this.dialogControlVisible = true
              this.isOpen = false
            }
          })
        } else {
          this.controlForm = {
            ac: isGroupFn(row.deviceType) ? '' : row.deviceSerialNumber,
            id: res.data.id,
            pwd: '',
            sshPortId: '',
            vncMode: 5,
            vncPortId: ''
          }
          this.dialogControlVisible = true
          this.isOpen = true
        }
      } else {
        this.controlForm = {
          ac: isGroupFn(row.deviceType) ? '' : row.deviceSerialNumber,
          pwd: '',
          sshPortId: '',
          vncMode: 5,
          vncPortId: ''
        }
        this.dialogControlVisible = true
        this.isOpen = true
      }
    },
    // 获取端口
    getPortFn() {
      listAll().then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let data = res.data.map(item => {
          return {
            id: item.id,
            port: item.port,
            disabled: item.status == 0,
            type: item.type
          }
        })
        this.vncPortOptions = data.filter(item => item.type == 1)
        this.sshPortOptions = data.filter(item => item.type == 0)
      })
    },
    handleControlCancel() {
      this.dialogControlVisible = false
    },
    handleControlConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const sendFn = () => {
            this.$modal.loading(`${this.$t(`param['正在下发中']`)}...`);
            argumentsJsonFrpOpen({
              ac: this.controlForm.ac,
              id: this.controlForm.id,
              pwd: this.controlForm.pwd,
              sshPortId: this.controlForm.sshPortId,
              vncMode: this.controlForm.vncMode,
              vncPortId: this.controlForm.vncPortId,
            }).then(response => {
              if (response.code !== 200) {
                this.$modal.closeLoading()
                this.$message({
                  type: 'error',
                  message: this.$t(`param['下发失败']`)
                });
              }
              this.$modal.closeLoading()
              this.dialogControlVisible = false
              this.$message({
                type: 'success',
                message: this.$t(`param['下发成功']`)
              });
              this.getPortFn()
              // setTimeout(() => {
              //   let routeData = this.$router.resolve({
              //     path: '/remote/' + this.controlForm.ac,
              //   });
              //   window.open(routeData.href, '_blank');
              // }, 30000)
            }).catch(() => {
              this.$modal.closeLoading()
            })
          }
          if (this.combinationSn.length) { // 组合设备
            vncParameterSettingByAc({ ac: this.controlForm.ac }).then(res => {
              if (res.code !== 200) return this.$message({
                type: 'error',
                message: res.msg
              })
              if (res.data) {
                this.acInfo = res.data
                if (res.data.onOff == 0 && res.data.status == 1) { // 下发成功/已开启
                  this.$confirm(this.$t('该设备已开启远程控制，是否需要关闭远程控制'), this.$t('common.systemPrompt'), {
                    distinguishCancelAndClose: true,
                    confirmButtonText: this.$t('确认关闭'),
                    cancelButtonText: this.$t('查看端口'),
                    type: 'warning'
                  }).then(() => {
                    this.$modal.loading(`${this.$t('正在关闭中')}...`);
                    argumentsJsonFrpClose({ ac: this.controlForm.ac, id: res.data.id }).then((res) => {
                      if (res.code !== 200) {
                        this.$modal.closeLoading()
                      }
                      this.$modal.closeLoading()
                      this.$message({
                        type: 'success',
                        message: this.$t('关闭远程成功')
                      })
                      this.getPortFn()
                    }).catch(() => {
                      this.$modal.closeLoading()
                    })
                  }).catch(action => {
                    if (action === 'cancel') {
                      this.controlLookForm = {
                        ...res.data
                      }
                      this.dialogControlLookVisible = true
                    }
                  })
                } else { // 该设备现在没有远程控制中
                  this.controlForm.id = res.data.id
                  sendFn()
                }
              } else { // 该设备从未远程控制过
                sendFn()
              }
            })
          } else { // 不是组合设备
            sendFn()
          }
        }
      });
    },
    nodeClick(id) {
      this.queryInfo.deptId = id
      this.getList()
    },
    handleTreeExpand() {
      this.treeExpand = !this.treeExpand
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep .device-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.device-state-on {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #28c79c;
  margin-right: 5px;
}

.device-state-off {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ff6b6b;
  margin-right: 5px;
}
</style>
