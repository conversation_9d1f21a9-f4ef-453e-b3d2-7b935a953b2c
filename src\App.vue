<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-20 11:35:49
 * @FilePath: \elecloud_platform-main\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="app" ref="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

import autofit from 'autofit.js'

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
    }
  },
  watch: {
    '$route.path': {
      handler() {
        if (this.$route.path == '/monitors/energy') {

        } else {
          // require('./utils/rem')
        }
      }
    },
  },
  mounted() {
    
  }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
