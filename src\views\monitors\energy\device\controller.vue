<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-28 19:18:06
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
      <el-descriptions-item>
        {{ $t('monitor.deviceInfo') }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot">
      <el-descriptions-item :label="$t('device.model')">
        {{ baseInfo.deviceModel }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('monitor.ratedCapacity')">
        <span v-if="baseInfo.deviceBatteryCapacity">{{ baseInfo.deviceBatteryCapacity }}kWh</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- <el-descriptions-item :label="$t('device.version')">
          {{ baseInfo.deviceFactoryVersion }}
        </el-descriptions-item> -->
      <el-descriptions-item :label="$t('monitor.topItem2')">
        {{ baseInfo.deviceRatedPower }}kW
      </el-descriptions-item>
      <el-descriptions-item :label="$t('monitor.topItem6')" v-if="isShowPhotovoltaicInstalledCapacity">
        {{ baseInfo.photovoltaicInstalledCapacity }}kWp
      </el-descriptions-item>
      <el-descriptions-item :label="$t('device.sn')">
        {{ baseInfo.deviceSerialNumber }}
      </el-descriptions-item>
      <!-- 1001：bit1、bit2 -->
      <el-descriptions-item :label="$t('monitor.jk_1001Bit1AndBit2')">
        <span v-if="control['jk_1001']">{{ getOnAndOffFn(control['jk_1001']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1002 0正常 1故障 -->
      <el-descriptions-item :label="$t('monitor.jk_1002')">
        <span v-if="control['jk_1002']">{{ control['jk_1002'] == '1' ? $t('common.fault') : $t('common.normal')
        }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1005 0正常 1告警 -->
      <el-descriptions-item :label="$t('monitor.jk_1005')">
        <span v-if="control['jk_1005']">{{ control['jk_1005'] == '1' ? $t('alarm.title') : $t('common.normal') }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1001：bit14 0并网 1离网 -->
      <el-descriptions-item :label="$t('monitor.jk_1001Bit14')">
        <span v-if="control['jk_1001']">{{ get1001Bit14Fn(control['jk_1001']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1092 -->
      <el-descriptions-item :label="$t('monitor.jk_1092')">
        <span v-if="control['jk_1092']">{{ control['jk_1092'] == "1" ? $t('common.running') : $t('common.Closure')
        }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1093 -->
      <el-descriptions-item :label="$t('monitor.jk_1093')">
        <span v-if="control['jk_1093']">{{ control['jk_1093'] == "1" ? $t('common.running') : $t('common.Closure')
        }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1001：bit3、bit4、bit5 全为0正常 -->
      <el-descriptions-item :label="$t('monitor.jk_1001Bit3AndBit4Bit5')">
        <span v-if="control['jk_1001']">{{ getWorkStateFn(control['jk_1001']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1106 -->
      <el-descriptions-item :label="$t(`param['运行模式']`)">
        <span>{{ get11056Fn(control['jk_1105'], control['jk_1001'])['1105'] }}</span>
      </el-descriptions-item>
      <!-- 1105 -->
      <el-descriptions-item :label="$t(`monitor['策略状态']`)">
        <span>{{ get11056Fn(control['jk_1105'], control['jk_1001'])['1001bit15'] }}</span>
      </el-descriptions-item>
      <!-- 电池的总状态 -> 0正常 1故障 -->
      <!-- 电池故障 1004 -->
      <!-- <el-descriptions-item :label="$t('monitor.jk_1004')">
        <span v-if="control['jk_1004']">{{ control['jk_1004'] == '1' ? $t('common.fault') :
          $t('common.normal') }}</span>
        <span v-else>--</span>
      </el-descriptions-item> -->
      <!-- 电池告警 1007 0：正常 1：告警 -->
      <!-- <el-descriptions-item :label="$t('电池告警')">
        <span v-if="control['jk_1007']">{{ control['jk_1007'] == '1' ? $t('alarm.title') :
          $t('common.normal') }}</span>
        <span v-else>--</span>
      </el-descriptions-item> -->
      <!-- 1094：0未上高压 1上高压 -->
      <el-descriptions-item :label="$t('monitor.jk_1094')">
        <span v-if="control['jk_1094']">{{ control['jk_1094'] == '1' ? $t('monitor.highV2') :
          $t('monitor.highV1') }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('monitor.onLineState')">
        <span v-if="control['onLineState'] == '在线'">{{ $t('common.online') }}</span>
        <span v-else-if="control['onLineState'] == '离线'">{{ $t('common.offline') }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1095：0关闭 -->
      <el-descriptions-item :label="$t('monitor.jk_1095')" v-if="isShowV75019()">
        <span v-if="control['jk_1095']">{{ get1095Fn(control['jk_1095']) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.water')" v-if="isShowV75019()">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 2) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.smoke')" v-if="isShowV75019()">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 3) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.thunder')">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 4) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1003：根据十进制转2进制，4->0100 -->
      <el-descriptions-item :label="$t('monitor.fire')" v-if="isShowV75019()">
        <span v-if="control['jk_1003']">{{ get1003Fn(control['jk_1003'], 5) }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1031-1033：ABC相加 -->
      <el-descriptions-item :label="$t('monitor.jk_1031')">
        <span v-if="control['jk_1031']">{{ control['power'] }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1051 -->
      <!-- <el-descriptions-item :label="$t('monitor.jk_1031')">
        <span v-if="control['jk_1031']">{{ control['power'] }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item> -->
      <!-- 1051 -->
      <!-- 发电机功率，为电网功率的值 -->
      <template v-if="isShowDiesel">
        <el-descriptions-item :label="$t('发电机功率')">
          <span v-if="flowData.power">{{ flowData.power }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </template>
      <template v-else>
        <el-descriptions-item :label="$t('monitor.flowItem1')" v-if="isShowGird">
          <span v-if="flowData.power">{{ flowData.power }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </template>
      <!-- 1015-1017 -->
      <el-descriptions-item :label="$t('monitor.flowItem3')" v-if="isShowLoad">
        <span v-if="flowData.load">{{ flowData.load }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1015-1017 -->
      <el-descriptions-item :label="$t('monitor.flowItem2')" v-if="isShowPhotovoltaicInstalledCapacity">
        <span v-if="control['jk_1074']">{{ control['jk_1074'] }}kW</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <!-- 1000 -->
      <el-descriptions-item :label="$t('采集屏版本')" v-if="control['jk_1107']">
        <span v-if="control['jk_1107']">{{ control['jk_1107'] }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('monitor.version')">
        <span v-if="control['jk_1000']">{{ control['jk_1000'] }}</span>
        <span v-else>--</span>
      </el-descriptions-item>
      <el-descriptions-item :label="$t('device.address')">
        {{ baseInfo.projectAddress }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t(`common['时区']`)">
        {{ baseInfo[getPropFn] }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import { getOnAndOff, get1001Bit14, getWorkState, get1003, get1001Bit15, isShow3502Bit10 } from '@/utils/parseBinaryToText'
import { controlAirStatusOptions } from '@/constant'

export default {
  computed: {
    control() {
      return this.$store.state.monitor.control
    },
    baseInfo() {
      return this.$store.state.monitor.baseInfo
    },
    flowData() {
      return this.$store.state.monitor.flowData
    },
    get1095Fn() {
      return (num) => {
        let value = controlAirStatusOptions.find(item => item.value == num)
        if (value) return value.label
        return '--'
      }
    },
    isShowPhotovoltaicInstalledCapacity() {
      let type = this.$route.query.type
      return type == 1 || type == 2 || type == 4 || type == 5 || type == 6
    },
    isShowLoad() {
      let type = this.$route.query.type
      return type == 1 || type == 2 || type == 9 || type == 10
    },
    isShowGird() {
      let type = this.$route.query.type
      return type == 1 || type == 2 || type == 5 || type == 6 || type == 7 || type == 8 || type == 9 || type == 10
    },
    get11056Fn() {
      return (pt1, pt2) => {
        switch (pt1) {
          case '0':
            return {
              // '1001bit15': this.$t(`monitor['未使用']`),
              '1001bit15': this.get1001Bit15Fn(pt2) == this.$t(`common['停止']`) ? this.$t(`monitor['未使用']`) : this.get1001Bit15Fn(pt2),
              '1105': this.$t(`param['手动模式']`),
            }
          case '1':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t(`param['削峰填谷']`),
            }
          case '2':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t(`param['后备模式']`),
            }
          case '3':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t('动态扩容'),
            }
          case '4':
            return {
              '1001bit15': this.get1001Bit15Fn(pt2),
              '1105': this.$t('光伏消纳'),
            }
          default:
            return {
              '1001bit15': '--',
              '1105': '--',
            }
        }
      }
    },
    // 是否显示发电机
    isShowDiesel() {
      let sts = this.$store.state.monitor.pcs_sts
      if (sts.length) return isShow3502Bit10(sts[0]['sts_3502'])
      return false
    },
    getPropFn() {
      let lang = this.$store.getters.language
      switch (lang) {
        case 'zh':
          return 'timeZoneAddress'
        case 'en':
          return 'timeZoneAddressUs'
        case 'it':
          return 'timeZoneAddressIt'
      }
    }
  },
  methods: {
    get1001Bit15Fn(num) {
      return get1001Bit15(num)
    },
    getOnAndOffFn(num) {
      return getOnAndOff(num)
    },
    get1001Bit14Fn(num) {
      return get1001Bit14(num)
    },
    getWorkStateFn(num) {
      return getWorkState(num)
    },
    get1003Fn(num, bit) {
      return get1003(num, bit)
    },
    isShowV75019() {
      let versionStart = this.control?.jk_1000?.split('V')[1].split('.')[0]
      let versionTwo = this.control?.jk_1000?.split('V')[1].split('.')[1]
      let versionThere = this.control?.jk_1000?.split('V')[1].split('.')[2]
      if (versionStart == 7) if (versionTwo == 5019) return false
      return true
    }
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
}
</style>
