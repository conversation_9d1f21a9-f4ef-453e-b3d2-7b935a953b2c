<template>
  <div class="app-container">
    <div class="input_box">
      <div class="header-title">{{ $route.meta.title }}</div>
      <div>
        <div class="input_ment">
        <el-input
          v-model="queryParams.menuName"
          :placeholder="$t(`menu['Please enter menu name']`)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </div>
      <div class="input_ment">
        <el-select v-model="queryParams.status" :placeholder="$t('menu.status')" clearable style="width: 120px">
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </div>
      <div class="input_ment">
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
      </div>
      <div class="input_ment">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-hasPermi="['system:menu:add']">
        {{ $t('common.add') }}</el-button>
      </div>
      <div class="input_ment">
        <el-button
          type="info"
          icon="el-icon-sort"
          @click="toggleExpandAll"
        >{{ $t('role.expand') }}</el-button>
      </div>
      </div>
    </div>


    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="menuList"
      row-key="menuId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="menuName" :label="$t('menu.name')" show-overflow-tooltip width="160" />
      <el-table-column prop="icon" :label="$t('menu.icon')" align="center" width="100">
        <template slot-scope="scope">
          <svg-icon :icon-class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" :label="$t('menu.sort')" width="60" />
      <el-table-column prop="perms" :label="$t('menu.perms')" show-overflow-tooltip />
      <el-table-column prop="component" :label="$t('menu.com')" show-overflow-tooltip />
      <el-table-column prop="status" :label="$t('common.status')" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.handle')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:menu:edit']"
          >{{ $t('common.edit') }}</el-button>
          <el-button
            type="text"
            @click="handleAdd(scope.row)"
            v-hasPermi="['system:menu:add']">
          {{ $t('common.add') }}</el-button>
          <el-button
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:menu:remove']"
          >{{ $t('common.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="680px" append-to-body center :modal-append-to-body="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('menu.parent')" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="menuOptions"
                :normalizer="normalizer"
                :show-count="true"
                :placeholder="$t(`menu['Select previous menu']`)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('menu.type')"  prop="menuType">
              <el-radio-group v-model="form.menuType">
                <el-radio label="M">{{ $t('menu.type1')}}</el-radio>
                <el-radio label="C">{{ $t('menu.type2')}}</el-radio>
                <el-radio label="F">{{ $t('menu.type3')}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.menuType != 'F'">
            <el-form-item :label="$t('menu.menuIcon')" prop="icon">
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" :active-icon="form.icon" />
                <el-input slot="reference" v-model="form.icon" :placeholder="$t(`menu['Click Select icon']`)" readonly>
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    style="width: 25px;"
                  />
                  <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('menu.name')" prop="menuName">
              <el-input v-model="form.menuName" :placeholder="$t(`menu['Please enter menu name']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('menu.show')" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="isFrame">
              <span slot="label">
                <el-tooltip :content="$t(`menu['If you select external link, the routing address needs to start with http(s)://']`)" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('menu.out') }}
              </span>
              <el-radio-group v-model="form.isFrame">
                <el-radio label="0">{{ $t('menu.yes') }}</el-radio>
                <el-radio label="1">{{ $t('menu.no') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="path">
              <span slot="label">
                <el-tooltip :content="$t(`menu['The accessed routing address, such as: user, if the external network address requires internal link access, it starts with http(s)://']`)" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('menu.router') }}
              </span>
              <el-input v-model="form.path" :placeholder="$t(`menu['Please enter routing address']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="component">
              <span slot="label">
                <el-tooltip :content="$t(`menu['The component path to be accessed, such as: system/user/index, which is in the views directory by default']`)" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('menu.com') }}
              </span>
              <el-input v-model="form.component" :placeholder="$t(`menu['Please enter component Path']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'M'">
            <el-form-item prop="perms">
              <el-input v-model="form.perms" :placeholder="$t(`menu['Please enter permission id']`)" maxlength="100" />
              <span slot="label">
                <el-tooltip :content="`${$t(`menu['The authority characters defined in the controller, for example:']`)}@PreAuthorize('@ss.hasPermi('system:user:list')')`" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('role.chart') }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="query">
              <el-input v-model="form.query" :placeholder="$t(`menu['Please enter routing parameters']`)" maxlength="255" />
              <span slot="label">
                <el-tooltip :content='`${$t(`menu["Default passed parameters for access routes, such as:"]`)}{"id": 1, "name": "ry"}`' placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('menu.param') }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="isCache">
              <span slot="label">
                <el-tooltip :content="$t(`menu['If Yes is selected']`)" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('menu.strorg') }}
              </span>
              <el-radio-group v-model="form.isCache">
                <el-radio label="0">{{ $t('menu.cache') }}</el-radio>
                <el-radio label="1">{{ $t('menu.uncached') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="visible">
              <span slot="label">
                <el-tooltip :content="$t(`menu['Select Hide and the route will not appear in the sidebar but will still be accessible.']`)" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('menu.showStatus') }}
              </span>
              <el-radio-group v-model="form.visible">
                <el-radio
                  v-for="dict in dict.type.sys_show_hide"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="status">
              <span slot="label">
                <el-tooltip :content="$t(`menu['If Disable is selected, the route will not appear in the sidebar and cannot be accessed']`)" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                {{ $t('menu.status') }}
              </span>
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from "@/api/system/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";

export default {
  name: "Menu",
  dicts: ['sys_show_hide', 'sys_normal_disable'],
  components: { Treeselect, IconSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        menuName: undefined,
        visible: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        menuName: [
          { required: true, message: this.$t(`menu['Menu name cannot be empty']`), trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: this.$t(`menu['Menu sort cannot be empty']`), trigger: "blur" }
        ],
        path: [
          { required: true, message: this.$t(`menu['Route address cannot be empty']`), trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 选择图标
    selected(name) {
      this.form.icon = name;
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      listMenu(this.queryParams).then(response => {
        this.menuList = this.handleTree(response.data, "menuId");
        this.loading = false;
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) { 
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      listMenu().then(response => {
        this.menuOptions = [];
        const menu = { menuId: 0, menuName: this.$t(`menu['Main category']`), children: [] };
        menu.children = this.handleTree(response.data, "menuId");
        this.menuOptions.push(menu);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuId: undefined,
        parentId: 0,
        menuName: undefined,
        icon: undefined,
        menuType: "M",
        orderNum: undefined,
        isFrame: "1",
        isCache: "0",
        visible: "0",
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.menuId) {
        this.form.parentId = row.menuId;
      } else {
        this.form.parentId = 0;
      }
      this.open = true;
      this.title = this.$t('menu.addMenu');
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      getMenu(row.menuId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('menu.editMenu');
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.menuId != undefined) {
            updateMenu(this.form).then(response => {
              this.$modal.msgSuccess(this.$t(`common['Modify successfully']`));
              this.open = false;
              this.getList();
            });
          } else {
            addMenu(this.form).then(response => {
              this.$modal.msgSuccess(this.$t(`common['Added successfully']`));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm(this.$t(`menu['Are you sure to delete the data item?']`)).then(function() {
        return delMenu(row.menuId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t(`common['Deleted successfully']`));
      }).catch(() => {});
    }
  }
};
</script>
