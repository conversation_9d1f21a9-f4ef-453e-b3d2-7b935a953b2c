<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-06-21 14:20:56
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-07-03 16:36:19
 * @FilePath: \elecloud_platform-main\src\views\system\user\profile\forgetPwd.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="forget-pwd">
    <div class="forget-pwd-top">
      <div>
        <img v-if="domainInfo.logo" :src="domainInfo.logo" alt="logo" class="logo" />
      </div>
      <!-- 国际化 -->
      <div class="forget-pwd-top-ri">
        <div class="forget-pwd-top-ri-oper" @click="handleOperClick">
          <i class="el-icon-collection"
            style="font-size: larger;font-weight: 600;line-height: inherit;margin-right: 4px;"></i>
          <span>{{ $t('操作手册') }}</span>
        </div>
        <lang-select class="lang"></lang-select>
      </div>
    </div>
    <div class="forget-pwd-cont">
      <div class="forget-pwd-cont-card">
        <template v-if="isActive == 1">
          <div class="forget-pwd-cont-card-title">{{ $t('找回账号或者密码') }}</div>
          <div class="forget-pwd-cont-card-tip">{{ $t('请输入您账号绑定的邮箱地址') }}</div>
          <el-input v-model="form.email" maxlength="50" :placeholder="$t(`user['Please enter email']`)"
          class="forget-pwd-cont-card-input" />
          <div class="forget-pwd-cont-card-btn-box">
            <el-button :style="{ borderRadius: $convertPx(30, 'rem'), height: $convertPx(50, 'rem') }"
              @click.native.prevent="handleBack" class="forget-pwd-cont-card-btn">
              {{ backText == 1 ? $t('返回登录'): $t('common.back') }}
            </el-button>
            <el-button type="primary" :style="{ borderRadius: $convertPx(30, 'rem'), height: $convertPx(50, 'rem') }"
              @click.native.prevent="handleOneClick" class="forget-pwd-cont-card-btn">
              {{ $t('发送验证码') }}
            </el-button>
          </div>
        </template>
        <template v-if="isActive == 2">
          <div class="forget-pwd-cont-card-title">{{ $t('验证身份') }}</div>
          <div class="forget-pwd-cont-card-tip">{{ $t('请输入验证码') }}</div>
          <el-input v-model="form.code" maxlength="4" :placeholder="$t('请输入验证码')" class="forget-pwd-cont-card-input" />
          <div class="forget-pwd-cont-card-btn-box">
            <el-button :style="{ borderRadius: $convertPx(30, 'rem'), height: $convertPx(50, 'rem') }"
              @click.native.prevent="isActive = 1" class="forget-pwd-cont-card-btn">
              {{ $t('返回上一步') }}
            </el-button>
            <el-button type="primary" :style="{ borderRadius: $convertPx(30, 'rem'), height: $convertPx(50, 'rem') }"
              @click.native.prevent="handleTwoClick" class="forget-pwd-cont-card-btn">
              {{ $t('下一步') }}
            </el-button>
          </div>
        </template>
        <template v-if="isActive == 3">
          <div class="forget-pwd-cont-card-title">{{ $t('重置密码') }}</div>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" class="forget-pwd-cont-card-form">
            <el-form-item :label="$t('login.username')">
              <div>{{ form.username }}</div>
            </el-form-item>
            <el-form-item :label="$t('user.email')">
              <div>{{ form.email }}</div>
            </el-form-item>
            <el-form-item :label="$t('login.password')" prop="password">
              <el-input v-model="form.password" :placeholder="$t('请输入密码')" type="password" show-password />
            </el-form-item>
          </el-form>
          <div class="forget-pwd-cont-card-btn-box">
            <el-button :style="{ borderRadius: $convertPx(30, 'rem'), height: $convertPx(50, 'rem') }"
              @click.native.prevent="isActive = 2" class="forget-pwd-cont-card-btn">
              {{ $t('返回上一步') }}
            </el-button>
            <el-button type="primary" :style="{ borderRadius: $convertPx(30, 'rem'), height: $convertPx(50, 'rem') }"
              @click.native.prevent="handleThreeClick" class="forget-pwd-cont-card-btn">
              {{ $t('common.confirm') }}
            </el-button>
          </div>
        </template>
        <template v-if="isActive == 4">
          <el-result icon="success" :title="$t('成功提示')" :subTitle="$t('密码已重置成功啦')">
            <template slot="extra">
              <el-button type="primary" size="medium" @click="$router.push('/login')">{{ $t('去登录') }}</el-button>
            </template>
          </el-result>
        </template>
        <div class="forget-pwd-cont-card-hint">
          <div>{{ $t('特别说明') }}：</div>
          <div>1. {{ $t('找回步骤：输入您想找回密码或者账号的邮箱地址 - 输入4位验证码，完成身份验证 - 设置新密码。') }}</div>
          <div>2. {{ $t('如果您忘记了邮箱地址或者没有邮箱，那么您应该联系管理员帮您重置您的密码，客服电话：') }}<el-link type="primary" :underline="false">+86-0755-23051586</el-link>{{ $t('。') }}</div>
          <div>3. {{ $t('进行身份验证时，验证码有效时长为5分钟，如您在身份验证页面停留过久（5分钟），那么您将身份验证失败。') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
export default defineComponent({
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.path == '/login') {
        vm.backText = 1
      } else {
        vm.backText = 0
        vm.formRoute = from.fullPath
      }
    })
  },
})
</script>
<script setup>
import { computed, getCurrentInstance, ref, defineExpose } from 'vue'
import Cookies from "js-cookie";
import { useStore, useRouter, useRoute } from '@/utils/vueApi.js'
import { sendEmailCode, CheckVerificationCode, resetPwd } from '@/api/login'

import LangSelect from '@/components/LangSelect'

const { proxy } = getCurrentInstance()
const store = useStore()
const router = useRouter()
const route = useRoute()
// 返回
const backText = ref(1)
const formRoute = ref('')
const handleBack = () => {
  if (backText.value == 1) {
    router.push('/login')
  } else {
    router.push(formRoute.value)
  }
}

const domainInfo = computed(() => {
  const origin = window.location.origin
  let data = store.state.common.domainInfo
  let title = Cookies.get("language") == 'zh' ? data.title : data.titleUs
  if (data) return {
    title,
    logo: data.homeLogoPath ? `${origin}/prod-api${data.homeLogoPath}` : null
  }
  return {
    title: proxy.$t('login.title'),
    logo: null
  }
})

const form = ref({})
const rules = ref({
  password: [
    { required: true, message: proxy.$t(`user['Please enter your new password']`), trigger: "blur" }
  ]
})
const isActive = ref(1)
// 第一步：输入邮箱
const handleOneClick = () => {
  if (!form.value.email) {
    proxy.$modal.msgError(proxy.$t(`user['Please enter email']`))
  } else {
    proxy.$modal.loading(`${proxy.$t('正在发送验证码，请稍后')}...`)
    sendEmailCode({ email: form.value.email, subjectName: domainInfo.value.title }).then(res => {
      if (res.code !== 200) {
        proxy.$message({
          type: 'error',
          message: res.msg
        })
        proxy.$modal.closeLoading()
        return
      }
      isActive.value = 2
      form.value.username = res.msg
      proxy.$modal.closeLoading()

    }).catch(() => {
      proxy.$modal.closeLoading()
    })
  }
}
// 第二步：验证邮箱验证码
const handleTwoClick = () => {
  if (!form.value.code) {
    proxy.$modal.msgError(proxy.$t('login.vCode'))
  } else {
    proxy.$modal.loading(`${proxy.$t('正在验证身份，请稍后')}...`)
    CheckVerificationCode({ code: form.value.code, email: form.value.email }).then(res => {
      if (res.code !== 200) {
        proxy.$message({
          type: 'error',
          message: res.msg
        })
        proxy.$modal.closeLoading()
        return
      }
      proxy.$modal.closeLoading()
      proxy.$message({
        type: 'success',
        message: proxy.$t('验证成功')
      })
      isActive.value = 3

    }).catch(() => {
      proxy.$modal.closeLoading()
    })
  }
}
// 第三步：重置密码
const handleThreeClick = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      proxy.$modal.loading(`${proxy.$t('正在重置密码，请稍后')}...`)
      resetPwd({ password: form.value.password, email: form.value.email }).then(res => {
        if (res.code !== 200) {
          proxy.$message({
            type: 'error',
            message: res.msg
          })
          proxy.$modal.closeLoading()
          return
        }
        proxy.$modal.closeLoading()
        isActive.value = 4
      }).catch(() => {
        proxy.$modal.closeLoading()
      })
    }
  })
}

// 操作手册
const handleOperClick = () => {
  window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/doc.pdf', '_blank')
}

defineExpose({
  backText,
  formRoute
})
</script>

<style lang="scss" scoped>
.forget-pwd {
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
  min-width: 1912px;

  &-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    background: #fff;
    /* border-bottom: 1px solid #dcdfe6; */
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px 0px;

    &-ri {
      display: flex;
      align-items: center;
      color: #606266;
      height: 20px;

      &-oper {
        display: flex;
        align-items: center;
        margin-right: 20px;
        cursor: pointer;
      }
    }
  }

  &-cont {
    width: 100%;
    display: flex;
    justify-content: center;
    min-height: 90%;

    &-card {
      margin-top: 40px;
      width: 60%;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 4px;
      border: 1px solid #e6ebf5;
      background-color: #FFFFFF;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      padding: 30px 0;
      position: relative;

      &-title {
        font-size: 28px;
      }

      &-tip {
        font-size: 14px;
        color: #606266;
        margin: 20px 0;
      }

      &-btn-box {
        display: flex;
        justify-content: space-between;
      }

      &-btn {
        width: 300px;
        margin-top: 40px;
      }

      &-input {
        height: 50px;
        width: 38%;
        margin: 20px 0;

        :deep(.el-input__inner) {
          height: 50px;
          font-size: 16px;
        }
      }

      &-form {
        width: 40%;
        margin-top: 30px;
      }

      &-hint {
        color: #909399;
        width: 50%;
        border-top: 1px solid #dcdfe6;
        padding-top: 30px;
        margin-top: 60px;

        div {
          margin-bottom: 5px;
        }
      }

      :deep(.el-result) {
        .el-result__icon svg {
          width: 120px;
          height: 120px;
        }
      }
    }
  }
}

.lang {
  background: #fff;
  font-size: 16px !important;

  .lang-text {
    font-size: 16px !important;
  }

  .lang-select {
    font-size: 16px !important;
  }
}
</style>
