<template>
  <div class="upload-file">
    <el-upload multiple :action="uploadFileUrl" :before-upload="handleBeforeUpload" :file-list="fileList" :limit="limit"
      :on-error="handleUploadError" :on-exceed="handleExceed" :on-success="handleUploadSuccess"
      :show-file-list="type == 1 ? true : false" :headers="headers" class="upload-file-uploader" ref="fileUpload"
      :data="data" :http-request="handleHttpRequest" :on-remove="handleDelete">
      <!-- 上传按钮 -->
      <el-button size="mini" type="primary">{{ $t(`oss['选择文件']`) }}</el-button>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip">
        <template v-if="type == 1">
          {{ $t(`oss['请上传 大小不超过']`) }} <b style="color: #f56c6c">{{ fileSize }}MB</b>
          <template v-if="fileType.length && isHex"> {{ $t('格式为') }} <b style="color: #f56c6c">hex</b> </template>{{
      $t(`oss['的文件']`) }}
          <div v-if="isHex">{{ $t('文件名称格式') }}：
            <strong style="color: #f56c6c" v-if="data.type == 3 && data.upgradeObject == 'DSP'">DSP_MAC_xxx_V(0-99)_(0-999)_(0-999).hex</strong>
            <strong style="color: #f56c6c" v-if="data.type == 3 && data.upgradeObject == 'ARM'">ARM_MAC_xxx_V(0-99)_(0-999)_(0-999).hex</strong>
            <strong style="color: #f56c6c" v-if="data.type == 4 && data.upgradeObject == 'DSP'">DSP_MDC_xxx_V(0-99)_(0-999)_(0-999).hex</strong>
            <strong style="color: #f56c6c" v-if="data.type == 4 && data.upgradeObject == 'ARM'">ARM_MDC_xxx_V(0-99)_(0-999)_(0-999).hex</strong>
            <strong style="color: #f56c6c" v-if="data.type == 5 && data.upgradeObject == 'DSP'">DSP_MSTS_xxx_V(0-99)_(0-999)_(0-999).hex</strong>
            <strong style="color: #f56c6c" v-if="data.type && !data.upgradeObject">ARM/DSP_MAC/MDC/MSTS_xxx_V(0-99)_(0-999)_(0-999).hex</strong>
          </div>
          <div v-else>{{ $t('文件名称格式') }}：<strong style="color: #f56c6c">xxx_V(0-99)_(0-9999)_(0-9999)</strong></div>
        </template>
        <template v-else-if="type == 3">
          {{ $t(`oss['请上传 大小不超过']`) }} <b style="color: #f56c6c">{{ fileSize }}MB</b>
          {{ $t('格式为') }} <b style="color: #f56c6c">{{ fileType.join("、") }}</b>{{
            $t(`oss['的文件']`) }}
          <div>{{ $t('文件名称格式') }}：<strong style="color: #f56c6c">xxx_V(0-99)_(0-9999)_(0-9999)</strong></div>
        </template>
        <template v-else>
          请上传
          <template v-if="fileSize">大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
          <template v-if="fileType.length"> 格式为 <b style="color: #f56c6c">{{ fileType.join("、") }}</b> </template>
          <template v-if="size"> 尺寸为 <b style="color: #f56c6c">{{ size }}</b> </template>
          的文件
        </template>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.url" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :href="`${baseUrl}${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger">{{ $t('common.delete') }}</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import request from '@/utils/request'

export default {
  name: "FileUpload",
  props: {
    // 值
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => [],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 尺寸
    size: [String],
    // 上传url
    uploadFileUrl: {
      type: String,
      default: process.env.VUE_APP_BASE_API + "/common/upload"
    },
    // 额外参数
    data: {
      type: Object,
      default: () => ({ type: undefined, upgradeObject: undefined })
    },
    // 类型
    type: {
      type: Number
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      // uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      isHex: false
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',');
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === "string") {
              item = { name: item, url: item };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    },
    data: {
      handler() {
        this.isHex = this.data.type == 2 ? false : true
      },
      deep: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      if (this.type && !this.data.type) { // 上传设备升级文件类型校验
        this.$message({
          type: 'error',
          message: this.$t(`oss['请先选择文件版本类型']`)
        })
        return false
      }
      if (this.type && (this.data.type == 3 || this.data.type == 4 || this.data.type == 5) && !this.data.upgradeObject) {
        this.$message({
          type: 'error',
          message: '请先选择要上传的升级对象'
        })
        return false
      }
      if (this.type && (this.data.type == 3 || this.data.type == 4 || this.data.type == 5) && this.data.upgradeObject) {
        const mac_dsp_patt = /^(DSP_|dsp_)(MAC|mac)_[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,3})_([0-9]{1,3})\.hex$/
        const mac_arm_patt = /^(ARM_|arm_)(MAC|mac)_[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,3})_([0-9]{1,3})\.hex$/
        const mdc_arm_patt = /^(ARM_|arm_)(MDC|mdc)_[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,3})_([0-9]{1,3})\.hex$/
        const mdc_dsp_patt = /^(DSP_|dsp_)(MDC|mdc)_[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,3})_([0-9]{1,3})\.hex$/
        const sts_dsp_patt = /^(DSP_|dsp_)(MSTS|msts)_[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,3})_([0-9]{1,3})\.hex$/
        if (this.data.type == 3 && this.data.upgradeObject == 'DSP') {
          if (!mac_dsp_patt.test(file.name)) {
            this.$message({
              type: 'error',
              message: '文件名称错误，请严格按照格式上传，提示：请带含MAC、DSP的文件名称'
            })
            return false
          }
        }
        if (this.data.type == 3 && this.data.upgradeObject == 'ARM') {
          if (!mac_arm_patt.test(file.name)) {
            this.$message({
              type: 'error',
              message: '文件名称错误，请严格按照格式上传，提示：请带含MAC、ARM的文件名称'
            })
            return false
          }
        }
        if (this.data.type == 4 && this.data.upgradeObject == 'DSP') {
          if (!mdc_dsp_patt.test(file.name)) {
            this.$message({
              type: 'error',
              message: '文件名称错误，请严格按照格式上传，提示：请带含MDC、DSP的文件名称'
            })
            return false
          }
        }
        if (this.data.type == 4 && this.data.upgradeObject == 'ARM') {
          if (!mdc_arm_patt.test(file.name)) {
            this.$message({
              type: 'error',
              message: '文件名称错误，请严格按照格式上传，提示：请带含MDC、ARM的文件名称'
            })
            return false
          }
        }
        if (this.data.type == 5 && this.data.upgradeObject == 'DSP') {
          if (!sts_dsp_patt.test(file.name)) {
            this.$message({
              type: 'error',
              message: '文件名称错误，请严格按照格式上传，提示：请带含STS、DSP的文件名称'
            })
            return false
          }
        }
      }
      if (this.type && this.data.type == 2) { // HMI文件上传
        // HMI文件名称格式
        const name = file.name.split('.')[0]
        const patt = /^[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,4})_([0-9]{1,4})$/
        if (!patt.test(name)) {
          this.$message({
            type: 'error',
            message: '文件名称错误，请严格按照格式上传'
          })
          return false
        }
      } else if (this.type && (this.data.type == 3 || this.data.type == 4 || this.data.type == 5)) { // 升级包
        // 其他类型文件名称格式
        const patt = /^(ARM_|DSP_|arm_|dsp_)(MAC|mac|MDC|mdc|MSTS|msts)_[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,3})_([0-9]{1,3})\.hex$/
        if (!patt.test(file.name)) {
          this.$message({
            type: 'error',
            message: '文件名称错误，请严格按照格式上传'
          })
          return false
        }
      }
      if (this.type == 3 && this.data.type == 6) {
        if (this.data.fileType == 'tar') {
          const patt = /^[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,2})_([0-9]{1,2})\.tar$/
          if (!patt.test(file.name)) {
            this.$message({
              type: 'error',
              message: '文件名称错误，请严格按照格式上传'
            })
            return false
          }
        } else if (this.data.fileType == 'ini') {
          const patt = /^[a-zA-Z0-9_]+_V([0-9]{1,2})_([0-9]{1,2})_([0-9]{1,2})\.ini$/
          if (!patt.test(file.name)) {
            this.$message({
              type: 'error',
              message: '文件名称错误，请严格按照格式上传'
            })
            return false
          }
        }
      }
      // 校检文件类型
      if (this.fileType.length) {
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
          this.$modal.msgError(`${this.$t(`oss['文件格式不正确, 请上传']`)}${this.fileType.join("/")}${this.$t(`oss['格式文件']`)}!`);
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`${this.$t(`oss['上传文件大小不能超过']`)} ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading(`${this.$t(`oss['正在上传文件，请稍候']`)}...`);
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`${this.$t(`oss['上传文件数量不能超过']`)} ${this.limit}!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError(this.$t(`oss['上传文件失败，请重试']`));
      this.$modal.closeLoading()
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (this.type == 1 || this.type == 3) { // 上传设备升级文件的回调
        if (res.code !== 200) {
          this.number--;
          this.$modal.closeLoading();
          this.$modal.msgError(res.msg);
          this.$refs.fileUpload.handleRemove(file);
        } else {
          this.uploadList.push({ name: res.url, url: res.url });
          this.uploadedSuccessfully();
          this.$modal.closeLoading();
          this.$emit("upload", { filePath: res.url, fileSize: file.size / 1024 / 1024, fileName: file.name });
        }
      } else {
        if (res.code === 200) {
          this.uploadList.push({ name: res.fileName, url: res.fileName });
          this.uploadedSuccessfully();
          this.$emit("upload", { filePath: res.fileName, fileSize: file.size, fileName: file.originalFilename });
        } else {
          this.number--;
          this.$modal.closeLoading();
          this.$modal.msgError(res.msg);
          this.$refs.fileUpload.handleRemove(file);
          this.uploadedSuccessfully();
        }
      }
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1);
      this.$emit("input", this.listToString(this.fileList));
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.$emit("input", this.listToString(this.fileList));
        this.$modal.closeLoading();
      }
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return "";
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].url + separator;
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : '';
    },
    // 自定义上传
    handleHttpRequest(formData) {
      let fd = new FormData();
      if (formData.data) {
        Object.keys(formData.data).forEach(key => {
          if (key == 'type' && formData.data[key] == 6) {
            fd.append('type', 8);
          } else {
            fd.append(key, formData.data[key]);
          }
        });
      }
      fd.append("file", formData.file);

      request({
        url: formData.action.split(process.env.VUE_APP_BASE_API)[1],
        method: 'post',
        data: fd,
        timeout: 60000,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        formData.onSuccess(res, formData.file)
      }).catch(err => {
        formData.onError(err, formData.file)
      })
    }
  }
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  /* border: 1px solid #e4e7ed; */
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
