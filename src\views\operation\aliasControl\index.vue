<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-05-29 09:02:25
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-07 16:14:18
 * @FilePath: \elecloud_platform-main\src\views\operation\aliasControl\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="page-box elevation-4">
    <el-tabs v-model="activeName">
      <el-tab-pane :label="$t('测点别名')" name="first"></el-tab-pane>
      <el-tab-pane :label="$t('测点绑定')" name="second"></el-tab-pane>
    </el-tabs>
    <div class="box-cont">
      <DeviceBindAlias v-if="activeName == 'second'" />
      <Alias v-if="activeName == 'first'" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Alias from '../alias/index.vue'
import DeviceBindAlias from '../deviceBindAlias/index.vue'

const activeName = ref('first')
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  overflow: hidden;

  &-cont {
    height: calc(100% - 80px);
  }
}
</style>
