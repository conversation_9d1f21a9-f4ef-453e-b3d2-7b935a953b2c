<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';
import resize from './mixins/resize'

import { isShow3502Bit10 } from '@/utils/parseBinaryToText'
import { isPhotovoltaicFn, isGirdFn, isCellFn, isPowerFn, isBusFn2 } from '@/hook/useDeviceType'
import { emTypeOptions } from '@/constant'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '98%'
    },
    height: {
      type: String,
      default: '95%'
    }
  },
  data() {
    return {
    }
  },
  computed: {
    // 是否显示发电机
    isShowDiesel() {
      let sts = this.$store.state.monitor.pcs_sts
      if (sts.length) return isShow3502Bit10(sts[0]['sts_3502'])
      return false
    },
    // deviceType RT09 是否显示柴油机
    isShowDieselRT09() {
      let baseInfo = this.$store.state.monitor.baseInfo
      return baseInfo.showDiesel
    },
    isEmTypePCC() {
      let data = this.$store.state.monitor.pcs_ele
      return (this.$route.query.type == 7 || this.$route.query.type == 6) && data.some(item => {
        return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
      })
    }
  },
  watch: {
    '$store.state.monitor.powerLineData.times': {
      deep: true,
      handler(newValue, oldValue) {
        if (this.$store.state.monitor.powerLineData.times) this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      if (this.chart) this.chart.dispose()
      this.chart = echarts.init(this.$el)
      this.chart.clear();
      let options = {
        title: {
          subtext: `${this.$t('common.unit')}：kW`,
          left: '2%',
        },
        xAxis: {
          data: this.$store.state.monitor.powerLineData.times,
          // boundaryGap: true,
          axisTick: {
            show: false
          }
        },
        dataZoom: [
          {
            type: 'inside',
          }
        ],
        grid: {
          left: '2%',
          right: '2%',
          bottom: 10,
          top: 45,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10],
          formatter: (params) => {
            let groupData = this.$store.state.monitor.powerGroupLineData.groupData
            let htmlStart = `<div><div>${params[0].name}</div>`
            params.forEach((param, index) => {
              if (param.seriesName.indexOf('SOC') !== -1) {
                htmlStart += `
                <div style="display: flex;align-items: center;">
                  <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                  ${param.seriesName}：
                  <span style="font-weight: 600;margin-right: 3px;">${param.data !== null ? param.data : '--'}</span> %
                </div>`
              } else {
                if (this.isEmTypePCC && param.seriesName === this.$t('monitor.flowItem3')) {
                  let data = null
                  let powerLineData = this.$store.state.monitor.powerLineData
                  data = powerLineData.jk103133.map((item, index) => {
                    let em_10012s = powerLineData.em_10012s[index]
                    return (item - em_10012s).toFixed(2)
                  })
                  htmlStart += `
                  <div style="display: flex;align-items: center;">
                    <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                    ${param.seriesName}：
                    <span style="font-weight: 600;margin-right: 3px;">${data[param.dataIndex] !== null ? data[param.dataIndex] : '--'}</span> kW
                  </div>`
                } else {
                  htmlStart += `
                  <div style="display: flex;align-items: center;">
                    <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                    ${param.seriesName}：
                    <span style="font-weight: 600;margin-right: 3px;">${param.data !== null ? param.data : '--'}</span> kW
                  </div>`
                }
              }
            })
            return htmlStart + '</div>'
          }
        },
        yAxis: {
          type: 'value',
          splitLine: { show: false },
          minInterval: 5
        },
        legend: {
          icon: 'circle',
          type: 'scroll'
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: '',
            textAlign: 'center',
            fill: '#000',
            width: 30,
            height: 30,
            fontSize: 16
          }
        },
        series: [],
        color: [
          '#2ec7c9',
          '#b6a2de',
          '#5ab1ef',
          '#ffb980',
          '#d87a80',
          '#8d98b3',
        ]
      }
      let type = this.$route.query.type
      // 电网功率
      if (isGirdFn(type)) {
        let data = null
        if (this.isEmTypePCC) {
          data = this.$store.state.monitor.powerLineData.em_10012s
        } else {
          data = this.$store.state.monitor.powerLineData.powers
        }
        options.series.push({
          name: this.isShowDiesel ? this.$t('发电机功率') : this.$t('monitor.flowItem1'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data
        })
      }
      // 电池功率
      if (isCellFn(type)) {
        options.series.push({
          name: this.$t('monitor.flowItem4'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data: this.$store.state.monitor.powerLineData.cells
        })
      }
      // 负载功率
      if (isPowerFn(type) || this.isEmTypePCC) {
        let data = null
        let powerLineData = this.$store.state.monitor.powerLineData
        if (this.isEmTypePCC) {
          data = powerLineData.jk103133.map((item, index) => {
            let em_10012s = powerLineData.em_10012s[index]
            return (item - em_10012s).toFixed(2)
          })
        } else {
          data = powerLineData.loads
        }
        options.series.push({
          name: this.$t('monitor.flowItem3'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data
        })
      }
      // 光伏功率
      if (isPhotovoltaicFn(type)) {
        options.series.push({
          name: this.$t('monitor.flowItem2'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data: this.$store.state.monitor.powerLineData.photovoltaics
        })
      }
      // 直流母线功率
      if (isBusFn2(type)) {
        options.series.push({
          name: this.$t('monitor.flowItem5'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data: this.$store.state.monitor.powerLineData.bus
        })
      }
      // 充电桩、soc
      let data = this.$store.state.monitor.powerLineData.chargingPile ?? []
      let socData = this.$store.state.monitor.powerLineData.soc ?? []
      let chargingPileDc = new Set()
      let socDet = new Set()
      data.forEach(item => {
        if (item.length) item.forEach(item1 => chargingPileDc.add(item1.dc))
      })
      socData.forEach(item => {
        if (item) Object.keys(item).forEach(item1 => socDet.add(item1))
      })
      if (Array.from(chargingPileDc).length) {
        Array.from(chargingPileDc).forEach((item) => {
          options.series.push({
            name: `${parseInt(item) - 191000 + 1}#${this.$t('充电桩')}${this.$t(`price['功率']`)}`,
            data: data.map(item1 => {
              if (item1.length) {
                if (item1.findIndex(item2 => item2.dc == item) !== -1) {
                  return _.round(item1.find(item2 => item2.dc == item).chargingPile_19003, 2)
                } else {
                  return null
                }
              } return null
            }),
            smooth: true,
            type: 'line',
            symbol: 'none'
          })
        })
      }
      if (Array.from(socDet).length) {
        Array.from(socDet).forEach(item => {
          options.series.push({
            name: item.substring(0, 2) == '16' ? `${parseInt(item) - 161000 + 1}#SOC`: 'SOC',
            data: socData.map(item1 => {
              if (item1) {
                // console.log(item1, item);
                if (item1[item] !== null) {
                  return _.round(item1[item], 2)
                } else {
                  return null
                }
              } return null
            }),
            smooth: true,
            type: 'line',
            symbol: 'none'
          })
        })
      }
      if (type == 13) {
        options.series.push({
          name: this.$t('直流源功率'),
          smooth: true,
          type: 'line',
          symbol: 'none',
          data: this.$store.state.monitor.powerLineData.cells
        })
      }
      options && this.chart.setOption(options)
    }
  }
}
</script>
