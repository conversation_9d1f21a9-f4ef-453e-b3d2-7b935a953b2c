import request from '@/utils/request'

// 获取列表
export function jfpgList(queryInfo) {
  return request({
    url: '/system/cutTop/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addJfpg(data) {
  return request({
    url: '/system/cutTop',
    method: 'post',
    data
  })
}

// 修改
export function editJfpg(data) {
  return request({
    url: '/system/cutTop',
    method: 'put',
    data
  })
}

// 获取全部数据
export function allJfpg() {
  return request({
    url: '/system/cutTop/getCutTops',
    method: 'get'
  })
}

// 删除
export function deleteJfpg(queryInfo) {
  return request({
    url: `/system/cutTop/${queryInfo.cutTopIds}`,
    method: 'delete'
  })
}
