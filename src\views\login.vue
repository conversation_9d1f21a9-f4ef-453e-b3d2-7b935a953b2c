<template>
  <div class="login">
    <div class="login-top">
      <!-- logo -->
      <div>
        <img v-if="domainInfo.logo" :src="domainInfo.logo" alt="logo" :style="{ width: domainInfo.width }" />
      </div>
      <!-- 国际化 -->
      <div class="login-top-ri">
        <template v-if="isShow">
          <el-popover placement="bottom" trigger="hover">
            <div style="display: flex;justify-content: center;flex-direction: column;align-items: center;">
              <img :src="appBase64" style="width: 100px;height: 100px;margin-bottom: 10px" />
              <span>{{ $t('扫码下载APP') }}</span>
            </div>
            <div style="display: flex;margin-right: 6px;color: #5a5e66;" slot="reference">
              <svg-icon class-name="international-icon" icon-class="cellphone" style="width: 20px;height: 22px;" />
              <span>{{ $t('下载APP') }}</span>
            </div>
          </el-popover>
          <manualSelect class="lang"></manualSelect>
        </template>
        <lang-select class="lang"></lang-select>
      </div>
    </div>
    <!-- center -->
    <img src="~assets/images/center.png" alt="logo" class="center">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form elevation-6">
      <h2 class="title">{{ domainInfo.title }}</h2>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" :placeholder="$t('login.username')">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" show-password auto-complete="off"
          :placeholder="$t('login.password')" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <!-- <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          :placeholder="$t('login.code')"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item> -->
      <div class="login-oper" :style="{ margin: `0 0 ${$convertPx(25, 'rem')} 0` }">
        <el-checkbox v-model="loginForm.rememberMe">{{ $t('login.rememberPwd') }}</el-checkbox>
        <div class="login-oper-forget" @click="$router.push('forgetPwd')">{{ $t('忘记密码？') }}</div>
      </div>
      <el-form-item style="width:100%;">
        <el-button :loading="loading" type="primary"
          :style="{ width: '100%', borderRadius: $convertPx(30, 'rem'), height: $convertPx(50, 'rem') }"
          @click.native.prevent="handleLogin">
          <span v-if="!loading">{{ $t('login.logIn') }}</span>
          <span v-else>{{ $t('login.loging') }}...</span>
        </el-button>
        <div style="display: flex;justify-content: center;margin-top: 10px;">
          <el-link type="primary" href="https://11.elecod-cloud.com/" style="height: 36px;">{{ $t('前往旧平台') }}</el-link>
        </div>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <div>
        <span>Copyright © 2023 {{ $t('login.company') }}</span>
        &emsp;&emsp;
        <span @click="cimsInputClick()" class="put">粤ICP备2023118278号-2</span>
      </div>
    </div> -->
  </div>
</template>

<script>
import LangSelect from '@/components/LangSelect'
import manualSelect from '@/components/manualSelect'
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  components: { LangSelect, manualSelect },
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: this.$t('login.vUserName') }
        ],
        password: [
          { required: true, trigger: "blur", message: this.$t('login.vPassword') }
        ],
        code: [{ required: true, trigger: "change", message: this.$t('login.vCode') }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined,
      logo: '',
      title: '',
      appBase64: 'data:image/image/png;base64,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',
      isShow: false
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    // this.getCode();
    this.getCookie();
    this.getOwnDomain()
  },
  computed: {
    domainInfo() {
      const origin = window.location.origin
      let data = this.$store.state.common.domainInfo
      let title = Cookies.get("language") == 'zh' ? data.title : data.titleUs
      if (data) return {
        title,
        logo: data.homeLogoPath ? `${origin}/prod-api${data.homeLogoPath}` : null,
        width: data.logoWidth ? data.logoWidth + 'px': '130px',
        height: data.logoHeight ? data.logoHeight + 'px': 'auto',
      }
      return {
        title: this.$t('欢迎'),
        logo: null,
        width: '130px',
        height: 'auto',
      }
    }
  },
  methods: {
    cimsInputClick() {
      window.open('https://beian.miit.gov.cn/#/Integrated/index', '_blank')
    },
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      const domainName = window.location.hostname;
      const origin = window.location.origin
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: '/' }).catch(() => { });
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              // this.getCode();
            }
          });
        }
      });
    },
    // 操作手册
    handleOperClick() {
      window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/doc.pdf', '_blank')
    },
    getOwnDomain() {
      const origin = window.location.hostname
      const srcOptions =
        ['11.elecod-cloud.com', 'www.elecod-cloud.com', 'global.elecod-cloud.com', 'localhost',]
      this.isShow = srcOptions.findIndex(item => item === origin) != -1
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/back.png");
  background-size: cover;


  /* background-size: 100% 100%; */
  &-top-ri {
    display: flex;
    align-items: center
  }


  &-oper {
    display: flex;
    justify-content: space-between;


    &-forget {
      color: #909399;
      cursor: pointer;
      font-size: 14px;
    }
  }
}


.title {
  margin: 0px auto 40px auto;
  text-align: center;
  color: var(--base-color);
}

.logo {
  width: 130px;
}

.center {
  width: 800px;
  margin-bottom: 40px;
  margin-right: 250px;
}

.login-top {
  position: absolute;
  top: 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
}

.login-form {
  border-radius: 16px;
  background: #fff;
  width: 400px;
  padding: 40px 41px 5px 41px;
  border: 1px solid #E4E7ED;
  width: 459px;
  height: 425px;
  box-shadow: 2px 2px 20px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;

  .el-input {
    height: 50px;


    input {
      height: 50px;
    }
  }


  .input-icon {
    height: 51px;
    width: 14px;
    margin-left: 2px;
  }


  .el-form-item {
    margin-bottom: 30px;
  }


  .el-input__prefix {
    left: 21px;
  }


  .el-input--prefix .el-input__inner {
    padding-left: 47px
  }
}


.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}


.login-code {
  width: 33%;
  height: 49px;
  float: right;


  img {
    cursor: pointer;
    vertical-align: middle;
    width: 100%;
  }
}


.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 10px;
  width: 100%;
  text-align: center;
  color: var(--base-color);
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;


  .put {
    cursor: pointer;
  }


  .put:hover {
    color: #46a6ff
  }
}


.login-code-img {
  height: 49px;
}


.lang {
  background: #fff;
  font-size: 16px !important;
  margin-left: 10px;
  cursor: pointer;
  color: var(--base-color);

  .lang-text {
    font-size: 16px !important;
  }

  .lang-select {
    font-size: 16px !important;
  }
}

@media (max-width: 1500px) {
  .login {
    flex-direction: column-reverse;
    align-items: center;
  }
  .center {
    display: none;
  }
}
</style>
