<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-01-03 17:56:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-07-09 16:00:17
 * @FilePath: \elecloud_platform-main\src\views\help.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="help app-container">
    <div><el-link type="primary" @click="handleClick(1)">{{ $t('云平台PC端操作手册') }}</el-link></div>
    <div><el-link type="primary" @click="handleClick(2)">{{ $t('云平台PC端英文版操作手册') }}</el-link></div>
    <div><el-link type="primary" @click="handleClick(3)">{{ $t('云平台常见问题') }}</el-link></div>
    <div><el-link type="primary" @click="handleClick(4)">{{ $t('云平台英文版常见问题') }}</el-link></div>
    <div><el-link type="primary" @click="handleClick(5)">{{ $t('云平台APP操作手册') }}</el-link></div>
    <div><el-link type="primary" @click="handleClick(6)">{{ $t('云平台APP英文版操作手册') }}</el-link></div>
  </div>
</template>

<script setup>
const handleClick = (type) => {
  switch (type) {
    case 1:
      window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/doc.pdf', '_blank')
      break;
    case 2:
      window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/docEn.pdf', '_blank')
      break;
    case 3:
      window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/FAQ.pdf', '_blank')
      break;
    case 4:
      window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/FAQEn.pdf', '_blank')
      break;
    case 5:
      window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/APP.pdf', '_blank')
      break;
    case 6:
      window.open('https://11.elecod-cloud.com/prod-api/profile/avatar/2024/02/06/APPEn.pdf', '_blank')
      break;
  }
}
</script>

<style scoped lang="scss">
.el-link {
  font-size: 16px;
}
</style>

