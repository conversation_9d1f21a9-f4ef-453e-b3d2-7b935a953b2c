<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-12 17:36:31
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="param">
    <el-tree :data="dataCom" @node-click="handleNodeClick" node-key="label" class="tree"
      :current-node-key="currentNodeKey" ref="treeRef">
      <div class="tree-title" slot-scope="{ node }">
        <div>{{ node.label }}</div>
      </div>
    </el-tree>
    <System v-if="currentNodeKey == $t(`log['策略类参数设置']`)" />
    <MAC v-if="currentNodeKey == $t(`param['MAC参数设置']`)" />
    <MDC v-if="currentNodeKey == $t(`param['MDC参数设置']`)" />
    <BMS v-if="currentNodeKey == $t(`param['电池参数设置']`)" />
    <Upgrade v-if="currentNodeKey == $t(`param['在线升级']`)"></Upgrade>
    <OnOff v-if="currentNodeKey == $t(`log['系统开关机']`)"></OnOff>
    <IoOnOff v-if="showIoAlias" :currentIoAlias="currentIoAlias"></IoOnOff>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, nextTick } from 'vue'
import { useStore, useRoute } from '@/utils/vueApi.js'
import { checkRole, checkPermi } from '@/utils/permission'

import MAC from './param/MAC.vue'
import MDC from './param/MDC.vue'
import BMS from './param/BMS.vue'
import System from './param/System.vue'
import Upgrade from './param/upgrade.vue'
import OnOff from './param/OnOff.vue'
import IoOnOff from './param/IoOnOff.vue'

const { proxy } = getCurrentInstance()

const store = useStore()
const route = useRoute()

const currentKey = ref(proxy.$t(`log['策略类参数设置']`))
store.commit('SET_PARAMCURRENTKEY', proxy.$t(`log['策略类参数设置']`))
const currentNodeKey = computed({
  get: function () {
    if (store.state.monitor.paramCurrentKey) {
      return store.state.monitor.paramCurrentKey
    }
    return currentKey.value
  },
  set: function (newValue) {
    currentKey.value = newValue
  }
})

const handleNodeClick = (node) => {
  store.commit('SET_PARAMCURRENTKEY', node.label)
  currentNodeKey.value = node.label
  if (showIoAlias.value) {
    currentIoAlias.value = node
  }
}

const dataCom = computed(() => {
  let bms = store.state.monitor.pcs_bms
  let bmsBau = store.state.monitor.pcs_bmsBau
  let ac = store.state.monitor.pcs_ac
  let dc = store.state.monitor.pcs_dc
  let io = store.state.monitor.pcs_io
  // let io = [{
  //   peripherals_18001: 1,
  //   peripherals_18002: 1,
  //   peripherals_18003: 1,
  //   peripherals_18004: 1,
  //   peripherals_18005: 1,
  //   peripherals_18006: 1,
  // }]
  let aliasArr = store.state.monitor.aliasArr
  let data = [{
    label: proxy.$t(`log['策略类参数设置']`)
  }, {
    label: proxy.$t(`log['系统开关机']`)
  }]
  if (ac.length) data.push({ label: proxy.$t(`param['MAC参数设置']`) })
  if (bms.length || bmsBau.length) data.push({ label: proxy.$t(`param['电池参数设置']`) })
  if (dc.length) data.push({ label: proxy.$t(`param['MDC参数设置']`) })
  if (checkPermi(['system:sendMqtt:upgradeJson']) || (isShowV74280.value && checkPermi(['system:sendMqtt:BMSupgradeJson']))) {
    data.push({
      label: proxy.$t(`param['在线升级']`)
    })
  }
  // 别名
  if (io.length) {
    const set = new Set()
    io.forEach(item => {
      for (let i = 1; i < 11; i++) {
        if (item[`peripherals_180${i < 10 ? '0' + i : i}`]) {
          let value = aliasArr.find(item => item.point == `181${(i - 1) < 10 ? '0' + (i - 1) : (i - 1)}`)
          if (value) {
            set.add({ label: value.alias, point: value.point, pointId: value.pointId })
          }
        }
      }
    })
    data = [...data, ...set]
  }

  if (store.state.monitor.paramCurrentKey) {
    nextTick(() => {
      proxy.$refs?.treeRef?.setCurrentKey(store.state.monitor.paramCurrentKey)
    })
  }

  return data
})

// 显示别名开关
const showIoAlias = computed(() =>
  currentNodeKey.value !== proxy.$t(`log['策略类参数设置']`)
  && currentNodeKey.value !== proxy.$t(`param['在线升级']`)
  && currentNodeKey.value !== proxy.$t(`log['系统开关机']`)
  && currentNodeKey.value !== proxy.$t(`param['MAC参数设置']`)
  && currentNodeKey.value !== proxy.$t(`param['MDC参数设置']`)
  && currentNodeKey.value !== proxy.$t(`param['电池参数设置']`))
const currentIoAlias = ref()

/**
 * 是否显示V74280
 * BMS升级
 */
 const isShowV74280 = computed(() => {
  let versionStart = store.state.monitor.control?.jk_1000?.split('V')[1].split('.')[0]
  let versionTwo = store.state.monitor.control?.jk_1000?.split('V')[1].split('.')[1]
  let versionThere = store.state.monitor.control?.jk_1000?.split('V')[1].split('.')[2]
  if (versionStart == 7) if (versionTwo == 4280) return true
  return false
})

</script>

<style lang="scss" scoped>
:deep(.tree) {
  height: 100%;
  border-right: 1px solid #D6D6D6;
  flex: 1;

  .tree-title {
    padding: 10px;
  }

  .el-tree-node__content {
    height: auto;
  }

  .is-current {
    font-weight: bold;
    color: var(--base-color);
    background-color: #f6f6f6;
  }
  .el-tree-node__content:hover,
  .el-upload-list__item:hover {
    background-color: #f6f6f6;
  }
}


.param {
  height: 800px;
  display: flex;
}

.param-box {
  padding: 10px 20px 20px 20px;
  flex: 5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.param-title {
  margin-bottom: 20px;
  font-weight: bold;
}
</style>
