<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '95%'
    },
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    '$store.state.monitor.electricData.times': {
      deep: true,
      handler(newValue, oldValue) {
        if (this.$store.state.monitor.electricData.times) this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      if (this.chart) this.chart.dispose()
      this.chart = echarts.init(this.$el)
      this.chart.clear();
      let options = {
          title: {
            subtext: `${this.$t('common.unit')}：kWh`
          },
          xAxis: {
            data: this.$store.state.monitor.electricData.times,
            axisTick: {
              show: false
            }
          },
          grid: {
            left: '2%',
            right: '2%',
            bottom: '1%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            padding: [5, 10]
          },
          yAxis: {
            type: 'value',
            splitLine: { show: false },
          },
          legend: {
            // data: [this.$t('monitor.barItem1'), this.$t('monitor.barItem2')],
            top:'5%',
          },
        series: [],
        color: [
          '#2ec7c9',
          '#b6a2de',
          '#5ab1ef',
          '#ffb980',
          '#d87a80',
          '#8d98b3',
        ]
      }
      let type = this.$route.query.type
      // 光伏发电量
      if (type == 10000 || type == 10001) {
        options.series.push({
          name: this.$t('monitor.topItem7'),
            itemStyle: {  // 使用方法二的写法
              // color: {
              //   type: 'linear',
              //   x: 0,  //右
              //   y: 0,  //下
              //   x2: 0,  //左
              //   y2: 1,  //上
              //   colorStops: [
              //     {
              //       offset: 0,
              //       color: '#0DB0F5'
              //     },
              //     {
              //       offset: 0.5,
              //       color: '#2499EE'
              //     },
              //     {
              //       offset: 1,
              //       color: '#8477FF'
              //     }
              //   ]
              // }
            },
            smooth: true,
            type: 'bar',
            barWidth: 10,
            data: this.$store.state.monitor.electricData.photovoltaicPowerCapacityCalculate,
            animationDuration: 2800,
            animationEasing: 'quadraticOut'
        })
      }
      // if (type == 1 || type == 2 || type == 3 || type == 5 || type == 6 || type == 7 || type == 8 || type == 9 || type == 10) {
        options.series.push(
          {
            name: this.$t('monitor.barItem1'),
            barGap: 0,
            smooth: true,
            type: 'bar',
            barWidth: 10,
            data: this.$store.state.monitor.electricData.chargeCapacityCalculate,
            animationDuration: 2800,
            animationEasing: 'cubicInOut'
          },
          {
            name: this.$t('monitor.barItem2'),
            smooth: true,
            type: 'bar',
            barWidth: 10,
            data: this.$store.state.monitor.electricData.dischargeCapacityCalculate,
            animationDuration: 2800,
            animationEasing: 'quadraticOut'
            },
        )
      // }
      options && this.chart.setOption(options)
    }
  }
}
</script>
