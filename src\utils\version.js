
function compareVersions(version1, version2) {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;

    if (v1Part < v2Part) return -1; // v1 在 v2 之前
    if (v1Part > v2Part) return 1;  // v1 在 v2 之后
  }

  return 0; // 版本相同
}

function isVersionInRange(targetVersion, lowerBound, upperBound) {
  return compareVersions(targetVersion, lowerBound) >= 0 && compareVersions(targetVersion, upperBound) < 0;
}
function isVersionOrBefore(targetVersion, comparedVersion) {
  return compareVersions(targetVersion, comparedVersion) <= 0;
}
function isVersionOrAfter(targetVersion, comparedVersion) {
  return compareVersions(targetVersion, comparedVersion) >= 0;
}


// 使用示例
const currentVersion = '3.4.5';
const specifiedVersion1 = '3.4.5';
const specifiedVersion2 = '3.4.0';

// console.log('当前版本是否在', specifiedVersion1, '之后:', compareVersions(currentVersion, specifiedVersion1) > 0);
// console.log('当前版本是否在', specifiedVersion1, '之后:', isVersionOrAfter(currentVersion, specifiedVersion1) > 0);
// console.log('当前版本是否在', specifiedVersion2, '之前:', compareVersions(currentVersion, specifiedVersion2) < 0);
// console.log('当前版本是否在', specifiedVersion1, '和', specifiedVersion2, '之间:', isVersionInRange(currentVersion, specifiedVersion1, specifiedVersion2));

const typeArr = ['next', 'prev', 'between', '']
export const checkVersion = (type, currentVersion, version1, version2) => {
  if (typeArr.findIndex(item => item == type) == -1) {
    console.error(`type只能是['next', 'prev', 'between']其中一个`)
    return false
  }
  if (type == 'next') {
    // return compareVersions(currentVersion, version1) > 0
    return isVersionOrAfter(currentVersion, version1) > 0
  } else if (type == 'prev') {
    // return compareVersions(currentVersion, version2) < 0
    return isVersionOrBefore(currentVersion, version2) < 0
  } else if (type == 'between') {
    return isVersionInRange(currentVersion, version1, version2)
  }
}
