<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-03-12 18:02:24
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <div v-for="(ioItem, index) in io" :key="ioItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ $t('外设') }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot"
        style="margin-bottom: 20px">
        <el-descriptions-item :label="$t('monitor.onLineState')">
          <span>{{ getStatus(index) }}</span>
        </el-descriptions-item>
        <template v-for="i in 10">
          <el-descriptions-item :label="ioItem[`180${i < 10 ? '0' + i : i}_alias`]" v-if="ioItem[`180${i < 10 ? '0' + i : i}_alias`] != $t('未知别名')" :key="i">
            <span v-if="ioItem[`peripherals_180${i < 10 ? '0' + i : i}`]">{{ ioItem[`peripherals_180${i < 10 ? '0' + i : i}`] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
            <span v-else>--</span>
          </el-descriptions-item>
        </template>
        <!-- <el-descriptions-item :label="ioItem['18001_alias']" v-if="ioItem['18001_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18001']">{{ ioItem['peripherals_18001'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18002_alias']" v-if="ioItem['18002_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18002']">{{ ioItem['peripherals_18002'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18003_alias']" v-if="ioItem['18003_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18003']">{{ ioItem['peripherals_18003'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18004_alias']" v-if="ioItem['18004_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18004']">{{ ioItem['peripherals_18004'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18005_alias']" v-if="ioItem['18005_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18005']">{{ ioItem['peripherals_18005'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18006_alias']" v-if="ioItem['18006_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18006']">{{ ioItem['peripherals_18006'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18007_alias']" v-if="ioItem['18007_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18007']">{{ ioItem['peripherals_18007'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18008_alias']" v-if="ioItem['18008_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18008']">{{ ioItem['peripherals_18008'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18009_alias']" v-if="ioItem['18009_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18009']">{{ ioItem['peripherals_18009'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="ioItem['18010_alias']" v-if="ioItem['18010_alias'] != '未知别名'">
          <span v-if="ioItem['peripherals_18010']">{{ ioItem['peripherals_18010'] == 1 ? $t('common.Closure2'): $t('common.switchOff') }}</span>
          <span v-else>--</span>
        </el-descriptions-item> -->
      </el-descriptions>
    </div>
  </div>
</template>
<script>
export default {
  name: "device",
  data() {
    return {
    };
  },
  computed: {
    io() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let data = this.$store.state.monitor.groupList[currentNodeKey]
      if (!data || Object.keys(data).length != 11) return []
      data.io.forEach(item => {
        for (let i = 1; i < 11; i++) {
          let value = this.aliasArr.find(item => item.point == `180${i < 10 ? '0' + i : i}`)
          if (value) {
            item[`180${i < 10 ? '0' + i : i}_alias`] = value.alias
          } else {
            item[`180${i < 10 ? '0' + i : i}_alias`] = this.$t('未知别名')
          }
        }
      })
      return data.io
    },
    // 别名
    aliasArr() {
      let data =  this.$store.state.monitor.aliasArr
      return data
    },
    getStatus() {
      return (index) => {
        if (this.io[index].isAnalysis == 0) {
          return this.io.onLineState == '在线' ? this.$t('common.online') : this.$t('common.offline')
        } else if (this.io[index].isAnalysis == 1) {
          return this.io[index]['peripherals_18000'] == '1' ? this.$t('alarm.title'): this.$t('common.normal')
        } else {
          return '--'
        }
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;

  .el-button {
    float: right;
    margin-bottom: 20px;
  }

  .el_box {
    border-top: 1px solid #BFBFBF;
    border-left: 1px solid #BFBFBF;
    margin-bottom: 30px;

    .el-col {
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #BFBFBF;
      border-right: 1px solid #BFBFBF;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .el-col::after {
      content: attr(data-label);
      display: none;
    }

    .el-col:hover {
      overflow: visible;
      text-overflow: clip;
    }

    .left {
      text-align: left;
      padding: 0 10px 0 10px;
    }

    .right {
      text-align: right;
      padding: 0 10px 0 10px;
    }
  }

}
</style>
