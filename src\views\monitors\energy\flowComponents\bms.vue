<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-29 16:20:37
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-29 17:31:04
 * @FilePath: \elecloud_platform-main\src\views\monitors\energy\flowComponents\bms.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="battery-wrapper">
    <div class="battery">
    <div class="battery-top-box">
      <div class="battery-dot"></div>
      <div class="battery-dot"></div>
    </div>
    <div class="battery-box">
      <div class="battery-box-text">{{ props.value }}%</div>
      <div class="battery-box-value" :style="{height: batteryHeight}"></div>
    </div>
  </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";

const props = defineProps({
  value: {
    type: String,
    default: '20',
  },
});

const batteryHeight = computed(() => {
  return 23 / 100 * Number(props.value) + 'px'
})
</script>

<style lang="scss" scoped>
.battery-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70px;
  height: 70px;
  border-radius: 100%;
  background-color: #fff;
  border: 4.5px solid var(--primary-color);
}
.battery {
  width: 40px;
  height: 35px;
  &-top-box {
    display: flex;
    justify-content: space-evenly;
  }
  &-dot {
    width: 5px;
    height: 2px;
    background-color: var(--primary-color);
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
  }
  &-box {
    width: 100%;
    height: 31px;
    border: 2px solid var(--primary-color);
    display: flex;
    flex-direction: column-reverse;
    border-radius: 4px;
    padding: 2px;
    background-color: #fff;
    position: relative;
    &-value {
      background-color: var(--primary-color);
      width: 100%;
    }
    &-text {
      width: 100%;
      font-size: 12px;
      line-height: 23px;
      position: absolute;
      text-align: center;
    }
  }
}
</style>
