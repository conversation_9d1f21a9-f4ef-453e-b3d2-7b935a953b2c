<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-18 14:29:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-09-02 09:18:24
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="param-box">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="auto" style="overflow: auto">
      <el-form-item :label="`${$t(`param['下发状态']`)}`" prop="status" style="width: 50%">
        <span slot="label">
          {{ $t(`param['下发状态']`) }}
          <el-tooltip class="item" effect="dark" placement="bottom">
            <div slot="content" style="line-height: 1.5">
              &emsp;{{ $t(`param['未下发']`) }}：{{ $t(`param['该类参数从未下发']`) }};
              <br />
              &emsp;{{ $t(`param['下发中']`) }}：{{ $t(`param['参数已成功下发至设备，执行未知，请等待']`) }};
              <br />
              {{ $t(`param['下发成功']`) }}：{{ $t(`param['参数已成功下发至设备并已执行成功']`) }};
              <br />
              {{ $t(`param['下发失败']`) }}：{{ $t(`param['参数已成功下发至设备，设备并未执行成功']`) }}
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <el-link :underline="false" v-if="form.status == 0">{{ $t(`param['未下发']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 1" type="success">{{ $t(`param['下发成功']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 2" type="primary">{{ $t(`param['下发中']`) }}</el-link>
        <el-link :underline="false" v-if="form.status == 3" type="danger">{{ $t(`param['下发失败']`) }}</el-link>
      </el-form-item>
      <el-form-item :label="`${$t(`param['SOC上限设置']`)}`" prop="setting1930" style="width: 50%">
         <span slot="label">
          {{ $t(`param['SOC上限设置']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('设置电池停止充电时SOC。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1930" :placeholder="'请输入'">
          <span slot="suffix">（5~100）%</span>
        </el-input> -->
        <el-input-number v-model="form.setting1930" :precision="1" :step="0.1" :max="100" :min="5"
          :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（5~100）%</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['SOC下限设置']`)}`" prop="setting1931" style="width: 50%">
         <span slot="label">
          {{ $t(`param['SOC下限设置']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('设置电池停止放电时SOC。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1931" :placeholder="'请输入'">
          <span slot="suffix">（0~95）%</span>
        </el-input> -->
        <el-input-number v-model="form.setting1931" :precision="1" :step="0.1" :max="95" :min="0"
          :placeholder="$t(`common['Please enter']`)"></el-input-number>
        <span class="suffix">（0~95）%</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['充电限流值']`)}`" prop="setting1932" style="width: 50%">
        <span slot="label">
          {{ $t(`param['充电限流值']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('设置电池充电时的电流最大值。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1932" :placeholder="'请输入'">
          <span slot="suffix">（0~2000）A</span>
        </el-input> -->
        <el-input-number v-model="form.setting1932" :precision="1" :step="0.1" :max="2000" :min="0"
          :placeholder="$t(`common['Please enter']`)"></el-input-number>
        <span class="suffix">（0~2000）A</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['放电限流值']`)}`" prop="setting1933" style="width: 50%">
        <span slot="label">
          {{ $t(`param['放电限流值']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('设置电池放电时的电流最大值。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1933" :placeholder="'请输入'">
          <span slot="suffix">（0~2000）A</span>
        </el-input> -->
        <el-input-number v-model="form.setting1933" :precision="1" :step="0.1" :max="2000" :min="0"
          :placeholder="$t(`common['Please enter']`)"></el-input-number>
        <span class="suffix">（0~2000）A</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['欠压保护']`)}`" prop="setting1934" style="width: 50%">
        <span slot="label">
          {{ $t(`param['欠压保护']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('电池放电保护电压。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1934" :placeholder="'请输入'">
          <span slot="suffix">（50~1000）V</span>
        </el-input> -->
        <el-input-number v-model="form.setting1934" :precision="1" :step="0.1" :max="1000" :min="50"
          :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（50~1000）V</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['欠压恢复']`)}`" prop="setting1935" style="width: 50%">
        <span slot="label">
          {{ $t(`param['欠压恢复']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('电池可放电恢复电压。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1935" :placeholder="'请输入'">
          <span slot="suffix">（50~1000）V</span>
        </el-input> -->
        <el-input-number v-model="form.setting1935" :precision="1" :step="0.1" :max="1000" :min="50"
          :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（50~1000）V</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['过压保护']`)}`" prop="setting1936" style="width: 50%">
        <span slot="label">
          {{ $t(`param['过压保护']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('电池充电电保护电压。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1936" :placeholder="'请输入'">
          <span slot="suffix">（50~1000）V</span>
        </el-input> -->
        <el-input-number v-model="form.setting1936" :precision="1" :step="0.1" :max="1000" :min="50"
          :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（50~1000）V</span>
      </el-form-item>
      <el-form-item :label="`${$t(`param['过压恢复']`)}`" prop="setting1937" style="width: 50%">
        <span slot="label">
          {{ $t(`param['过压恢复']`) }}
          <el-tooltip class="item" effect="dark" :content="$t('电池可充电恢复电压。')" placement="bottom">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
        <!-- <el-input v-model="form.setting1937" :placeholder="'请输入'">
          <span slot="suffix">（50~1000）V</span>
        </el-input> -->
        <el-input-number v-model="form.setting1937" :precision="1" :step="0.1" :max="1000" :min="50"
          :placeholder="$t(`common['Please enter']`)" @focus="handleInputFocus" @blur="handleInputBlur"></el-input-number>
        <span class="suffix">（50~1000）V</span>
      </el-form-item>
    </el-form>
    <div class="box-footer">
      <el-divider></el-divider>
      <!-- <el-button :style="{ width: $convertPx(100, 'rem') }" @click="handleSaveClick()">保存</el-button> -->
      <el-button :style="{ width: $convertPx(100, 'rem') }" @click="getInfo">{{
    $t(`tagsView.refresh`) }}</el-button>
      <el-button type="primary" :style="{ width: $convertPx(100, 'rem') }" @click="handleSendClick" :disabled="isSend">{{ $t(`param['下发']`) }}</el-button>
    </div>

    <el-dialog :visible.sync="dialogVisible" width="20%" :modal-append-to-body="false">
      <span slot="title"></span>
      <el-result icon="success" :subTitle="$t(`param['参数已下发至设备']`)">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="handleResultClick">{{ $t(`param['查看执行结果']`) }}</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed, onUnmounted } from 'vue'
import { useStore, useRoute, useRouter } from '@/utils/vueApi.js'
import _ from 'lodash'

const { proxy } = getCurrentInstance()

const store = useStore()
const route = useRoute()
const router = useRouter()

const form = ref({
  setting1930: undefined,
  setting1931: undefined,
  setting1932: undefined,
  setting1933: undefined,
  setting1934: undefined,
  setting1935: undefined,
  setting1936: undefined,
  setting1937: undefined,
  ac: route.query.id,
  status: 0
})
const rules = ref({})

const handleSaveClick = () => {
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      const api = store.state.param.bmsInfo ? 'editBMSFn' : 'addBMSFn'
      store.dispatch(`param/${api}`, form.value).then(async (res) => {
        proxy.$message({
          type: 'success',
          message: proxy.$t(`param['保存成功']`)
        })
        await getInfo()
      })
    }
  });
}

// 下发
const handleSendClick = () => {
  if (form.value.status == 2) return proxy.$message({
    type: 'warning',
    message: proxy.$t(`param['正在下发中，请稍后再下发']`)
  })
  proxy.$modal.loading(`${proxy.$t(`param['正在下发中']`)}...`);
  proxy.$refs['formRef'].validate((valid) => {
    if (valid) {
      store.dispatch('param/sendParamBMSFn', {
        ac: form.value.ac,
        id: form.value.id,
        setting1930: form.value.setting1930,
        setting1931: form.value.setting1931,
        setting1932: form.value.setting1932,
        setting1933: form.value.setting1933,
        setting1934: form.value.setting1934,
        setting1935: form.value.setting1935,
        setting1936: form.value.setting1936,
        setting1937: form.value.setting1937,
      }).then(async res => {
        // dialogVisible.value = true
        getInfo()
        proxy.$modal.closeLoading()
      }).catch(() => {
        proxy.$modal.closeLoading()
      })
    }
  })
}

const dialogVisible = ref(false)

/**
 * 查看结果
 */
const handleResultClick = () => {
  let routeData = router.resolve({
    path: '/operation/log/instruct',
  });
  window.open(routeData.href, '_blank');
}

const getInfo = async () => {
  const res = await store.dispatch('param/bmsInfoFn', { ac: route.query.id })
  if (!res) return
    for (let key in res) {
      if (res[key] == null) res[key] = undefined
    }
    let data = _.cloneDeep(form.value)
    if (res.status == 1 && data.status == 2) {
      proxy.$message({
        type: 'success',
          message: proxy.$t(`param['下发成功']`)
        })
    } else if (res.status == 3 && data.status == 2) {
      proxy.$message({
        type: 'error',
        message: proxy.$t(`param['下发失败']`)
      })
    }
    form.value = {
      ...res
    }
}

/**
 * 设置才有范围限制
 */
const isShowExceed = ref(false)
const isExceedCom = computed(() => {
  return (min) => {
    if (isShowExceed.value) {
    return min
  } else {
    return 0
  }
  }
})
const handleInputFocus = () => {
  isShowExceed.value = true
}
const handleInputBlur = () => {
  isShowExceed.value = false
}

const isSend = computed(() => {
  if (_.isEmpty(store.state.monitor.control)) return true
  return store.state.monitor.control['onLineState'] == '离线'
})

const time = ref(null)
onMounted(() => {
  getInfo()

  // time.value = setInterval(() => {
  //   getInfo()
  // }, 10000)
})
onUnmounted(() => {
  clearInterval(time.value)
})
</script>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-bottom: none;
}
</style>
