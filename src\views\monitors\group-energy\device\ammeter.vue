<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-05 09:36:24
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <div v-for="(eleItem, index) in ele" :key="eleItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <!-- 电表名称可修改 -->
        <el-descriptions-item>
          {{ eleItem.name }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot"
        style="margin-bottom: 20px">
        <el-descriptions-item :label="$t('device.type')">
          <!-- {{ getEleType(eleItem['em_10001']) }} -->
          {{ getEleDcType(eleItem['dc']).text }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('电表在线状态')">
          <span>{{ getStatus(index) }}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ac_2051')">
          <span v-if="eleItem['em_10011']">{{ eleItem['em_10011'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 有功功率 -->
        <el-descriptions-item :label="$t('monitor.ac_2049')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10012']">{{ eleItem['em_10012'] }} kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 无功功率 -->
        <el-descriptions-item :label="$t('monitor.ac_2050')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10013']">{{ eleItem['em_10013'] }} kVar</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 视在功率 -->
        <el-descriptions-item :label="$t('monitor.ac_2061')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10014']">{{ eleItem['em_10014'] }} kVA</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('A相电压')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10005']">{{ eleItem['em_10005'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('B相电压')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10006']">{{ eleItem['em_10006'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('C相电压')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10007']">{{ eleItem['em_10007'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ac_2045')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10008']">{{ eleItem['em_10008'] }} A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ac_2046')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10009']">{{ eleItem['em_10009'] }} A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ac_2047')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10010']">{{ eleItem['em_10010'] }} A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('AB线电压')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10041']">{{ eleItem['em_10041'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('BC线电压')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10042']">{{ eleItem['em_10042'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('CA线电压')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10043']">{{ eleItem['em_10043'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('正向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10015']">{{ eleItem['em_10015'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('反向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10016']">{{ eleItem['em_10016'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('正向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10017']">{{ eleItem['em_10017'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('反向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10018']">{{ eleItem['em_10018'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('尖正向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10031']">{{ eleItem['em_10031'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('尖反向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10032']">{{ eleItem['em_10032'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('尖正向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10033']">{{ eleItem['em_10033'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('尖反向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10034']">{{ eleItem['em_10034'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('峰正向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10019']">{{ eleItem['em_10019'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('峰反向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10020']">{{ eleItem['em_10020'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('峰正向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10021']">{{ eleItem['em_10021'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('峰反向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10022']">{{ eleItem['em_10022'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('平正向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10027']">{{ eleItem['em_10027'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('平反向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10028']">{{ eleItem['em_10028'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('平正向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10029']">{{ eleItem['em_10029'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('平反向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10030']">{{ eleItem['em_10030'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('谷正向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10023']">{{ eleItem['em_10023'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('谷反向有功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10024']">{{ eleItem['em_10024'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('谷正向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10025']">{{ eleItem['em_10025'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('谷反向无功电度')" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <span v-if="eleItem['em_10026']">{{ eleItem['em_10026'] }} kVarh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="getEleDcType(eleItem['dc']).type == '1'">
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ac_2039')" v-if="getEleDcType(eleItem['dc']).type == '6'">
          <span v-if="eleItem['em_10038']">{{ eleItem['em_10038'] }} V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ac_2040')" v-if="getEleDcType(eleItem['dc']).type == '6'">
          <span v-if="eleItem['em_10039']">{{ eleItem['em_10039'] }} A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.ac_2041')" v-if="getEleDcType(eleItem['dc']).type == '6'">
          <span v-if="eleItem['em_10040']">{{ eleItem['em_10040'] }} kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('contrast.AC_2048')" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <span v-if="eleItem['em_10035']">{{ eleItem['em_10035'] }}Hz</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('正向总电量')">
          <span v-if="eleItem['em_10036']">{{ eleItem['em_10036'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('反向总电量')">
          <span v-if="eleItem['em_10037']">{{ eleItem['em_10037'] }} kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>
<script>
import { emTypeOptions } from '@/constant'

export default {
  name: "device",
  data() {
    return {
    };
  },
  computed: {
    ele() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let data = this.$store.state.monitor.groupList[currentNodeKey]
      if (!data || Object.keys(data).length != 11) return []
      data.ele.forEach(item => {
        let value = this.aliasArr.find(alias => alias.point == item.dc)
        if (value) {
          item.name = value.alias
        } else {
          item.name = `${this.getEleDcType(item.dc).text}_${item.dc}`
        }
      })
      return data.ele
    },
    // 设备类型
    getEleType() {
      return (type) => {
        if (type == '1') {
          return this.$t('计量点电表')
        } else if (type == '2') {
          return this.$t('储能电表')
        } else if (type == '3') {
          return this.$t('PCC电表')
        } else if (type == '4') {
          return this.$t('光伏电表')
        } else if (type == '5') {
          return this.$t('负载电表')
        } else if (type == '6') {
          return this.$t('直流电表')
        } else if (type == '7') {
          return this.$t('市电电表')
        }
      }
    },
    getEleDcType() {
      return (dc) => {
        let dcNum = Number(dc)
        let value = emTypeOptions.find(range => dcNum >= range.min && dcNum <= range.max)
        if (value) {
          return {
            text: value.textKey,
            type: value.type
          }
        } else {
          return {
            text: this.$t('未知电表'),
            type: '0'
          }
        }
      }
    },
    // 别名
    aliasArr() {
      let data =  this.$store.state.monitor.aliasArr
      return data
    },
    getStatus() {
      return (index) => {
        if (this.ele[index].isAnalysis == 0) {
          return this.ele.onLineState == '在线' ? this.$t('common.online') : this.$t('common.offline')
        } else if (this.ele[index].isAnalysis == 1) {
          return this.ele[index]['em_10003'] == '1' ? this.$t('alarm.title'): this.$t('common.normal')
        } else {
          return '--'
        }
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
  overflow: auto;

  .el-button {
    float: right;
    margin-bottom: 20px;
  }

  .el_box {
    border-top: 1px solid #BFBFBF;
    border-left: 1px solid #BFBFBF;
    margin-bottom: 30px;

    .el-col {
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #BFBFBF;
      border-right: 1px solid #BFBFBF;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .el-col::after {
      content: attr(data-label);
      display: none;
    }

    .el-col:hover {
      overflow: visible;
      text-overflow: clip;
    }

    .left {
      text-align: left;
      padding: 0 10px 0 10px;
    }

    .right {
      text-align: right;
      padding: 0 10px 0 10px;
    }
  }

}
</style>
