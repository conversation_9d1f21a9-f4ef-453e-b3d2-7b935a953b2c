import request from '@/utils/request'

// 查询设备告警列表
export function alarmList(queryInfo) {
  return request({
    url: '/system/alarm/list',
    method: 'get',
    params: queryInfo
  })
}

// 修改告警处理状态
export function updateStateByRecordNameIds(data) {
  return request({
    url: '/system/alarm/updateStateByRecordNameIds',
    method: 'post',
    data
  })
}

// 根据设备det、告警点、告警bit位、告警点位协议获取详细信息
export function getAlarmDetails(data) {
  return request({
    url: '/system/method/getInfo',
    method: 'post',
    data
  })
}

