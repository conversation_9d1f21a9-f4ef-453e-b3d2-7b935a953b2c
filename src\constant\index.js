/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-05-19 19:45:15
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-19 19:45:46
 * @FilePath: \elecloud_platform-main\src\constant\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import i18n from '@/lang'

// 电池类型
export const bmsTypeOptions = [
  { label: i18n.t('宁德'), value: '0' },
  { label: i18n.t('协能'), value: '1' },
  { label: i18n.t('高泰昊能'), value: '2' },
  { label: i18n.t('华塑'), value: '3' },
  { label: i18n.t('高特'), value: '4' },
  { label: i18n.t('华思'), value: '5' },
  { label: i18n.t('小鸟'), value: '6' },
  { label: i18n.t('山东威马'), value: '7' },
  { label: i18n.t('亿纬锂电'), value: '9' },
  { label: i18n.t('力神'), value: '10' },
  { label: i18n.t('帷幕-BCU'), value: '11' },
  { label: i18n.t('宁德液冷'), value: '12' },
  { label: i18n.t('三级宁德'), value: '13' },
  { label: i18n.t('优旦'), value: '14' },
  { label: i18n.t('欣旺达'), value: '15' },
  { label: 'BLK', value: '16' },
  { label: i18n.t('沛城电子'), value: '17' },
  { label: i18n.t('帷幕-BCU2'), value: '18' },
  { label: i18n.t('群控能源GCE'), value: '19' },
  { label: i18n.t('高特三级bms'), value: '20' },
  { label: 'Qualtech power', value: '21' },
  { label: i18n.t('科工'), value: '23' },
]

export const bmsAirModeOptions = [
  { label: i18n.t('内循环'), value: '1' },
  { label: i18n.t('monitor.Cooling'), value: '2' },
  { label: i18n.t('制热'), value: '3' },
]

export const bmsAirStatusOptions = [
  { label: i18n.t(`param['关机']`), value: '0' },
  { label: i18n.t(`param['开机']`), value: '1' },
  { label: i18n.t('common.fault'), value: '2' },
]

export const GunStatusOptions = [
  { label: i18n.t('空闲'), value: '0' },
  { label: i18n.t('插枪'), value: '1' },
  { label: i18n.t('充电等待'), value: '2' },
  { label: i18n.t('启动中'), value: '3' },
  { label: i18n.t('充电中'), value: '4' },
  { label: i18n.t('重连'), value: '5' },
  { label: i18n.t('结算状态'), value: '7' },
  { label: i18n.t('故障状态'), value: '8' },
  { label: i18n.t('放电中'), value: '9' },
  { label: i18n.t('预约状态'), value: '11' },
  { label: i18n.t('后台预约状态'), value: '12' },
  { label: i18n.t('充电完成状态'), value: '14' },
  { label: i18n.t('APP，预约状态'), value: '15' },
  { label: i18n.t('试用期到，停止服务状态'), value: '16' },
]

export const chargingPileStatusOptions = [
  { label: i18n.t('common.Shutdown'), value: '0' },
  { label: i18n.t(`param['开机']`), value: '1' },
  { label: i18n.t('common.fault'), value: '2' },
]

export const controlAirStatusOptions = [
  { label: i18n.t('common.Closure'), value: '0' },
  { label: i18n.t('monitor.Blowers'), value: '1' },
  { label: i18n.t('monitor.Cooling'), value: '2' },
  { label: i18n.t('monitor.HeatUp'), value: '3' },
  { label: i18n.t('monitor.Dehumidify'), value: '4' },
]

export const acWorkModeOptions = [
  { label: 'PQ', value: '0' },
  { label: 'MPPT', value: '1' },
  { label: 'CV', value: '2' },
  { label: 'VSG', value: '3' },
  { label: 'VF', value: '4' },
]

export const dcWorkModeOptions = [
  { label: 'CP/CC', value: '0' },
  { label: 'MPPT', value: '1' },
  { label: 'CV', value: '2' },
]

export const dcHighTypeOptions = [
  { label: i18n.t('monitor.Lbattery'), value: '0' },
  { label: i18n.t(`monitor['铅酸电池']`), value: '1' },
  { label: i18n.t('monitor.Photovoltaic'), value: '2' },
  { label: i18n.t('monitor.DCbusbar'), value: '3' },
  { label: i18n.t('monitor.DCsource'), value: '4' },
]

export const emTypeOptions = [
  { min: 100, max: 199, textKey: i18n.t('计量点电表'), type: '1' },
  { min: 200, max: 299, textKey: i18n.t('储能电表'), type: '2' },
  { min: 300, max: 399, textKey: i18n.t('PCC电表'), type: '3' },
  { min: 400, max: 499, textKey: i18n.t('光伏电表'), type: '4' },
  { min: 500, max: 599, textKey: i18n.t('负载电表'), type: '5' },
  { min: 600, max: 699, textKey: i18n.t('直流电表'), type: '6' },
  { min: 700, max: 799, textKey: i18n.t('市电电表'), type: '7' }
]
