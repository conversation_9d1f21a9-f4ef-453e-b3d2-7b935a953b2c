import request from '@/utils/request'

export function list(queryInfo) {
  return request({
    url: '/system/appUpgrade/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addAppUpgrade(data) {
  return request({
    url: '/system/appUpgrade',
    method: 'post',
    data
  })
}

// 修改
export function editAppUpgrade(data) {
  return request({
    url: '/system/appUpgrade',
    method: 'put',
    data
  })
}

// 删除
export function deleteAppUpgrade(queryInfo) {
  return request({
    url: `/system/appUpgrade/${queryInfo.ids}`,
    method: 'delete'
  })
}
