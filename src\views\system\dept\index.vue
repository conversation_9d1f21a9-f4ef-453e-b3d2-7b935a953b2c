<template>
  <div class="app-container">
    <div class="input_box">
      <div class="header-title">{{ $route.meta.title }}</div>
      <div>
        <div class="input_ment">
        <el-input
          v-model="queryParams.deptName"
          :placeholder="$t(`dept['Please enter department name']`)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </div>
      <div class="input_ment">
        <el-select v-model="queryParams.status" :placeholder="$t('dept.status')" clearable style="width: 120px">
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </div>
      <div class="input_ment">
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('common.search') }}</el-button>
      </div>
      <div class="input_ment">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-hasPermi="['system:dept:add']">
        {{ $t('common.add') }}</el-button>
      </div>
      <div class="input_ment">
        <el-button
          type="info"
          icon="el-icon-sort"
          @click="toggleExpandAll"
        >{{ $t('role.expand') }}</el-button>
      </div>
      </div>
    </div>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="deptList"
      row-key="deptId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="deptName" :label="$t('dept.name')" width="260" />
      <el-table-column prop="orderNum" :label="$t('menu.sort')" width="200" align="center" />
      <el-table-column prop="status" :label="$t('common.status')" width="100" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column prop="logoPathCn" :label="$t('中文logo')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-image
              :style="{width: $convertPx(100, 'rem'), height: $convertPx(60, 'rem')}"
              :src="`${origin}/prod-api/${scope.row.logoPathCn}`"
              :preview-src-list="[`${origin}/prod-api/${scope.row.logoPathCn}`]">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="logoPathUs" :label="$t('英文logo')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-image
              :style="{width: $convertPx(100, 'rem'), height: $convertPx(60, 'rem')}"
              :src="`${origin}/prod-api/${scope.row.logoPathUs}`"
              :preview-src-list="[`${origin}/prod-api/${scope.row.logoPathUs}`]">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="zoomLogoPath" :label="$t('缩放logo')" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            <el-image
              :style="{width: $convertPx(100, 'rem'), height: $convertPx(60, 'rem')}"
              :src="`${origin}/prod-api/${scope.row.zoomLogoPath}`"
              :preview-src-list="[`${origin}/prod-api/${scope.row.zoomLogoPath}`]">
            </el-image>
          </template>
        </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.handle')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:dept:edit']"
          >{{ $t('common.edit') }}</el-button>
          <el-button
            type="text"
            @click="handleAdd(scope.row)"
            v-hasPermi="['system:dept:add']">
          {{ $t('common.add') }}</el-button>
          <el-button
            v-if="scope.row.parentId != 0"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:dept:remove']"
          >{{ $t('common.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body center :modal-append-to-body="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="24" v-if="form.parentId !== 0">
            <el-form-item :label="$t('dept.parent')" prop="parentId">
              <treeselect v-model="form.parentId" :options="deptOptions" :normalizer="normalizer" :placeholder="$t(`dept['Select superior department']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('dept.name')" prop="deptName">
              <el-input v-model="form.deptName" :placeholder="$t(`dept['Please enter department name']`)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('menu.show')" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('dept.principal')" prop="leader">
              <el-input v-model="form.leader" :placeholder="$t(`dept['Please enter responsible Person']`)" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('dept.phone')" prop="phone">
              <el-input v-model="form.phone" :placeholder="$t(`dept['Please enter phone']`)" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('user.email')" prop="email">
              <el-input v-model="form.email" :placeholder="$t(`user['Please enter email']`)" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('dept.status')">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('二级角色部门')" prop="isAdministrator">
              <el-radio-group v-model="form.isAdministrator">
                <el-radio :label="1">{{ $t('是') }}</el-radio>
                <el-radio :label="0">{{ $t('否') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t(`中文logo`)" prop="logoPathCn" v-if="form.isAdministrator == 1">
              <file-upload :value="form.logoPathCn" @input="getCn" size="200 x 55px" :limit="1" :fileSize="20" :fileType="['png', 'jpeg', 'jpg']"></file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t(`英文logo`)" prop="logoPathUs" v-if="form.isAdministrator == 1">
              <file-upload :value="form.logoPathUs" @input="getUs" size="200 x 55px" :limit="1" :fileSize="20" :fileType="['png', 'jpeg', 'jpg']"></file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t(`缩放logo`)" prop="zoomLogoPath" v-if="form.isAdministrator == 1">
              <file-upload :value="form.zoomLogoPath" @input="getZoom" size="54 x 50px" :limit="1" :fileSize="20" :fileType="['png', 'jpeg', 'jpg']"></file-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('宽度')" prop="logoWidth">
              <el-input v-model="form.logoWidth" :placeholder="$t(`common['Please enter']`)" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('高度')" prop="logoHeight">
              <el-input v-model="form.logoHeight" :placeholder="$t(`common['Please enter']`)" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Dept",
  dicts: ['sys_normal_disable'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: this.$t(`dept['Superior department cannot be empty']`), trigger: "blur" }
        ],
        deptName: [
          { required: true, message: this.$t(`dept['Department name cannot be empty']`), trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: this.$t(`dept['Display order cannot be empty']`), trigger: "blur" }
        ],
        email: [
          {
            type: "email",
            message: this.$t(`user['Please enter a valid email address']`),
            trigger: ["blur", "change"]
          }
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t(`user['Please enter a valid mobile phone number']`),
            trigger: "blur"
          }
        ],
        // logoPathCn: [
        //   { required: true, message: '请选择中文logo', trigger: 'blur' }
        // ],
        // logoPathUs: [
        //   { required: true, message: '请选择英文logo', trigger: 'blur' }
        // ],
        // isAdministrator: [
        //   { required: true, message: '请选择是否为二级角色部门', trigger: 'blur' }
        // ],
      }
    };
  },
  created() {
    this.getList();
    this.origin = window.location.origin
  },
  methods: {
    /** 查询部门列表 */
    getList() {
      this.loading = true;
      listDept(this.queryParams).then(response => {
        this.deptList = this.handleTree(response.data, "deptId");
        this.loading = false;
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        status: "0",
        isAdministrator: 1,
        logoWidth: undefined,
        logoHeight: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row != undefined) {
        this.form.parentId = row.deptId;
      }
      this.open = true;
      this.title = this.$t('dept.addDept');
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getDept(row.deptId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('dept.editDept');
        listDeptExcludeChild(row.deptId).then(response => {
          this.deptOptions = this.handleTree(response.data, "deptId");
          if (this.deptOptions.length == 0) {
            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] };
            this.deptOptions.push(noResultsOptions);
          }
        });
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.deptId != undefined) {
            updateDept(this.form).then(response => {
              this.$modal.msgSuccess(this.$t(`common['Modify successfully']`));
              this.open = false;
              this.getList();
            });
          } else {
            addDept(this.form).then(response => {
              this.$modal.msgSuccess(this.$t(`common['Added successfully']`));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm(this.$t(`menu['Are you sure to delete the data item?']`)).then(function() {
        return delDept(row.deptId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t(`common['Deleted successfully']`));
      }).catch(() => {});
    },
    getCn(res) {
      this.form.logoPathCn = res
    },
    getUs(res) {
      this.form.logoPathUs = res
    },
    getZoom(res) {
      this.form.zoomLogoPath = res
    },
  }
};
</script>
