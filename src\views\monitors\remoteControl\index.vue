<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-26 16:41:10
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-30 16:17:09
 * @FilePath: \办公文档\代码\新建文件夹\src\views\property\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="remote-home">
    <InnerLink
    ref="remoteIframe"
    :iframeId="'iframe' + 'Remote'"
      :src="src"></InnerLink>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import { getToken } from '@/utils/auth'
import Cookies from 'js-cookie'

import InnerLink from "@/layout/components/InnerLink/index"

const props = defineProps({
  id: String
})
const { proxy } = getCurrentInstance()
const src = ref('http://*************:8080')
const time = ref(null)
const params = ref({
  type: 'setToken',
  ac: '',
  token: getToken() ? 'Bearer ' + getToken(): ''
})
onMounted(() => {
  /**
   * 给子窗口传递消息
   */
  time.value = setInterval(() => {
    if (proxy.$refs.remoteIframe) {
      if (proxy.$refs.remoteIframe.$refs['iframeRemote']) {
        proxy.$refs.remoteIframe.$refs['iframeRemote'].contentWindow.postMessage({ ...params.value, lang: Cookies.get('language'), ac: props.id }, src.value)
      }
    }
  }, 3000)
})

window.onmessage = (e) => {
  // console.log(e.origin, src.value)
  if (e.origin == src.value) {
    clearInterval(time.value)
  }
}

onUnmounted(() => {
  clearInterval(time.value)
})
</script>

<style scoped lang="scss">
.screen-home {
  width: 100%;
  height: 100%;
}
</style>
