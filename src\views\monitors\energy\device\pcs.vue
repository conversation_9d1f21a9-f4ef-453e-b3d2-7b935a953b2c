<!--
 * @Author: she<PERSON>ri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-06-26 10:40:28
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <!-- AC -->
    <div v-for="(acItem, index) in ac" :key="acItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ acItem.name }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot">
        <!-- 2015：bit4 0并网 1离网 -->
        <el-descriptions-item :label="$t('monitor.jk_1001Bit14')">
          {{ get2015Bit4Fn(acItem['ac_2015']) }}
        </el-descriptions-item>
        <!-- 2010 -->
        <el-descriptions-item :label="$t('monitor.ac_2010')">
          {{ get2010Fn(acItem['ac_2010']) }}
        </el-descriptions-item>
        <!-- 2015：bit5 0放电 1充电 -->
        <el-descriptions-item :label="$t('monitor.ac_2015Bit5')">
          {{ get2015Bit5Fn(acItem['ac_2015']) }}
        </el-descriptions-item>
        <!-- 2015：bit6 0停机 1运行 -->
        <el-descriptions-item :label="$t('monitor.ac_2015Bit6')">
          {{ get2015Bit6Fn(acItem['ac_2015']) }}
        </el-descriptions-item>
        <!-- 2015：bit7 0正常 1故障 -->
        <el-descriptions-item :label="$t('monitor.ac_2015Bit7')">
          {{ get2015Bit7Fn(acItem['ac_2015']) }}
        </el-descriptions-item>
        <!-- 2000 1在线 0离线 -->
        <el-descriptions-item :label="$t('monitor.onLineState')">
          <span v-if="getStatus('ac', index, 'ac_2000') == '离线'">{{ $t('common.offline') }}</span>
          <span v-else>{{ getStatus('ac', index, 'ac_2000') }}</span>
        </el-descriptions-item>
        <!-- 2039 -->
        <el-descriptions-item :label="$t('monitor.ac_2039')">
          {{ acItem['ac_2039'] }}V
        </el-descriptions-item>
        <!-- 2040 -->
        <el-descriptions-item :label="$t('monitor.ac_2040')">
          {{ acItem['ac_2040'] }}A
        </el-descriptions-item>
        <!-- 2041 -->
        <el-descriptions-item :label="$t('monitor.ac_2041')">
          {{ acItem['ac_2041'] }}kW
        </el-descriptions-item>
        <!-- 2042 -->
        <el-descriptions-item :label="$t('monitor.ac_2042')">
          {{ acItem['ac_2042'] }}V
        </el-descriptions-item>
        <!-- 2043 -->
        <el-descriptions-item :label="$t('monitor.ac_2043')">
          {{ acItem['ac_2043'] }}V
        </el-descriptions-item>
        <!-- 2044 -->
        <el-descriptions-item :label="$t('monitor.ac_2044')">
          {{ acItem['ac_2044'] }}V
        </el-descriptions-item>
        <!-- 2045 -->
        <el-descriptions-item :label="$t('monitor.ac_2045')">
          {{ acItem['ac_2045'] }}A
        </el-descriptions-item>
        <!-- 2046 -->
        <el-descriptions-item :label="$t('monitor.ac_2046')">
          {{ acItem['ac_2046'] }}A
        </el-descriptions-item>
        <!-- 2047 -->
        <el-descriptions-item :label="$t('monitor.ac_2047')">
          {{ acItem['ac_2047'] }}A
        </el-descriptions-item>
        <!-- 2049 -->
        <el-descriptions-item :label="$t('monitor.ac_2049')">
          {{ acItem['ac_2049'] }}kW
        </el-descriptions-item>
        <!-- 2050 -->
        <el-descriptions-item :label="$t('monitor.ac_2050')">
          {{ acItem['ac_2050'] }}kVar
        </el-descriptions-item>
        <!-- 2061 -->
        <el-descriptions-item :label="$t('monitor.ac_2061')">
          <span v-if="acItem['ac_2061']">{{ acItem['ac_2061'] }}kVA</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 2048 -->
        <el-descriptions-item :label="$t('monitor.ac_2048')">
          <span v-if="acItem['ac_2048']">{{ acItem['ac_2048'] }}Hz</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 2056 -->
        <el-descriptions-item :label="$t('monitor.ac_2056')" v-if="!commonRole">
          <span v-if="acItem['ac_2056']">{{ acItem['ac_2056'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 2051 -->
        <el-descriptions-item :label="$t('monitor.ac_2051')">
          <span v-if="acItem['ac_2051']">{{ acItem['ac_2051'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 2057 -->
        <el-descriptions-item :label="$t('monitor.ac_2057')">
          <span v-if="acItem['ac_2057']">{{ acItem['ac_2057'] }}kΩ</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 2058 -->
        <el-descriptions-item :label="$t('monitor.ac_2058')">
          <span v-if="acItem['ac_2058']">{{ acItem['ac_2058'] }}kΩ</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 2001 -->
        <el-descriptions-item :label="$t('monitor.acVersion')">
          <span v-if="acItem['ac_2002']">{{ acItem['ac_2002'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- DC -->
    <div v-for="(dcItem, index) in dc" :key="dcItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ dcItem.name }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot">
        <el-descriptions-item :label="$t('monitor.highType')">
          <span v-if="dcItem['dc_3028']">{{ get3028Fn(dcItem['dc_3028']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.lowType')">
          <span v-if="dcItem['dc_3029']">{{ get3028Fn(dcItem['dc_3029']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3004：bit6 -->
        <el-descriptions-item :label="$t('monitor.ac_2015Bit6')">
          <span v-if="dcItem['dc_3004']">{{ get3004Bit6Fn(dcItem['dc_3004']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3004：bit7 -->
        <el-descriptions-item :label="$t('monitor.ac_2015Bit7')">
          <span v-if="dcItem['dc_3004']">{{ get3004Bit7Fn(dcItem['dc_3004']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3003 -->
        <el-descriptions-item :label="$t('monitor.ac_2010')">
          <span v-if="dcItem['dc_3003']">{{ get3003Fn(dcItem['dc_3003']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3000 -->
        <el-descriptions-item :label="$t('monitor.onLineState')">
          <span v-if="getStatus('dc', index, 'dc_3000') == '离线'">{{ $t('common.offline') }}</span>
          <span v-else>{{ getStatus('dc', index, 'dc_3000') }}</span>
        </el-descriptions-item>
        <!-- 3012 -->
        <el-descriptions-item :label="$t('monitor.dc_3012')">
          <span v-if="dcItem['dc_3012']">{{ dcItem['dc_3012'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3013 -->
        <el-descriptions-item :label="$t('monitor.dc_3013')">
          <span v-if="dcItem['dc_3013']">{{ dcItem['dc_3013'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3014 -->
        <el-descriptions-item :label="$t('monitor.dc_3014')">
          <span v-if="dcItem['dc_3014']">{{ dcItem['dc_3014'] }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3015 -->
        <el-descriptions-item :label="$t('monitor.dc_3015')">
          <span v-if="dcItem['dc_3015']">{{ dcItem['dc_3015'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3016 -->
        <el-descriptions-item :label="$t('monitor.dc_3016')">
          <span v-if="dcItem['dc_3016']">{{ dcItem['dc_3016'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3017 -->
        <el-descriptions-item :label="$t('monitor.dc_3017')">
          <span v-if="dcItem['dc_3017']">{{ dcItem['dc_3017'] }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3018 -->
        <el-descriptions-item :label="$t('monitor.dc_3018')">
          <span v-if="dcItem['dc_3018']">{{ dcItem['dc_3018'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3019 -->
        <el-descriptions-item :label="$t('monitor.dc_3019')">
          <span v-if="dcItem['dc_3019']">{{ dcItem['dc_3019'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3020 -->
        <el-descriptions-item :label="$t('monitor.dc_3020')">
          <span v-if="dcItem['dc_3020']">{{ dcItem['dc_3020'] }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3025 -->
        <el-descriptions-item :label="$t('monitor.ac_2056')" v-if="!commonRole">
          <span v-if="dcItem['dc_3025']">{{ dcItem['dc_3025'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3026 -->
        <el-descriptions-item :label="$t('正母线电压')">
          <span v-if="dcItem['dc_3026']">{{ dcItem['dc_3026'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3027 -->
        <el-descriptions-item :label="$t('负母线电压')">
          <span v-if="dcItem['dc_3027']">{{ dcItem['dc_3027'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3002 -->
        <el-descriptions-item :label="$t('monitor.acVersion')">
          <span v-if="dcItem['dc_3002']">{{ dcItem['dc_3002'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- STS -->
    <div  v-for="(stsItem, index) in sts" :key="stsItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ stsItem.name }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot">
        <!-- 3500 1在线 0离线 -->
        <el-descriptions-item :label="$t('monitor.sts_3500')">
          <span v-if="getStatus('sts', index, 'sts_3500') == '离线'">{{ $t('common.offline') }}</span>
          <span v-else>{{ getStatus('sts', index, 'sts_3500') }}</span>
        </el-descriptions-item>
        <!-- 3501 -->
        <el-descriptions-item :label="$t('monitor.acVersion')">
          <span v-if="stsItem['sts_3501']">{{ stsItem['sts_3501'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3502 动态 -->
        <el-descriptions-item :label="$t('monitor.deviceStatus')">
          <span v-if="stsItem['sts_3502']">{{ get3002Bit0Fn(stsItem['sts_3502']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3507 -->
        <el-descriptions-item :label="$t('monitor.sts_3507')">
          <span v-if="stsItem['sts_3507']">{{ stsItem['sts_3507'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3508 -->
        <el-descriptions-item :label="$t('monitor.sts_3508')">
          <span v-if="stsItem['sts_3508']">{{ stsItem['sts_3508'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3009 -->
        <el-descriptions-item :label="$t('monitor.sts_3509')">
          <span v-if="stsItem['sts_3509']">{{ stsItem['sts_3509'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3511 -->
        <el-descriptions-item :label="$t('monitor.sts_3511')">
          <span v-if="stsItem['sts_3511']">{{ stsItem['sts_3511'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3512 -->
        <el-descriptions-item :label="$t('monitor.sts_3512')">
          <span v-if="stsItem['sts_3512']">{{ stsItem['sts_3512'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3513 -->
        <el-descriptions-item :label="$t('monitor.sts_3513')">
          <span v-if="stsItem['sts_3513']">{{ stsItem['sts_3513'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3519 -->
        <el-descriptions-item :label="$t('monitor.sts_3519')">
          <span v-if="stsItem['sts_3519']">{{ stsItem['sts_3519'] }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3520 -->
        <el-descriptions-item :label="$t('monitor.sts_3520')">
          <span v-if="stsItem['sts_3520']">{{ stsItem['sts_3520'] }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3021 -->
        <el-descriptions-item :label="$t('monitor.sts_3521')">
          <span v-if="stsItem['sts_3521']">{{ stsItem['sts_3521'] }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3523 -->
        <el-descriptions-item :label="$t('monitor.sts_3523')">
          <span v-if="stsItem['sts_3523']">{{ stsItem['sts_3523'] }}kvar</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3524 -->
        <el-descriptions-item :label="$t('monitor.sts_3524')">
          <span v-if="stsItem['sts_3524']">{{ stsItem['sts_3524'] }}kvar</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3525 -->
        <el-descriptions-item :label="$t('monitor.sts_3525')">
          <span v-if="stsItem['sts_3525']">{{ stsItem['sts_3525'] }}kvar</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3522 -->
        <el-descriptions-item :label="$t('monitor.sts_3522')">
          <span v-if="stsItem['sts_3522']">{{ stsItem['sts_3522'] }}kW</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3526 -->
        <el-descriptions-item :label="$t('monitor.sts_3526')">
          <span v-if="stsItem['sts_3526']">{{ stsItem['sts_3526'] }}kvar</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3534 -->
        <el-descriptions-item :label="$t('monitor.sts_3534')">
          <span v-if="stsItem['sts_3534']">{{ stsItem['sts_3534'] }}kVA</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3531 -->
        <el-descriptions-item :label="$t('monitor.sts_3531')">
          <span v-if="stsItem['sts_3531']">{{ stsItem['sts_3531'] }}kVA</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3532 -->
        <el-descriptions-item :label="$t('monitor.sts_3532')">
          <span v-if="stsItem['sts_3532']">{{ stsItem['sts_3532'] }}kVA</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3533 -->
        <el-descriptions-item :label="$t('monitor.sts_3533')">
          <span v-if="stsItem['sts_3533']">{{ stsItem['sts_3533'] }}kVA</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3515 -->
        <el-descriptions-item :label="$t('monitor.sts_3515')">
          <span v-if="stsItem['sts_3515']">{{ stsItem['sts_3515'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3516 -->
        <el-descriptions-item :label="$t('monitor.sts_3516')">
          <span v-if="stsItem['sts_3516']">{{ stsItem['sts_3516'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3517 -->
        <el-descriptions-item :label="$t('monitor.sts_3517')">
          <span v-if="stsItem['sts_3517']">{{ stsItem['sts_3517'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3527 -->
        <el-descriptions-item :label="`${$t('monitor.ac_2051')} A`">
          <span v-if="stsItem['sts_3527']">{{ stsItem['sts_3527'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3528 -->
        <el-descriptions-item :label="`${$t('monitor.ac_2051')} B`">
          <span v-if="stsItem['sts_3528']">{{ stsItem['sts_3528'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3529 -->
        <el-descriptions-item :label="`${$t('monitor.ac_2051')} C`">
          <span v-if="stsItem['sts_3529']">{{ stsItem['sts_3529'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3530 -->
        <el-descriptions-item :label="$t('monitor.sts_3530')">
          <span v-if="stsItem['sts_3530']">{{ stsItem['sts_3530'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3518 -->
        <el-descriptions-item :label="$t('monitor.sts_3518')">
          <span v-if="stsItem['sts_3518']">{{ stsItem['sts_3518'] }}Hz</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 3510 -->
        <el-descriptions-item :label="$t('变压器温度')">
          <span v-if="stsItem['sts_3510']">{{ stsItem['sts_3510'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>
<script>
import { get2015Bit4, get2015Bit5, get2015Bit6, get2015Bit7, get3004Bit6, get3004Bit7, get3002Bit0 } from '@/utils/parseBinaryToText'
import { acWorkModeOptions, dcWorkModeOptions, dcHighTypeOptions } from '@/constant'

export default {
  name: "device",
  computed: {
    ac() {
      let data = this.$store.state.monitor.pcs_ac
      data.forEach(item => {
        item.name = `${parseInt(item.dc) - 131000 + 1}#Monet-AC`
      })
      return data
    },
    dc() {
      let data = this.$store.state.monitor.pcs_dc
      data.forEach(item => {
        item.name = `${parseInt(item.dc) - 141000 + 1}#Monet-DC`
      })
      return data
    },
    sts() {
      let data = this.$store.state.monitor.pcs_sts
      data.forEach(item => {
        item.name = `${parseInt(item.dc) - 151000 + 1}#Monet-STS`
      })
      return data
    },
    get2010Fn() {
      return (num) => {
        let value = acWorkModeOptions.find(item => item.value == num)
        if (value) return value.label
        return '--'
      }
    },
    get3003Fn() {
      return (num) => {
        let value = dcWorkModeOptions.find(item => item.value == num)
        if (value) return value.label
        return '--'
      }
    },
    get3028Fn() {
      return (num) => {
        let value = dcHighTypeOptions.find(item => item.value == num)
        if (value) return value.label
        return '--'
      }
    },
    commonRole() {
      let roles = this.$store.state.user.roles
      return roles.findIndex(item => item == 'common') !== -1
    },
    getStatus() {
      return (data, index, item) => {
        if (this[data][index].isAnalysis == 0) {
          return this[data][index].onLineState == '在线' ? this.$t('common.online') : this.$t('common.offline')
        } else if (this[data][index].isAnalysis == 1) {
          return this[data][index][item] == '1' ? this.$t('common.online') : this.$t('common.offline')
        } else {
          return '--'
        }
      }
    }
  },
  methods: {
    get2015Bit4Fn(num) {
      return get2015Bit4(num)
    },
    get2015Bit5Fn(num) {
      return get2015Bit5(num)
    },
    get2015Bit6Fn(num) {
      return get2015Bit6(num)
    },
    get2015Bit7Fn(num) {
      return get2015Bit7(num)
    },
    get3004Bit6Fn(num) {
      return get3004Bit6(num)
    },
    get3004Bit7Fn(num) {
      return get3004Bit7(num)
    },
    get3002Bit0Fn(num) {
      return get3002Bit0(num)
    }
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
}
</style>
