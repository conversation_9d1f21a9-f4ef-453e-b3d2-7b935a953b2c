<template>
  <div class="page-box elevation-4 ">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <el-button type="primary" @click="getListNotice">{{ $t('tagsView.refresh') }}</el-button>
        <el-button type="primary" @click="handleReadClick(null, 'all')">{{ $t('全部已读') }}</el-button>
      </div>
    </div>
    <div>
      <template v-if="total > 0">
        <div v-for="item in listContent" :key="item.noticeId" class="list-item pointer" :style="{color: item.isRead == 1 ? '#c0c4cc': 'var(--base-color)'}" @click="handleItemClick(item)">
          <div class="list-item-title">
            <div style="display: flex;align-items: center;">
                <svg-icon icon-class="bullhorn-outline" style="width: 24px;height: 24px;margin-right: 5px;"></svg-icon>
                <div>{{ item[getPropFn('title')] }}</div>
            </div>
            <div class="list-item-dot" v-if="item.isRead == 0" />
            <!-- <el-button type="primary" size="mini" @click.stop="handleReadClick(item)" v-if="item.isRead == 0">{{ $t('已读') }}</el-button> -->
          </div>
          <div v-html="item[getPropFn('cont')]" class="list-item-cont">
          </div>
          <div class="list-item-time" :style="{color: item.isRead == 1 ? '#c0c4cc': 'var(--base-color)'}">
            {{ item.createTime }}
          </div>
        </div>
      </template>
      <el-empty :description="$t('暂无数据')" v-else></el-empty>

      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getListNotice" style="margin-top: 20px;text-align: right;" />
    </div>

    <el-dialog :title="detailsItem[getPropFn('title')]" :visible.sync="open" width="500px" append-to-body center :modal-append-to-body="false">
      <div v-html="detailsItem[getPropFn('content')]"></div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">{{ $t('common.Closure') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, ref, getCurrentInstance } from 'vue'
import { getUnreadNoticeList, readNotice } from '@/api/system/notice'

const { proxy } = getCurrentInstance()
const total = ref(0)
const queryInfo = ref({
  pageNum: 1,
  pageSize: 10
})
const listContent = ref([])
const getListNotice = () => {
  getUnreadNoticeList(queryInfo).then(res => {
    listContent.value = res.rows
    total.value = res.total
  })
}
getListNotice()

const getPropFn = computed(() => {
  return (type) => {
    let lang = proxy.$store.getters.language
    if (type == 'title') {
      switch (lang) {
        case 'zh':
          return 'noticeTitle'
        case 'en':
          return 'noticeTitleUs'
        case 'it':
          return 'noticeTitleIt'
      }
    } else {
      switch (lang) {
        case 'zh':
          return 'noticeContent'
        case 'en':
          return 'noticeContentUs'
        case 'it':
          return 'noticeContentIt'
      }
    }
  }
})

const handleReadClick = (item, type) => {
  let data = []
  let msg = ''
  if (type == 'all') {
    if (!listContent.value.length) return proxy.$modal.msg(proxy.$t('没有未读消息'));
    if (listContent.value.filter(item => item.isRead == 0).length == 0) return proxy.$modal.msg(proxy.$t('没有未读消息'));
    data = listContent.value.map(item => item.noticeIdRecipients)
    msg = proxy.$t('确定全部已读吗？')
  } else {
    data = [item.noticeIdRecipients]
    msg = proxy.$t('确定已读吗？')
  }
  const readFn = () => {
    proxy.$store.dispatch('notice/readNoticeFn', data).then(() => {
      getListNotice();
      if (type == 'all') proxy.$modal.msgSuccess(proxy.$t('已读成功'));
    }).catch(() => { });
  }
  if (type == 'all') proxy.$modal.confirm(msg).then(() => {
    readFn()
  })
  else readFn()
}

const open = ref(false)
const detailsItem = ref({
  noticeTitle: '',
  noticeTitleUs: '',
  noticeTitleIt: ''
})
const handleItemClick = (item) => {
  if (item.isRead == 0) handleReadClick(item)
  detailsItem.value = item
  open.value = true
}
</script>

<style lang="scss" scoped>
.list-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  font-size: 16px;

  &-title {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-time {
    color: #909399;
    font-size: 14px;
  }

  &-cont {
    padding: 5px 0 5px 20px;
  }

  &-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    margin-right: 5px;
  }
}
</style>
