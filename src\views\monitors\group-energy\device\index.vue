<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:01
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-02 09:11:50
 * @FilePath: \elecloud_platform-main\src\views\monitors\energy\device\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div style="position: relative;height: 100%;display: flex;padding: 20px 0;">
    <el-tree :data="dataCom" @node-click="handleNodeClick" node-key="id" class="tree" :current-node-key="currentNodeKey"
      default-expand-all :expand-on-click-node="false" ref="treeRef">
      <div class="tree-title" slot-scope="{ node }">
        <div>{{ node.label }}</div>
      </div>
    </el-tree>
    <div style="flex: 5;">
      <Controller v-show="isDeviceInfo"></Controller>
      <PCS v-show="isPcs"></PCS>
      <BMS type="bms" v-show="isBms"></BMS>
      <BMS type="bmsBau" v-show="isBmsBau"></BMS>
      <Cell v-show="isCell"></Cell>
      <Ammeter v-show="isAmmeter"></Ammeter>
      <peripherals v-show="isIo"></peripherals>
      <chargingPile v-show="isCp"></chargingPile>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, getCurrentInstance, watch, nextTick } from 'vue'
import { useStore, useRoute } from '@/utils/vueApi.js'

import Ammeter from "./ammeter.vue";
import BMS from "./bms.vue";
import Cell from "./cell.vue";
import Controller from "./controller.vue";
import PCS from "./pcs.vue";
import peripherals from './peripherals.vue'
import chargingPile from './chargingPile.vue';
import { isEmpty } from 'lodash'

const { proxy } = getCurrentInstance()

const store = useStore()
const route = useRoute()

const groupId = computed(() => {
  let data = proxy.$route.query.groupId.split(',')
  return data
})
const groupType = computed(() => {
  let data = proxy.$route.query.groupType.split(',')
  return data
})
const currentKey = ref()
currentKey.value = `${groupId.value[0]}-deviceInfo`
store.commit('SET_CURRENTNODEKEY', 0)
store.commit('SET_CURRENTNODTYPE', 'deviceInfo')
const currentNodeKey = computed({
  get: function () {
    if (store.state.monitor.currentNodeKey || store.state.monitor.currentNodeType) {
      return `${groupId.value[store.state.monitor.currentNodeKey]}-${store.state.monitor.currentNodeType}`
    }
    return currentKey.value
  },
  set: function (newValue) {
    currentKey.value = newValue
  }
})
const handleNodeClick = (node) => {
  currentNodeKey.value = node.id
  let idArr = groupId.value.findIndex(item => item == node.id.split('-')[0])
  store.commit('SET_CURRENTNODEKEY', idArr)
  store.commit('SET_CURRENTNODTYPE', node.id.split('-')[1])
  let type = node.id.split('-')[1]
  if (type == 'ammeter') {
    store.dispatch('bindAliasQueryFn', {
      ac: groupId.value.findIndex(item => item == node.id.split('-')[0]),
      enable: 1,
      type: 1
    })
  } else if (type == 'io') {
    store.dispatch('bindAliasQueryFn', {
      ac: groupId.value.findIndex(item => item == node.id.split('-')[0]),
      enable: 1,
      type: 0
    })
  } else if (type == 'cell') {
    store.dispatch('getCellConfigFn', {
      ac: node.id.split('-')[0],
      deviceType: route.query.type,
      groupId: groupId.value
    })
  }
}

const isDeviceInfo = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'deviceInfo' || !type) return true
  return false
})
const isPcs = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'pcs') return true
  return false
})
const isBms = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'bms') return true
  return false
})
const isBmsBau = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'bmsBau') return true
  return false
})
const isCell = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'cell') return true
  return false
})
const isAmmeter = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'ammeter') return true
  return false
})
const isIo = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'io') return true
  return false
})
const isCp = computed(() => {
  let type = currentNodeKey.value.split('-')[1]
  if (type == 'cp') return true
  return false
})

const dataCom = computed(() => {
  let data = []

  let groupList = store.state.monitor.groupList
  groupList.forEach((item, index) => {
    let children = [
      {
        label: proxy.$t('monitor.deviceInfo'), // 设备信息
        id: `${item.id}-deviceInfo`
      }
    ]
    if ((item.ac && item.ac.length) || (item.dc && item.dc.length) || (item.sts && item.sts.length)) children.push({
      label: groupType.value[index] == 4 ? 'PV': 'PCS',
      id: `${item.id}-pcs`
    })
    if (item.bms && item.bms.length) children.push({
      label: 'BMS',
      id: `${item.id}-bms`
    })
    if (item.bmsBau && item.bmsBau.length) children.push({
      label: 'BMS-BAU',
      id: `${item.id}-bmsBau`
    })
    if (item.cell?.findIndex(item => !isEmpty(item.bms_7101_7612) || !isEmpty(item.bms_7613_8124) || !isEmpty(item.bms_8125_8637) || !isEmpty(item.bms_8638_9149) || !isEmpty(item.bms_9150_9661)) != -1) children.push({
      label: 'CELL',
      id: `${item.id}-cell`
    })
    if (item.ele && item.ele.length) children.push({
      label: 'Ammeter',
      id: `${item.id}-ammeter`
    })
    if (item.io && item.io.length) children.push({
      label: 'IO',
      id: `${item.id}-io`
    })
    if (item.cp && item.cp.length) children.push({
      label: 'CP',
      id: `${item.id}-cp`
    })
    let label = ''
    if (index == 0) {
      label = `${proxy.$t(`monitor['主机']`)} - ${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
    } else {
      label = `${proxy.$t(`monitor['从机']`)} - ${index < 10 ? '0' + index : index}`
    }

    if (store.state.monitor.currentNodeKey || store.state.monitor.currentNodeType) {
      nextTick(() => {
        proxy.$refs?.treeRef?.setCurrentKey(`${groupId.value[store.state.monitor.currentNodeKey]}-${store.state.monitor.currentNodeType}`)
      })
    }

    data.push({
      label: label,
      children,
      id: item.id
    })
  })
  return data
})
</script>

<style lang="scss" scoped>
.tab {
  padding: 10px 20px 0 20px;
  height: 100%;
}

::v-deep .el-tabs__content {
  height: calc(100% - 80px);
  overflow: auto;
  overflow-x: hidden;
}

.tree {
  height: 100%;
  border-right: 1px solid #D6D6D6;
  flex: 1;
  margin-right: 10px;
  font-size: 14px;
  overflow: auto;
  padding-right: 10px;
}

.tree-title {
  padding: 10px;
}

::v-deep .el-tree-node__content {
  height: auto;
}

:deep(.is-current) {
  >.el-tree-node__content {
    font-weight: bold;
    color: var(--base-color);
    background-color: #F5F7FA;
  }
}

.param {
  height: 800px;
  display: flex;
}
</style>
