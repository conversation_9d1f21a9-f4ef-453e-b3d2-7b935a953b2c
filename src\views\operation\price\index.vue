<template>
  <div class="box">
    <div class="input_box elevation-4">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="$t(`backup['方案名称']`)" value="name"></el-option>
              <el-option :label="$t('创建人员')" value="createBy"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
            }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{ $t('添加方案') }}</el-button>
        </div>
      </div>
    </div>
    <el-row :gutter="12" class="card-cont" v-loading="loading" v-if="tableData.length">
      <el-col :span="12" v-for="item in tableData" :key="item.id" class="cont-item">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="card-header">
            <div class="card-header-title">{{ item.name }}</div>
            <div>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleEditClick(item)"
                :disabled="item.id == 1 && !$auth.hasRole('admin')">{{
                  $t('common.edit') }}</el-button>
              <el-button type="text" style="padding-top: 0;padding-bottom: 0;" @click="handleDeleteClick(item)"
                :disabled="item.id == 1 && !$auth.hasRole('admin')">{{
                  $t('common.delete') }}</el-button>
            </div>
          </div>
          <el-form :model="item" label-width="auto" class="cont-form" disabled>
            <el-form-item :label="$t('创建人员')" prop="createBy">
              {{ item.createBy }}
            </el-form-item>
            <el-form-item :label="$t('费率')">
                <el-row :gutter="10" type="flex" justify="space-between">
                  <template v-for="rateItem in item.rateList">
                    <el-col :span="4">
                      <el-input-number v-model="rateItem.value" :key="rateItem.value" :placeholder="rateItem.label"
                        :precision="2" class="input-number" style="width: 100%" />
                    </el-col>
                  </template>
                </el-row>
              </el-form-item>
              <el-form-item :label="$t(`price['买卖电是否同价']`)" prop="buySell">
                <el-radio-group v-model="item.buySell" style="width: 100%">
                  <el-radio :label="0">{{ $t('menu.yes') }}</el-radio>
                  <el-radio :label="1">{{ $t('menu.no') }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="item.buySell == 0">
                <el-form-item :label="`${$t(`price['时段']`)}${pointIndex1 + 1}：`" v-for="(pointItem1, pointIndex1) in item.pointList1"
                  :key="`${pointIndex1}-form`" prop="pointList1">
                  <el-row :gutter="10" type="flex" justify="space-between">
                    <el-col :span="6" style="padding-left: 0">
                      <el-form-item :prop="'pointList1.' + pointIndex1 + '.startTime'" :rules="{
                        required: true,
                        message: $t('sim.startTime')
                      }" label-width="0">
                        <el-time-select v-model="pointItem1.startTime" :placeholder="$t('sim.startTime')"
                          style="width: 100%;"></el-time-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="0.5" class="icon-box">~</el-col>
                    <el-col :span="6">
                      <el-form-item :prop="'pointList1.' + pointIndex1 + '.endTime'" :rules="{
                        required: true,
                        message: $t('sim.endTime')
                      }" label-width="0">
                        <el-time-select v-model="pointItem1.endTime" :picker-options="{
                          start: '00:00',
                          step: '01:00',
                          end: '24:00',
                        }" :placeholder="$t('sim.endTime')" style="width: 100%;"></el-time-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item :prop="'pointList1.' + pointIndex1 + '.type'" :rules="{
                        required: true,
                        message: $t('费率')
                      }" label-width="0">
                        <el-select v-model="pointItem1.type" :placeholder="$t('费率')" style="width: 100%;">
                          <template v-for="rateItem in item.rateList">
                            <el-option :key="rateItem.num" :label="rateItem.label" :value="rateItem.num" v-if="rateItem.value">
                            </el-option>
                          </template>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item :prop="'pointList1.' + pointIndex1 + '.price'" :rules="{
                        required: true,
                        message: $t(`price['电价']`)
                      }" label-width="0">
                        <el-input v-model="pointItem1.price" type="text" :placeholder="$t(`price['电价']`)" readonly></el-input>
                        <!-- <span class="suffix">{{ getCurrencyText(scope.row.countryCurrencyId) }} / kWh</span> -->
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item :label="$t(`price['买电']`)" prop="pointList">
                  <el-form-item label=" " v-for="(pointItem, pointIndex) in item.pointList" :key="`pointList-${pointIndex}-form`"
                    label-width="0px" class="sell-form-item">
                    <el-row :gutter="10" type="flex" justify="space-between">
                      <el-col :span="6" style="padding-left: 0">
                        <el-form-item :prop="'pointList.' + pointIndex + '.startTime'" :rules="{
                          required: true,
                          message: $t('sim.startTime')
                        }" label-width="0">
                          <el-time-select v-model="pointItem.startTime" :placeholder="$t('sim.startTime')"
                            style="width: 100%;"></el-time-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="0.5" class="icon-box">~</el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'pointList.' + pointIndex + '.endTime'" :rules="{
                          required: true,
                          message: $t('sim.endTime')
                        }" label-width="0">
                          <el-time-select v-model="pointItem.endTime" :picker-options="{
                            start: '00:00',
                            step: '01:00',
                            end: '24:00',
                          }" :placeholder="$t('sim.endTime')" style="width: 100%;"></el-time-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'pointList.' + pointIndex + '.type'" :rules="{
                          required: true,
                          message: $t('费率')
                        }" label-width="0">
                          <el-select v-model="pointItem.type" :placeholder="$t('费率')" style="width: 100%;">
                            <template v-for="rateItem in item.rateList">
                              <el-option :key="rateItem.num" :label="rateItem.label" :value="rateItem.num" v-if="rateItem.value">
                              </el-option>
                            </template>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'pointList.' + pointIndex + '.price'" :rules="{
                          required: true,
                          message: $t(`price['电价']`)
                        }" label-width="0">
                          <el-input v-model="pointItem.price" type="text" :placeholder="$t(`price['电价']`)"
                            readonly></el-input>
                          <!-- <span class="suffix">{{ getCurrencyText(scope.row.countryCurrencyId) }} / kWh</span> -->
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-form-item>
                <el-form-item :label="$t(`price['卖电']`)" prop="sellPointList">
                  <el-form-item label=" " v-for="(sellItem, sellIndex) in item.sellPointList"
                    :key="`sellPointList-${sellIndex}-form`" label-width="0px" class="sell-form-item">
                    <el-row :gutter="10" type="flex" justify="space-between">
                      <el-col :span="6" style="padding-left: 0">
                        <el-form-item :prop="'sellPointList.' + sellIndex + '.startTime'" :rules="{
                          required: true,
                          message: $t('sim.startTime')
                        }" label-width="0">
                          <el-time-select v-model="sellItem.startTime" :placeholder="$t('sim.startTime')"
                            style="width: 100%;"></el-time-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="0.5" class="icon-box">~</el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sellPointList.' + sellIndex + '.endTime'" :rules="{
                          required: true,
                          message: $t('sim.endTime')
                        }" label-width="0">
                          <el-time-select v-model="sellItem.endTime" :picker-options="{
                            start: '00:00',
                            step: '01:00',
                            end: '24:00',
                          }" :placeholder="$t('sim.endTime')" style="width: 100%;"></el-time-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sellPointList.' + sellIndex + '.type'" :rules="{
                          required: true,
                          message: $t('费率')
                        }" label-width="0">
                          <el-select v-model="sellItem.type" :placeholder="$t('费率')" style="width: 100%;">
                            <template v-for="rateItem in item.rateList">
                              <el-option :key="rateItem.num" :label="rateItem.label" :value="rateItem.num" v-if="rateItem.value">
                              </el-option>
                            </template>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sellPointList.' + sellIndex + '.price'" :rules="{
                          required: true,
                          message: $t(`price['电价']`)
                        }" label-width="0">
                          <el-input v-model="sellItem.price" type="text" :placeholder="$t(`price['电价']`)"
                            readonly></el-input>
                          <!-- <span class="suffix">{{ getCurrencyText(scope.row.countryCurrencyId) }} / kWh</span> -->
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-form-item>
              </template>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <el-empty :image-size="200" v-else></el-empty>
    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
      @pagination="getList" style="margin-top: 20px;text-align: right;" />


    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" :width="$convertPx(1000, 'rem')"
      :title="dialogTitle">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
        <el-form-item :label="$t(`backup['方案名称']`)" prop="name">
          <el-input v-model="form.name" :placeholder="$t(`backup['请输入方案名称']`)" />
        </el-form-item>
        <el-form-item :label="$t('费率')">
          <el-row :gutter="10" type="flex" justify="space-between">
            <template v-for="item in form.rateList">
              <el-col :span="6">
                <el-input-number v-model="item.value" :key="item.value" :placeholder="item.label" :precision="2"
                  @change="handleRateChange()" class="input-number" style="width: 100%;" />
              </el-col>
            </template>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t(`price['买卖电是否同价']`)" prop="buySell">
          <el-radio-group v-model="form.buySell" style="width: 100%">
            <el-radio :label="0">{{ $t('menu.yes') }}</el-radio>
            <el-radio :label="1">{{ $t('menu.no') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="dialog-add-wrapper">
          <template v-if="form.buySell == 0">
            <el-form-item :label="`${$t(`price['时段']`)}${index + 1}`" v-for="(item, index) in form.pointList1"
              :key="`${index}-form`" prop="pointList1">
              <el-row :gutter="10" type="flex" justify="space-between">
                <el-col :span="5" style="padding-left: 0">
                  <el-form-item :prop="'pointList1.' + index + '.startTime'" :rules="{
                    required: true,
                    message: $t('sim.startTime')
                  }" label-width="0">
                    <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                      :picker-options="limitTimeCom1(index, 'startTime')" :placeholder="$t('sim.startTime')"
                      style="width: 100%;"></el-time-picker>
                    <!-- <el-time-select v-model="item.startTime" :picker-options="limitTimeCom1(index, 'startTime')"
                      :placeholder="$t('sim.startTime')" style="width: 100%;"></el-time-select> -->
                  </el-form-item>
                </el-col>
                <el-col :span="0.5" class="icon-box">~</el-col>
                <el-col :span="5">
                  <el-form-item :prop="'pointList1.' + index + '.endTime'" :rules="{
                    required: true,
                    message: $t('sim.endTime')
                  }" label-width="0">
                    <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                      :picker-options="limitTimeCom1(index, 'endTime')" :placeholder="$t('sim.endTime')"
                      style="width: 100%;"></el-time-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item :prop="'pointList1.' + index + '.type'" :rules="{
                    required: true,
                    message: $t('费率')
                  }" label-width="0">
                    <el-select v-model="item.type" :placeholder="$t('费率')" style="width: 100%;"
                      @change="handleTypeChange(item.type, index)">
                      <template v-for="item in form.rateList">
                        <el-option :key="item.num" :label="item.label" :value="item.num" v-if="item.value">
                        </el-option>
                      </template>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item :prop="'pointList1.' + index + '.price'" :rules="{
                    required: true,
                    message: $t(`price['电价']`)
                  }" label-width="0">
                    <el-input v-model="item.price" type="text" :placeholder="$t(`price['电价']`)" readonly></el-input>
                    <!-- <span class="suffix">{{ getCurrencyText(form.countryCurrencyId) }} / kWh</span> -->
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-tooltip :content="$t('price.add')" placement="top">
                    <div class="icon-box" @click="handleTimeAdd1()"><i class="el-icon-circle-plus-outline icon"></i>
                    </div>
                  </el-tooltip>
                </el-col>
                <el-col :span="1">
                  <el-tooltip :content="$t('common.delete')" placement="top">
                    <div class="icon-box" @click="handleDeleteTime1(index)"><i class="el-icon-circle-close icon"></i>
                    </div>
                  </el-tooltip>
                </el-col>
              </el-row>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item :label="$t(`price['买电']`)" prop="pointList">
              <el-form-item label=" " v-for="(item, index) in form.pointList" :key="`pointList-${index}-form`"
                label-width="0px" class="sell-form-item">
                <el-row :gutter="10" type="flex" justify="space-between">
                  <el-col :span="5" style="padding-left: 0">
                    <el-form-item :prop="'pointList.' + index + '.startTime'" :rules="{
                      required: true,
                      message: $t('sim.startTime')
                    }" label-width="0">
                      <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                        :picker-options="limitTimeCom(index, 'startTime', 'pointList')"
                        :placeholder="$t('sim.startTime')" style="width: 100%;"></el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="0.5" class="icon-box">~</el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'pointList.' + index + '.endTime'" :rules="{
                      required: true,
                      message: $t('sim.endTime')
                    }" label-width="0">
                      <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                        :picker-options="limitTimeCom(index, 'endTime', 'pointList')" :placeholder="$t('sim.endTime')"
                        style="width: 100%;"></el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'pointList.' + index + '.type'" :rules="{
                      required: true,
                      message: $t('费率')
                    }" label-width="0">
                      <el-select v-model="item.type" :placeholder="$t('费率')" style="width: 100%;"
                        @change="handleTypeChange(item.type, index, 'pointList')">
                        <template v-for="item in form.rateList">
                          <el-option :key="item.num" :label="item.label" :value="item.num" v-if="item.value">
                          </el-option>
                        </template>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'pointList.' + index + '.price'" :rules="{
                      required: true,
                      message: $t(`price['电价']`)
                    }" label-width="0">
                      <el-input v-model="item.price" type="text" :placeholder="$t(`price['电价']`)" readonly></el-input>
                      <!-- <span class="suffix">{{ getCurrencyText(form.countryCurrencyId) }} / kWh</span> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="1">
                    <el-tooltip :content="$t('price.add')" placement="top">
                      <div class="icon-box" @click="handleTimeAdd('pointList')"><i
                          class="el-icon-circle-plus-outline icon"></i>
                      </div>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="1">
                    <el-tooltip :content="$t('common.delete')" placement="top">
                      <div class="icon-box" @click="handleDeleteTime(index, 'pointList')"><i
                          class="el-icon-circle-close icon"></i>
                      </div>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form-item>
            <el-form-item :label="$t(`price['卖电']`)" prop="sellPointList">
              <el-form-item label=" " v-for="(item, index) in form.sellPointList" :key="`sellPointList-${index}-form`"
                label-width="0px" class="sell-form-item">
                <el-row :gutter="10" type="flex" justify="space-between">
                  <el-col :span="5" style="padding-left: 0">
                    <el-form-item :prop="'sellPointList.' + index + '.startTime'" :rules="{
                      required: true,
                      message: $t('sim.startTime')
                    }" label-width="0">
                      <el-time-picker v-model="item.startTime" value-format="HH:mm" format="HH:mm"
                        :picker-options="limitTimeCom(index, 'startTime', 'sellPointList')"
                        :placeholder="$t('sim.startTime')" style="width: 100%;"></el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="0.5" class="icon-box">~</el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'sellPointList.' + index + '.endTime'" :rules="{
                      required: true,
                      message: $t('sim.endTime')
                    }" label-width="0">
                      <el-time-picker v-model="item.endTime" value-format="HH:mm" format="HH:mm"
                        :picker-options="limitTimeCom(index, 'endTime', 'sellPointList')"
                        :placeholder="$t('sim.endTime')" style="width: 100%;"></el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'sellPointList.' + index + '.type'" :rules="{
                      required: true,
                      message: $t('费率')
                    }" label-width="0">
                      <el-select v-model="item.type" :placeholder="$t('费率')" style="width: 100%;"
                        @change="handleTypeChange(item.type, index, 'sellPointList')">
                        <template v-for="item in form.rateList">
                          <el-option :key="item.num" :label="item.label" :value="item.num" v-if="item.value">
                          </el-option>
                        </template>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="'sellPointList.' + index + '.price'" :rules="{
                      required: true,
                      message: $t(`price['电价']`)
                    }" label-width="0">
                      <el-input v-model="item.price" type="text" :placeholder="$t(`price['电价']`)" readonly></el-input>
                      <!-- <span class="suffix">{{ getCurrencyText(form.countryCurrencyId) }} / kWh</span> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="1">
                    <el-tooltip :content="$t('price.add')" placement="top">
                      <div class="icon-box" @click="handleTimeAdd('sellPointList')"><i
                          class="el-icon-circle-plus-outline icon"></i></div>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="1">
                    <el-tooltip :content="$t('common.delete')" placement="top">
                      <div class="icon-box" @click="handleDeleteTime(index, 'sellPointList')"><i
                          class="el-icon-circle-close icon"></i></div>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form-item>
          </template>
        </div>
      </el-form>
      <span>{{ $t('注：') }} {{ $t('电价：电价货币为项目绑定的货币为准，如项目绑定货币为美元，那么电价为0.2美元') }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('formRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import _ from 'lodash'
import { priceList, addPrice, editPrice, deletePrice } from '@/api/operation/price'
import { allCurrency } from '@/api/operation/currency'

export default {
  data() {
    return {
      dialogVisible: false,
      dialogTitle: this.$t(`backup['添加方案']`),
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        name: undefined
      },
      // 表单
      form: {
        name: '',
        buySell: 0,
        pointList: [],
        pointList1: [],
        sellPointList: [],
        rateList: []
      },
      rateList: [
        {
          label: this.$t('费率一'),
          num: 0,
          value: undefined
        },
        {
          label: this.$t('费率二'),
          num: 1,
          value: undefined
        },
        {
          label: this.$t('费率三'),
          num: 2,
          value: undefined
        },
        {
          label: this.$t('费率四'),
          num: 3,
          value: undefined
        },
        {
          label: this.$t('费率五'),
          num: 4,
          value: undefined
        },
      ],
      rules: {
        name: [
          { required: true, message: this.$t(`backup['请输入方案名称']`), trigger: 'blur' }
        ],
        countryCurrencyId: [
          { required: true, message: this.$t('common.select'), trigger: 'blur' }
        ],
        pointList: [
          { required: true, message: this.$t(`price['请添加时段']`), trigger: 'blur' }
        ],
        pointList1: [
          { required: true, message: this.$t(`price['请添加时段']`), trigger: 'blur' }
        ],
        sellPointList: [
          { required: true, message: this.$t(`price['请添加时段']`), trigger: 'blur' }
        ],
        buySell: [
          { required: true, message: this.$t(`price['请选择买卖电是否同价']`), trigger: 'blur' }
        ],
      },
      pointList: [],
      currencyOptions: [],
      rateTextList: [this.$t('费率一'), this.$t('费率二'), this.$t('费率三'), this.$t('费率四'), this.$t('费率五')],
      pointListExample: {
        enable: 0,
        endTime: '',
        price: undefined,
        startTime: '',
        type: undefined
      },
      // 搜索
      searchKey: 'name',
      searchValue: '',
    };
  },
  computed: {
    limitTimeCom() {
      return (index, type, listType) => {
        let options = {
          start: '00:00',
          step: '01:00',
          end: '24:00',
          minTime: undefined,
          selectableRange: '00:00:00 - 23:59:00'
        }
        if (index) {
          if (type == 'startTime') {
            options.minTime = listType == 'pointList' ? this.form.pointList[index - 1].endTime : this.form.sellPointList[index - 1].endTime
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
          if (type == 'endTime') {
            options.minTime = listType == 'pointList' ? this.form.pointList[index].startTime : this.form.sellPointList[index].startTime
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
        } else {
          if (type == 'startTime') options.minTime = undefined
          if (type == 'endTime') {
            options.minTime = listType == 'pointList' ? this.form.pointList[0].startTime : this.form.sellPointList[0].startTime
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
        }
        return options
      }
    },
    limitTimeCom1() {
      return (index, type) => {
        let options = {
          start: '00:00',
          step: '01:00',
          end: '24:00',
          minTime: undefined,
          selectableRange: '00:00:00 - 23:59:00'
        }
        if (index) {
          if (type == 'startTime') {
            // options.minTime = this.$moment(this.form.pointList1[index - 1].endTime, 'HH:mm').subtract(1, 'hours').format('HH:mm')
            options.minTime = this.form.pointList1[index - 1].endTime
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
          if (type == 'endTime') {
            options.minTime = this.form.pointList1[index].startTime
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
        } else {
          if (type == 'startTime') options.minTime = undefined
          if (type == 'endTime') {
            options.minTime = this.form.pointList1[0].startTime || '00:00'
            options.selectableRange = `${options.minTime}:00 - 23:59:00`
          }
        }
        return options
      }
    },
    getCurrencyText() {
      return (id) => {
        return this.currencyOptions.find(item => item.id == id)?.currency
      }
    },
    getPriceText() {
      return (num) => {
        return this.form.rateList.find(item => item.num == num)?.value
      }
    }
  },
  mounted() {
    this.getList()
    // this.getCurrencyOptions()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      priceList({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        [this.searchKey]: this.searchValue
      }).then(res => {
        let data = res.rows
        data.forEach(item => {
          if (item.buySell == 0) {
            item.pointList1 = item.pointList
            item.sellPointList = [{ ...this.pointListExample }]
          } else {
            item.pointList1 = [{ ...this.pointListExample }]
            item.sellPointList = item.sellPointList ? item.sellPointList : [{ ...this.pointListExample }]
          }
          item.rateList = item.rateList ? item.rateList.map(item1 => {
            item1.label = this.rateTextList.find((text, index) => index == item1.num)
            return item1
          }) : _.cloneDeep(this.rateList)
        })
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == this.$t(`backup['添加方案']`)) { // 添加
            this.addPriceFn()
          } else { // 修改
            this.editPriceFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogTitle = this.$t(`backup['添加方案']`)
      this.dialogVisible = true;
      this.form = {
        name: '',
        countryCurrencyId: '',
        buySell: 0,
        pointList: [{ ...this.pointListExample }],
        pointList1: [{ ...this.pointListExample }],
        sellPointList: [{ ...this.pointListExample }],
        rateList: _.cloneDeep(this.rateList)
      }
      this.$nextTick(() => {
        this.resetForm('form')
      })
    },
    // 修改
    handleEditClick(row) {
      this.dialogTitle = this.$t(`backup['修改方案']`)
      this.dialogVisible = true;
      let data = _.cloneDeep(row)
      if (data.buySell == 0) {
        data.pointList1 = data.pointList
        data.sellPointList = [{ ...this.pointListExample }]
      } else {
        data.pointList1 = [{ ...this.pointListExample }]
        data.sellPointList = data.sellPointList ? data.sellPointList : [{ ...this.pointListExample }]
      }
      this.form = {
        ...data,
        rateList: data.rateList.length ? data.rateList.map(item => {
          item.label = this.rateTextList.find((text, index) => index == item.num)
          return item
        }) : _.cloneDeep(this.rateList)
      }
    },
    addPriceFn() {
      let pointList = this.form.buySell == 0 ? this.form.pointList1 : this.form.pointList
      addPrice({
        name: this.form.name,
        countryCurrencyId: this.form.countryCurrencyId,
        buySell: this.form.buySell,
        sellPointList: this.form.sellPointList,
        pointList,
        rateList: this.form.rateList
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Addition Failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Added successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    editPriceFn() {
      let pointList = this.form.buySell == 0 ? this.form.pointList1 : this.form.pointList
      let sellPointList = this.form.buySell == 0 ? [] : this.form.sellPointList
      editPrice({
        name: this.form.name,
        countryCurrencyId: this.form.countryCurrencyId,
        buySell: this.form.buySell,
        sellPointList,
        pointList,
        rateList: this.form.rateList,
        id: this.form.id
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Change failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Modify successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deletePrice({
          electricIds: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    handleTimeAdd(type) {
      let length = type == 'sellPointList' ? this.form.sellPointList.length : this.form.pointList.length
      if (length == 12) return this.$message({
        type: 'error',
        message: `${this.$t(`price['最多只能添加12条哦']`)}~`
      })
      let list = type == 'sellPointList' ? this.form.sellPointList : this.form.pointList
      list.push({ ...this.pointListExample })
    },
    handleTimeAdd1() {
      if (this.form.pointList1.length == 12) return this.$message({
        type: 'error',
        message: `${this.$t(`price['最多只能添加12条哦']`)}~`
      })
      this.form.pointList1.push({ ...this.pointListExample })
    },
    handleDeleteTime(index, type) {
      let length = type == 'sellPointList' ? this.form.sellPointList.length : this.form.pointList.length
      if (length == 1) return this.$message({
        type: 'error',
        message: `${this.$t(`price['至少要有一条哦']`)}~`
      })
      let list = type == 'sellPointList' ? this.form.sellPointList : this.form.pointList
      list.splice(index, 1)
    },
    handleDeleteTime1(index) {
      if (this.form.pointList1.length == 1) return this.$message({
        type: 'error',
        message: `${this.$t(`price['至少要有一条哦']`)}~`
      })
      this.form.pointList1.splice(index, 1)
    },
    // 获取货币
    getCurrencyOptions() {
      allCurrency().then(res => {
        this.currencyOptions = res.data
      })
    },
    handleTypeChange(type, index, form) {
      if (this.form.buySell == 0) {
        this.form.pointList1[index].price = this.getPriceText(type)
      } else {
        this.form[form][index].price = this.getPriceText(type)
      }
    },
    handleRateChange() {
      if (this.form.buySell == 0) {
        this.form.pointList1.forEach(item => {
          item.price = this.getPriceText(item.type)
        })
      } else {
        this.form.pointList.forEach(item => {
          item.price = this.getPriceText(item.type)
        })
        this.form.sellPointList.forEach(item => {
          item.price = this.getPriceText(item.type)
        })
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;

  .input_box {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    &-title {
      width: 70%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .card-cont {
    /* max-height: 840px; */
    height: calc(100% - 80px);
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
  }

  .cont-item {
    margin-bottom: 20px;
    flex: 0 0 auto;
  }

  .cont-form {
    width: 100%;
    height: 310px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 10px;
  }

  .icon-box {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .icon {
    font-size: 28px;
    display: block;
  }
}

.el-form-item .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-dialog__body {
  padding-right: 10px !important;
}

.dialog-add-wrapper {
  max-height: 400px;
  overflow: auto;
  padding-right: 20px;
  margin-bottom: 20px;
}

.sell-form-item {
  :deep(.el-form-item__label) {
    display: none;
  }
}
:deep(.el-card) {
  border: thin solid rgb(204, 204, 204);
  box-shadow: none !important;
  .el-card__header {
    border-bottom: thin solid rgb(204, 204, 204) !important;
  }
  .el-card__body, .el-main {
    padding-bottom: 0 !important;
  }
}
</style>
