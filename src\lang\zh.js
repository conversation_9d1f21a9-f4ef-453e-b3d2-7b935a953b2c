// zh.js
export default {
  login: {
    title: '亿兰科云平台',
    logIn: '登录',
    username: '账号',
    password: '密码',
    code: '验证码',
    vUserName: '请输入您的账号',
    vPassword: '请输入您的密码',
    vCode: '请输入验证码',
    rememberPwd: '记住密码',
    loging: '登录中',
    company: '深圳亿兰科电气有限公司 版权所有',
    logOut: '确定退出系统吗？'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有',
    home: '首页'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo',
    screen: '全屏'
  },
  // 数据概括
  home: {
    title: '数据概括',
    item1: '总装机容量',
    item2: '总装机功率',
    item3: '累计充电量',
    item4: '累计放电量',
    pieTitle: '故障比例',
    pieTitle: '故障比例',
    tableTitle: '项目汇总',
    pieRadio1: '电站',
    pieRadio2: '设备',
    pieRadioText1: '电站总数',
    pieRadioText2: '设备总数',
    '总收益': '总收益'
  },
  common: {
    search: '搜索',
    confirm: '确定',
    cancel: '取消',
    systemPrompt: '系统提示',
    normal: '正常',
    fault: '故障',
    edit: '修改',
    delete: '删除',
    add: '新建',
    reset: '重置',
    handle: '操作',
    id: '序号',
    createTime: '添加时间(UTC+08:00)',
    status: '状态',
    unit: '单位',
    logout: '注销',
    detailAddress: '详细地址',
    'Please enter': '请输入',
    offline: '离线',
    online: '在线',
    all: '全部',
    save: '保存',
    'Added successfully': '添加成功！',
    'Addition Failed': '添加失败！',
    'Modify successfully': '修改成功！',
    'Change failed': '修改失败！',
    'Deleted successfully': '删除成功！',
    'Deleted Failed': '删除失败！',
    'Deletion Cancelled': '已取消删除',
    select: '请选择',
    check: '查看',
    TurnOn: '开启',
    Closure: '关闭',
    GridConnection: '并网',
    OffGrid: '离网',
    Shutdown: '停机',
    running: '运行',
    'Confirm addition': '确认添加',
    submit: '提交',
    back: '返回',
    sele: '选择',
    BatteryStartable: '电池可启动',
    BatteryNoStartable: '电池不可启动',
    Discharge: '放电',
    Charge: '充电',
    Enable: '启用',
    Deactivate: '停用',
    Closure2: '闭合',
    switchOff: '断开',
    export: '导出',
    exportReport: '导出报表',
    '时区': '时区',
    '时区地址': '时区地址',
    '停止': '停止'
  },
  date: {
    week: '周',
    month: '月',
    year: '年',
    day: '日',
    lastWeek: '最近一周',
    lastMonth: '最近一个月',
    last3Month: '最近三个月',
    to: '至',
    start: '开始日期',
    end: '开始日期'
  },
  DataSummary: '数据概括',
  faultMessage: '故障信息',
  // 设备监控
  monitor: {
    title: '设备监控',
    topItem1: '电池装机容量',
    topItem2: '额定功率',
    topItem3: '系统运行状态',
    topItem4: '储能充电量',
    topItem5: '储能放电量',
    topItem6: '光伏装机容量',
    topItem7: '光伏发电量',
    flowItem1: '电网功率',
    flowItem2: '光伏功率',
    flowItem3: '负载功率',
    flowItem4: '电池功率',
    flowItem5: '直流母线功率',
    lineTitle: '功率分析',
    barTitle: '电量统计',
    barItem1: '充电量',
    barItem2: '放电量',
    Photovoltaic: '光伏',
    energyStorage: '储能',
    PhotovoltaicStorage: '光储',
    voltage: '电压',
    temperature: '温度',
    cell: '电芯',
    ratedCapacity: '额定容量',
    OnlineStatus: '在线状态',
    WorkingStatus: '工作状态',
    'Battery rechargeable': '电池可充电',
    'Battery rechargeable no': '电池不可充电',
    'Battery dischargeable': '电池可放电',
    'Battery dischargeable no': '电池不可放电',
    // 本地控制器
    control: '本地控制器',
    deviceInfo: '设备信息',
    jk_1001Bit1AndBit2: '系统工作模式',
    jk_1002: '系统故障状态',
    jk_1005: '系统告警状态',
    jk_1001Bit14: '并离网状态',
    jk_1092: 'AC运行状态',
    jk_1093: 'DC运行状态',
    jk_1001Bit3AndBit4Bit5: '系统状态',
    jk_1004: '电池状态',
    jk_1094: '电池高压状态',
    onLineState: '通讯状态',
    jk_1095: '空调状态',
    water: '水浸状态',
    smoke: '烟感状态',
    thunder: '防雷',
    fire: '消防动作',
    jk_1031: '储能功率',
    version: '软件版本',
    baseInfo: '基本信息',
    'Total reactive power of the grid': '电网总无功功率',
    'Total active power of the grid': '电网总有功功率',
    'Grid Frequency A': '电网频率A',
    'Grid Frequency B': '电网频率B',
    'Grid Frequency C': '电网频率C',
    highV1: '未上高压',
    highV2: '上高压',
    Blowers: '风机',
    Cooling: '制冷',
    HeatUp: '制热',
    Dehumidify: '除湿',
    '策略状态': '策略状态',
    '未使用': '未使用',
    '未运行': '未运行',
    // AC
    ac_2010: '工作模式',
    ac_2015Bit5: '充放电',
    ac_2015Bit6: '运行状态',
    ac_2015Bit7: '故障状态',
    ac_2039: '直流电压',
    ac_2040: '直流电流',
    ac_2041: '直流功率',
    ac_2042: '线电压Vab',
    ac_2043: '线电压Vbc',
    ac_2044: '线电压Vca',
    ac_2045: 'A相电流',
    ac_2046: 'B相电流',
    ac_2047: 'C相电流',
    ac_2049: '有功功率',
    ac_2050: '无功功率',
    ac_2061: '视在功率',
    ac_2048: '频率',
    ac_2056: 'igbt温度',
    ac_2051: '功率因数',
    ac_2057: '正极对地阻抗值',
    ac_2058: '负极对地阻抗值',
    acVersion: '版本号',
    // DC
    dcJoin: '接入设备',
    dc_3012: 'DC低压侧1路电压',
    dc_3013: 'DC低压侧1路电流',
    dc_3014: 'DC低压侧1路功率',
    dc_3015: 'DC低压侧2路电压',
    dc_3016: 'DC低压侧2路电流',
    dc_3017: 'DC低压侧2路功率',
    dc_3018: 'DC高压侧电压',
    dc_3019: 'DC高压侧电流',
    dc_3020: 'DC高压侧功率',
    highType: '高压侧类型',
    lowType: '低压侧类型',
    Lbattery: '锂电池',
    DCbusbar: '直流母线',
    DCsource: '直流源',
    '铅酸电池': '铅酸电池',
    // STS
    sts_3500: '通信状态',
    deviceStatus: '设备状态',
    sts_3507: '电网电压A(AB)',
    sts_3508: '电网电压B(BC)',
    sts_3509: '电网电压C(CA)',
    sts_3511: '电网电流A',
    sts_3512: '电网电流B',
    sts_3513: '电网电流C',
    sts_3519: '电网有功A',
    sts_3520: '电网有功B',
    sts_3521: '电网有功C',
    sts_3523: '电网无功A',
    sts_3524: '电网无功B',
    sts_3525: '电网无功C',
    sts_3522: '电网总有功',
    sts_3526: '电网总无功',
    sts_3534: '电网总视在功率',
    sts_3531: '电网视在功率A',
    sts_3532: '电网视在功率B',
    sts_3533: '电网视在功率C',
    sts_3515: '负载电压A(AB)',
    sts_3516: '负载电压B(BC)',
    sts_3517: '负载电压C(CA)',
    sts_3530: '总功率因数',
    sts_3518: '电网频率',
    // BMS
    bmsCS: 'BMS通信状态',
    bms_4020: '电池电压',
    bms_4021: '电池电流',
    bms_4036: '电池可充电量',
    bms_4030: '充电限制电流',
    bms_4031: '放电限制电流',
    bms_4037: '电池可放电量',
    bms_4025: '最小单体电压',
    bms_4024: '最大单体电压',
    bms_4026: '平均单体电压',
    bms_4027: '最高单体温度',
    bms_4028: '最低单体温度',
    bms_4029: '平均单体温度',
    bms_4043: '最低单体电压位置',
    bms_4041: '最高单体电压位置',
    bms_4054: '电芯压差',
    bms_4045: '最高单体温度位置',
    bms_4047: '最低单体温度位置',
    bms_4053: '电芯温差',
    bms_4038: '环境温度(电池仓)',
    bms_4055: '环境湿度(电池仓)',
    bms_4039: '绝缘阻抗值',
    // 电表
    amTitle: '电表',
    // 电芯
    cell: '电芯',
    '主机': '主机',
    '从机': '从机',
    '组合类型': '组合类型',
    '组合序列号': '组合序列号',
    '组合光储系统': '组合光储系统',
    '组合光伏控制系统': '组合光伏控制系统',
    '组合储能系统': '组合储能系统',
  },
  project: {
    title: '项目管理',
    name: '项目名称',
    address: '项目地址',
    area: '区域',
    lonAndLat: '经纬度',
    sn: '设备SN码',
    logOutHint: '此操作将永久删除该项目，该项目的设备记录将保留，如要删除设备，请到设备入库删除！ 是否继续?',
    logOutCHint: '已取消注销',
    'Please enter project name': '请输入项目名称',
    'Add item': '添加项目',
    'Modify Project': '修改项目',
    'Please enter full address': '请输入详细地址',
    'Please enter project address': '请输入项目地址',
    'Please enter longitude': '请输入经度',
    'Please enter latitude': '请输入纬度',
  },
  device: {
    title: '设备入库',
    name: '设备名称',
    screenId: '整机序列号',
    model: '设备型号',
    type: '设备类型',
    cellCapacity: '电池容量',
    software: '出厂软件版本',
    hardware: '出厂硬件版本',
    CustomerOrder: '客户单号',
    order: '订单号',
    orderDetail: '订单特殊说明',
    module: '模块序列号',
    power: '实时功率(kW)',
    version: '协议版本',
    info: '设备信息',
    address: '设备地址',
    sn: '序列号',
    selectDeviceTitle: '选择设备',
    deleteHint: '此操作将永久删除该项目，该项目的设备记录将保留，如要删除设备，请到设备入库删除！ 是否继续?',
    deleteDeviceHint: '此操作将永久注销该设备, 是否继续?',
    'Please enter device serial number': '请输入设备序列号',
    'Please enter machine serial number (screen)': '请输入整机序列号',
    'Please enter device name': '请输入设备名称',
    'Please enter device model': '请输入设备型号',
    'Please enter photovoltaic installed capacity': '请输入光伏装机容量',
    'Please enter battery capacity': '请输入电池容量',
    'Please enter rated power': '请输入额定功率',
    'Please enter factory software version': '请输入出厂软件版本',
    'Please enter factory hardware version': '请输入出厂硬件版本',
    'Please enter order number': '请输入订单号',
    'Please enter special instructions for orders': '请输入订单说明',
    'Please enter module serial number': '请输入模块序列号',
    'Please enter customer order number': '请输入客户单号',
    AddDevice: '添加设备',
    ModifyDevice: '修改设备',
    QueryingDevice: '查询设备',
    exceed: '超过10个'
  },
  // 告警
  alarm: {
    peStatus: '未处理',
    prStatus: '已处理',
    level: '告警等级',
    sn: '设备序列号',
    status: '告警状态',
    time: '发生时间',
    reporting: '上报时间(UTC+08:00)',
    name: '告警名称',
    object: '告警对象',
    belong: '所属项目',
    'Please enter alarm name': '请输入告警名称',
    level1: '等级一',
    level2: '等级二',
    dispose: '处理',
    'Processed successfully!': '处理成功！',
    'Are you sure you want to handle this exception manually?': '确定要手动处理这条异常吗？',
    title: '告警'
  },
  PersonalCenter: '个人中心',
  SignOut: '退出登录',
  outLint: '此操作将永久注销该设备，是否继续？',
  '设备详情': '设备详情',
  // 网络
  axios: {
    limit: '请求数据大小超出允许的5M限制，无法进行防重复提交验证。',
    repetition: '数据正在处理，请勿重复提交',
    date: '登录状态已过期，您可以继续留在该页面，或者重新登录',
    anew: '重新登录',
    invalid: '无效的会话，或者会话已过期，请重新登录。',
    error: '后端接口连接异常',
    timeout: '系统接口请求超时',
    interface: '系统接口',
    errorText: '异常',
    being: '正在下载数据，请稍候',
    downloadError: '下载文件出现错误，请联系管理员！'
  },
  // 用户管理
  user: {
    name: '用户名称',
    nickname: '用户昵称',
    phone: '手机号码',
    id: '用户编号',
    dept: '部门',
    handlePwd: '重置密码',
    handleRole: '分配角色',
    post: '岗位',
    belongDept: '归属部门',
    'Please enter user name': '请输入用户名称',
    'Please enter user nickname': '请输入用户昵称',
    'Please enter phone number': '请输入手机号码',
    'Please enter email': '请输入邮箱',
    'Please enter user password': '请输入用户密码',
    'Please enter content': '请输入内容',
    'Please select post': '请选择岗位',
    'Please select belonging department': '请选择归属部门',
    'Please select role': '请选择角色',
    userState: '用户状态',
    pwd: '用户密码',
    role: '角色',
    remark: '备注',
    email: '邮箱',
    drag: '将文件拖到此处，',
    or: '或',
    click: '点击上传',
    update: '是否更新已经存在的用户数据',
    limit: '仅允许导入xls、xlsx格式文件。',
    template: '下载模板',
    'Username cannot be empty': '用户名称不能为空',
    'Nickname cannot be empty': '用户昵称不能为空',
    'User password cannot be empty': '用户密码不能为空',
    'Username length must be between 2 and 20': '用户名称长度必须介于 2 和 20 之间',
    'User password length must be between 5 and 20': '用户密码长度必须介于 5 和 20 之间',
    'Please enter a valid email address': '请输入正确的邮箱地址',
    'Please enter a valid mobile phone number': '请输入正确的手机号码',
    'Please enter your new password': '请输入新密码',
    importUser: '用户导入',
    importResult: '导入结果',
    personInfo: '个人信息',
    userEmail: '用户邮箱',
    belongRole: '所属角色',
    createDate: '创建日期',
    changePwd: '修改密码',
    oldPassword: '旧密码',
    newPassword: '新密码',
    confirmPassword: '确认密码',
    two: '两次输入的密码不一致',
    'Please enter your old password': '请输入旧密码',
    'Please confirm the new password': '请确认新密码',
    'Old password cannot be empty': '旧密码不能为空',
    'New password cannot be empty': '新密码不能为空',
    'Confirm password cannot be empty': '确认密码不能为空',
    '6 to 20 characters in length': '长度在 6 到 20 个字符',
    '5 to 20 characters in length': '长度在 5 到 20 个字符',
    clickUploadAvatar: '点击上传头像',
    editAvatar: '修改头像',
    formatError: '文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。'
  },
  'Data Analysis': '数据分析',
  // 角色管理
  role: {
    status: '角色状态',
    id: '角色编号',
    name: '角色名称',
    chart: '权限字符',
    show: '显示顺序',
    data: '数据权限',
    user: '分配用户',
    expand: '展开/折叠',
    scope: '权限范围',
    'Please enter role name': '请输入角色名称',
    'Please enter permission characters': '请输入权限字符',
    order: '角色顺序',
    perm: '菜单权限',
    select: '全选/全不选',
    father: '父子联动',
    loading: '加载中，请稍后',
    all: '全部数据权限',
    custom: '自定数据权限',
    ownDept: '本部门数据权限',
    ownDeptDown: '本部门及以下数据权限',
    own: '仅本人数据权限',
    'Role name cannot be empty': '角色名称不能为空',
    'Permission Characters cannot be empty': '权限字符不能为空',
    'Role Order cannot be empty': '角色顺序不能为空',
    addRole: '添加角色',
    editRole: '修改角色',
    assign: '分配数据权限',
    'Permission characters defined in the controller, such as:': '控制器中定义的权限字符，如：'
  },
  // 数据对比
  contrast: {
    jk_1025: '储能侧电压A(AB)',
    jk_1026: '储能侧电压 B(BC)',
    jk_1027: '储能侧电压 C(CA)',
    jk_1028: '储能侧 A 相电流',
    jk_1029: '储能侧 B 相电流',
    jk_1030: '储能侧 C 相电流',
    jk_1031: '储能侧A相有功功率',
    jk_1032: '储能侧B相有功功率',
    jk_1033: '储能侧C相有功功率',
    jk_1034: '储能侧A相无功功率',
    jk_1035: '储能侧B相无功功率',
    jk_1036: '储能侧C相无功功率',
    jk_1037: '储能侧频率 A',
    jk_1038: '储能侧频率 B',
    jk_1039: '储能侧频率 C',
    jk_1040: '储能侧总功率因数',
    jk_1041: '电网电压A(AB)',
    jk_1042: '电网电压B(BC)',
    jk_1043: '电网电压C(CA)',
    jk_1044: '交流A相电流',
    jk_1045: '交流B相电流',
    jk_1046: '交流C相电流',
    jk_1047: '电网频率A',
    jk_1048: '电网频率B',
    jk_1049: '电网频率C',
    jk_1050: '总功率因数',
    jk_1051: '电网总无功功率',
    jk_1052: '电网总有功功率',
    jk_1053: '电网总视在功率',
    jk_1072: '光伏电压',
    jk_1073: '光伏电流',
    jk_1074: '光伏功率',
    bms_4038: '仓内环境温度',
    bms_4055: '仓内环境湿度',
    bms_4027: '电池单体最高温度',
    bms_4028: '电池单体最低温度',
    bms_4024: '电池单体最大电压',
    bms_4025: '电池单体最低电压',
    bms_4022: '电池电量SOC',
    bms_4020: '电池电压',
    bms_4021: '电池电流',
    bms_4056: '电池功率',
    bms_4030: '充电限制电流',
    bms_4031: '放电限制电流',
    param: '参数',
    AC_2039: '直流电压',
    AC_2040: '直流电流',
    AC_2041: '直流功率',
    AC_2042: '线电压Vab',
    AC_2043: '线电压Vbc',
    AC_2044: '线电压Vca',
    AC_2045: 'A相电流',
    AC_2046: 'B相电流',
    AC_2047: 'C相电流',
    AC_2049: '有功功率',
    AC_2050: '无功功率',
    AC_2061: '视在功率',
    AC_2048: '频率',
    AC_2056: 'igbt温度',
    AC_2051: '功率因数',
    DC_3012: 'DC低压侧1路电压',
    DC_3013: 'DC低压侧1路电流',
    DC_3014: 'DC低压侧1路功率',
    DC_3015: 'DC低压侧2路电压',
    DC_3016: 'DC低压侧2路电流',
    DC_3017: 'DC低压侧2路功率',
    DC_3018: 'DC高压侧电压',
    DC_3019: 'DC高压侧电流',
    DC_3020: 'DC高压侧功率',
    DC_3025: 'igbt温度',
    em_10003: '电表在线状态',
    em_10005: 'A相电压',
    em_10006: 'B相电压',
    em_10007: 'C相电压',
    em_10008: 'A相电流',
    em_10009: 'B相电流',
    em_10010: 'C相电流',
    em_10011: '功率因数',
    em_10012: '有功功率',
    em_10013: '无功功率',
    em_10014: '视在功率',
    em_10015: '正向有功电度',
    em_10016: '反向有功电度',
    em_10017: '正向无功电度',
    em_10018: '反向无功电度',
    em_10019: '峰正向有功电度',
    em_10020: '峰反向有功电度',
    em_10021: '峰正向无功电度',
    em_10022: '峰反向无功电度',
    em_10023: '谷正向有功电度',
    em_10024: '谷反向有功电度',
    em_10025: '谷正向无功电度',
    em_10026: '谷反向无功电度',
    em_10027: '平正向有功电度',
    em_10028: '平反向有功电度',
    em_10029: '平正向无功电度',
    em_10030: '平反向无功电度',
    em_10031: '尖正向有功电度',
    em_10032: '尖反向有功电度',
    em_10033: '尖正向无功电度',
    em_10034: '尖反向无功电度',
    em_10035: '频率',
    em_10036: '正向总电量',
    em_10037: '反向总电量',
    em_10038: '直流电压',
    em_10039: '直流电流',
    em_10040: '直流功率',
    em_10041: 'AB线电压',
    em_10042: 'BC线电压',
    em_10043: 'CA线电压',
    chargingPile_19003: '总输出功率',
    chargingPile_19004: '1#枪输出电压',
    chargingPile_19005: '1#枪输出电流',
    chargingPile_19006: '1#枪输出功率',
    chargingPile_19007: '1#枪当前SOC',
    chargingPile_19008: '1#枪充电电量',
    chargingPile_19009: '1#枪充电金额',
    chargingPile_19010: '1#枪电池最高温度',
    chargingPile_19015: '2#枪输出电压',
    chargingPile_19016: '2#枪输出电流',
    chargingPile_19017: '2#枪输出功率',
    chargingPile_19018: '2#枪当前SOC',
    chargingPile_19019: '2#枪充电电量',
    chargingPile_19020: '2#枪充电金额',
    chargingPile_19021: '2#枪电池最高温度',
    sts_3507: '电网电压A(AB)',
    sts_3508: '电网电压B(BC)',
    sts_3509: '电网电压C(CA)',
    sts_3510: '变压器温度',
    sts_3511: '电网电流A',
    sts_3512: '电网电流B',
    sts_3513: '电网电流C',
    sts_3514: '环境温度',
    sts_3515: '负载电压A(AB)',
    sts_3516: '负载电压B(BC)',
    sts_3517: '负载电压C(CA)',
    sts_3518: '电网频率',
    sts_3519: '电网有功A',
    sts_3520: '电网有功B',
    sts_3521: '电网有功C',
    sts_3522: '电网总有功',
    sts_3523: '电网无功A',
    sts_3524: '电网无功B',
    sts_3525: '电网无功C',
    sts_3526: '电网总无功',
    sts_3527: '功率因素A',
    sts_3528: '功率因素B',
    sts_3529: '功率因素C',
    sts_3530: '总功率因素',
    sts_3531: '电网视在功率A',
    sts_3532: '电网视在功率B',
    sts_3533: '电网视在功率C',
    sts_3534: '电网总视在功率',
  },
  // 菜单管理
  menu: {
    status: '菜单状态',
    name: '菜单名称',
    EnglishName: '英文菜单',
    icon: '图标',
    sort: '排序',
    perms: '权限标识',
    com: '组件路径',
    parent: '上级菜单',
    type: '菜单类型',
    type1: '目录',
    type2: '菜单',
    type3: '按钮',
    menuIcon: '菜单图标',
    show: '显示排序',
    out: '是否外链',
    yes: '是',
    no: '否',
    router: '路由地址',
    param: '路由参数',
    strorg: '是否缓存',
    showStatus: '显示状态',
    'Please enter menu name': '请输入菜单名称',
    'Please enter English menu name': '请输入英文菜单',
    'Please enter routing address': '请输入路由地址',
    'Please enter component Path': '请输入组件路径',
    'Please enter permission id': '请输入权限标识',
    'Please enter routing parameters': '请输入路由参数',
    'Select previous menu': '选择上级菜单',
    'Click Select icon': '点击选择图标',
    'Main category': '主类目',
    addMenu: '添加菜单',
    editMenu: '修改菜单',
    cache: '缓存',
    uncached: '不缓存',
    'Menu name cannot be empty': '菜单名称不能为空',
    'Menu sort cannot be empty': '菜单顺序不能为空',
    'Route address cannot be empty': '路由地址不能为空',
    'Are you sure to delete the data item?': '是否确认删除该数据项？',
    "If you select external link, the routing address needs to start with http(s)://": '选择是外链则路由地址需要以`http(s)://`开头',
    'The accessed routing address, such as: user, if the external network address requires internal link access, it starts with http(s)://': '访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头',
    'The component path to be accessed, such as: system/user/index, which is in the views directory by default': '访问的组件路径，如：`system/user/index`，默认在`views`目录下',
    'The authority characters defined in the controller, for example:': '控制器中定义的权限字符，如：',
    'Default passed parameters for access routes, such as:': '访问路由的默认传递参数，如：',
    "If Yes is selected": '选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致',
    'Select Hide and the route will not appear in the sidebar but will still be accessible.': '选择隐藏则路由将不会出现在侧边栏，但仍然可以访问',
    "If Disable is selected, the route will not appear in the sidebar and cannot be accessed": '选择停用则路由将不会出现在侧边栏，也不能被访问',
    'Activated successfully!': '启用成功！',
    'Deactivation successfully!': '停用成功！',
    'Are you sure you want to enable it?': '确认要启用吗？',
    'Are you sure you want to deactivate it?': '确认要停用吗？',
    assign: '分配用户',
    addUser: '添加用户',
    editUser: '修改用户',
    batch: '批量取消授权',
    canPerm: '取消授权',
    'Are you sure you want to cancel the role of this user?': '确认要取消该用户的角色吗？',
    canPermSuccess: '取消授权成功',
    'Do you want to uncheck the user authorization data item?': '是否取消选中用户授权数据项？',
    selectUser: '选择用户',
    'Please select a user to assign': '请选择要分配的用户',
    'Authorization successful!': '授权成功！',
    account: '登录账号',
    roleInfo: '角色信息'
  },
  // 部门管理
  dept: {
    status: '部门状态',
    name: '部门名称',
    parent: '上级部门',
    principal: '负责人',
    phone: '联系电话',
    'Superior department cannot be empty': '上级部门不能为空',
    'Department name cannot be empty': '部门名称不能为空',
    'Display order cannot be empty': '显示排序不能为空',
    'Please enter department name': '请输入部门名称',
    'Please enter responsible Person': '请输入负责人',
    'Please enter phone': '请输入联系电话',
    'Select superior department': '选择上级部门',
    addDept: '添加部门',
    editDept: '修改部门'
  },
  // 设备类型
  deviceType: {
    type1: '光储系统(并离网)',
    type2: '光储变流器(并离网)',
    type3: '电池系统',
    type4: '光伏控制系统',
    type5: '光储变流器(纯并网)',
    type6: '光储系统(纯并网)',
    type7: '储能系统(纯并网)',
    type8: '储能变流器(纯并网)',
    type9: '储能系统(并离网)',
    type10: '储能变流器(并离网)',
  },
  export: {
    dateType: '日期类型',
    selectDate: '选择日期',
    selectWeek: '选择周',
    selectMonth: '选择月',
    selectYear: '选择年',
    exportDetail: '导出详情',
    fileSuffix: '电量统计报表',
    'Please select date': '请选择日期'
  },
  sim: {
    card: '物联网卡卡号',
    status: '物联网卡状态',
    plan: '流量套餐',
    use: '月使用流量(MB)',
    residue: '月剩余流量(MB)',
    expire: '到期时间',
    topUp: '充值',
    unBind: '解绑',
    cActive: '可激活',
    active: '已激活',
    test: '可测试',
    off: '已注销',
    stock: '库存',
    pre: '预注销',
    maintaining: '维护中',
    'Please enter iot card number': '请输入物联网卡号',
    'Please select iot card status': '请选择激活状态',
    'Please enter binding item': '请输入绑定项目',
    Bsn: '绑定SN',
    startTime: '开始时间',
    endTime: '结束时间',
    sn: '设备Sn',
    add: '添加SIM卡'
  },
  // 收益统计
  bill: {
    '总收益(元)': '总收益(元)',
    '储能充电收益(元)': '储能充电收益(元)',
    '储能放电收益(元)': '储能放电收益(元)',
    '电网放电收益(元)': '电网放电收益(元)',
    '电网充电收益(元)': '电网充电收益(元)',
    '光伏发电量收益(元)': '光伏发电量收益(元)',
    '等效植树数(棵)': '等效植树(棵)',
    '等效减排co2(t)': '等效减排CO2(T)',
    '更新时间': '更新时间',
    '合计': '合计',
    '放电量(kWh)': '放电量(kWh)',
    '充电量(kWh)': '充电量(kWh)',
    '光伏发电量(kWh)': '光伏发电量(kWh)',
    '统计时间': '统计时间',
    '棵': '棵',
  },
  // 备电方案
  backup: {
    '方案名称': '方案名称',
    '电网充电': '电网充电',
    '发电机': '发电机',
    '发电机充电': '发电机充电',
    '电池充电功率': '电池充电功率',
    '备电保持SOC': '备电保持SOC',
    '使用电网给电池充电': '使用电网给电池充电',
    '电池没电时使用发电机': '电池没电时使用发电机',
    '在发电机工作时给电池充电': '在发电机工作时给电池充电',
    '使用电网、油机给电池充电的功率（不限制光伏功率）': '使用电网、油机给电池充电的功率（不限制光伏功率）',
    '有电网时，电池保留SOC电量，SOC之上电量可给负载使用': '有电网时，电池保留SOC电量，SOC之上电量可给负载使用',
    '使能': '使能',
    '不使能': '不使能',
    '请输入方案名称': '请输入方案名称',
    '请选择电网充电': '请选择电网充电',
    '请选择发电机': '请选择发电机',
    '请选择发电机充电': '请选择发电机充电',
    '请输入电池充电功率': '请输入电池充电功率',
    '请输入备电保持SOC': '请输入备电保持SOC',
    '添加方案': '添加方案',
    '修改方案': '修改方案',
  },
  // oss
  oss: {
    '文件名称': '文件名称',
    '文件类型': '文件类型',
    '文件大小': '文件大小',
    '版本名称': '版本名称',
    '文件路径': '文件路径',
    '文件版本': '文件版本',
    '下载': '下载',
    '选择文件': '选择文件',
    '上传文件': '上传文件',
    '请输入文件名称': '请输入文件名称',
    '请选择文件版本类型': '请选择文件版本类型',
    '请先选择文件版本类型': '请先选择文件版本类型',
    '请选择文件': '请选择文件',
    '上传失败': '上传失败',
    '上传成功': '上传成功',
    '下载文件成功': '下载文件成功',
    '请上传 大小不超过': '请上传 大小不超过',
    '的文件': '的文件',
    '文件格式不正确, 请上传': '文件格式不正确, 请上传',
    '格式文件': '格式文件',
    '上传文件大小不能超过': '上传文件大小不能超过',
    '正在上传文件，请稍候': '正在上传文件，请稍候',
    '上传文件数量不能超过': '上传文件数量不能超过',
    '上传文件失败，请重试': '上传文件失败，请重试',
    '正在下载文件，请稍候': '正在下载文件，请稍候',
    'HMI升级文件': 'HMI升级文件',
    'MAC升级文件': 'MAC升级文件',
    'MDC升级文件': 'MDC升级文件',
    'STS升级文件': 'STS升级文件',
    '上传地区': '上传地区',
    '访问域名': '访问域名',
    '升级对象': '升级对象',
  },
  price: {
    '电价': '电价',
    '时段': '时段',
    '请选择使能': '请选择使能',
    '请选择开始时间': '请选择开始时间',
    '请选择结束时间': '请选择结束时间',
    '请输入电价': '请输入电价',
    '请添加时段': '请添加时段',
    '最多只能添加12条哦': '最多只能添加12条哦',
    '至少要有一条哦': '至少要有一条哦',
    add: '添加',
    '功率': '功率',
    '请输入功率': '请输入功率',
    '买卖电是否同价': '买卖电是否同价',
    '买电': '买电',
    '卖电': '卖电',
    '请选择买卖电是否同价': '请选择买卖电是否同价',
    '元': '元'
  },
  log: {
    '请输入下发参数': '请输入下发参数',
    '指令类型': '指令类型',
    '下发参数': '下发参数',
    '下发参数值': '下发参数值',
    '执行结果': '执行结果',
    '执行成功': '执行成功',
    '执行失败': '执行失败',
    '已下发，等待执行': '已下发，等待执行',
    '下发时间': '下发时间',
    '完成时间': '完成时间',
    '系统设置参数': '系统设置参数',
    '策略类参数设置': '策略类参数设置',
    'MAC参数': 'MAC参数',
    'MDC参数': 'MDC参数',
    '电池参数': '电池参数',
    '设备升级': '设备升级',
    '系统开关机': '系统开关机',
    '收益统计报表': '收益统计报表',
    '导出成功': '导出成功',
    '导出失败': '导出失败',
    '导出中': '导出中',
    '电量统计报表': '电量统计报表',
    '操作人员': '操作人员'
  },
  param: {
    '下发状态': '下发状态',
    '未下发': '未下发',
    '下发成功': '下发成功',
    '下发中': '下发中',
    '下发失败': '下发失败',
    '该类参数从未下发': '该类参数从未下发',
    '参数已成功下发至设备，执行未知，请等待': '参数已成功下发至设备，执行未知，请等待',
    '参数已成功下发至设备并已执行成功': '参数已成功下发至设备并已执行成功',
    '参数已成功下发至设备，设备并未执行成功': '参数已成功下发至设备，设备并未执行成功。',
    '运行模式': '运行模式',
    '削峰填谷': '削峰填谷',
    '手动模式': '手动模式',
    '后备模式': '后备模式',
    '防逆流使能': '防逆流使能',
    '开关机': '开关机',
    '开机': '开机',
    '关机': '关机',
    '下发': '下发',
    '参数已下发至设备': '参数已下发至设备',
    '查看执行结果': '查看执行结果',
    '星期一': '星期一',
    '星期二': '星期二',
    '星期三': '星期三',
    '星期四': '星期四',
    '星期五': '星期五',
    '星期六': '星期六',
    '星期天': '星期天',
    '保存成功': '保存成功',
    '正在下发中，请稍后再下发': '正在下发中，请稍后再下发',
    '正在下发中': '正在下发中',
    '备电方案': '备电方案',
    '削峰填谷': '削峰填谷',
    '必选': '必选',
    '分时电价': '分时电价',
    '查看更多方案': '查看更多方案',
    '参数设置': '参数设置',
    'MAC参数设置': 'MAC参数设置',
    '电池参数设置': '电池参数设置',
    'MDC参数设置': 'MDC参数设置',
    '在线升级': '在线升级',
    '直流源电压设置': '直流源电压设置',
    '电池恒流设置': '电池恒流设置',
    '电池恒功率设置': '电池恒功率设置',
    '光伏限功率设置': '光伏限功率设置',
    '有功功率设置': '有功功率设置',
    '无功功率设置': '无功功率设置',
    '功率因数设置': '功率因数设置',
    '并离网设置': '并离网设置',
    'SOC上限设置': 'SOC上限设置',
    'SOC下限设置': 'SOC下限设置',
    '充电限流值': '充电限流值',
    '放电限流值': '放电限流值',
    '欠压保护': '欠压保护',
    '欠压恢复': '欠压恢复',
    '过压保护': '过压保护',
    '过压恢复': '过压恢复',
    '版本文件': '版本文件',
    '请选择版本文件': '请选择版本文件',
    '确定要恢复出厂设置吗？': '确定要恢复出厂设置吗？',
    '恢复出厂设置成功，需要等 2 分钟后再查看': '恢复出厂设置成功，需要等 2 分钟后再查看',
    '已取消该操作': '已取消该操作',
    '恢复出厂设置': '恢复出厂设置',
  },
  '新加坡': '新加坡',
  '深圳': '深圳',
  '暂无设备': '暂无设备',
  '请选择设备': '请选择设备',
  '请选择参数': '请选择参数',
  '最多只能对比30条数据哦': '最多只能对比30条数据哦',
  '请输入纬度或点击地图自动生成': '请输入纬度或点击地图自动生成',
  '请输入经度或点击地图自动生成': '请输入经度或点击地图自动生成',
  '请输入地址或点击地图自动生成': '请输入地址或点击地图自动生成',
  '请输入正确的经度坐标(数字)': '请输入正确的经度坐标(数字)',
  '请输入正确的纬度坐标(数字)': '请输入正确的纬度坐标(数字)',
  '请输入经纬度坐标或点击地图自动生成': '请输入经纬度坐标或点击地图自动生成',
  '欢迎': '欢迎',
  '创建人员': '创建人员',
  '请先选择要上传的升级对象': '请先选择要上传的升级对象',
  '文件名称错误，请严格按照格式上传，提示：请带含MAC、DSP的文件名称': '文件名称错误，请严格按照格式上传，提示：请带含MAC、DSP的文件名称',
  '文件名称错误，请严格按照格式上传，提示：请带含MAC、ARM的文件名称': '文件名称错误，请严格按照格式上传，提示：请带含MAC、ARM的文件名称',
  '文件名称错误，请严格按照格式上传，提示：请带含MDC、DSP的文件名称': '文件名称错误，请严格按照格式上传，提示：请带含MDC、DSP的文件名称',
  '文件名称错误，请严格按照格式上传，提示：请带含MDC、ARM的文件名称': '文件名称错误，请严格按照格式上传，提示：请带含MDC、ARM的文件名称',
  '文件名称错误，请严格按照格式上传，提示：请带含STS、DSP的文件名称': '文件名称错误，请严格按照格式上传，提示：请带含STS、DSP的文件名称',
  '文件名称错误，请严格按照格式上传': '文件名称错误，请严格按照格式上传',
  '格式为': '格式为',
  '文件名称格式': '文件名称格式',
  '操作手册': '操作手册',
  '升级进度': '升级进度',
  '查看详情': '查看详情',
  '升级记录': '升级记录',
  '升级文件': '升级文件',
  '升级版本': '升级版本',
  '当前升级个数': '当前升级个数',
  '总升级个数': '总升级个数',
  '升级结果': '升级结果',
  '升级描述': '升级描述',
  '升级时间(UTC+00:00)': '升级时间(UTC+00:00)',
  '成功': '成功',
  '失败': '失败',
  '等待': '等待',
  '模块': '模块',
  '升级成功': '升级成功',
  '升级中': '升级中',
  '升级失败': '升级失败',
  '正在下载远程升级包': '正在下载远程升级包',
  '远程升级包下载成功': '远程升级包下载成功',
  '远程升级包下载失败': '远程升级包下载失败',
  '测点别名': '测点别名',
  '测点值': '测点值',
  '测点值别名': '测点值别名',
  '测点编号': '测点编号',
  '测点值编号': '测点值编号',
  '测点类型': '测点类型',
  '外设': '外设',
  '电表': '电表',
  '添加别名': '添加别名',
  '测点编号/测点值编号': '测点编号/测点值编号',
  '请选择测点类型': '请选择测点类型',
  '修改别名': '修改别名',
  '电表在线状态': '电表在线状态',
  'A相电压': 'A相电压',
  'B相电压': 'B相电压',
  'C相电压': 'C相电压',
  '正向有功电度': '正向有功电度',
  '反向有功电度': '反向有功电度',
  '正向无功电度': '正向无功电度',
  '正向无功电度': '正向无功电度',
  '尖正向有功电度': '尖正向有功电度',
  '尖反向有功电度': '尖反向有功电度',
  '尖正向无功电度': '尖正向无功电度',
  '尖反向无功电度': '尖反向无功电度',
  '峰正向有功电度': '峰正向有功电度',
  '峰反向有功电度': '峰反向有功电度',
  '峰正向无功电度': '峰正向无功电度',
  '峰反向无功电度': '峰反向无功电度',
  '平正向有功电度': '平正向有功电度',
  '平反向有功电度': '平反向有功电度',
  '平正向无功电度': '平正向无功电度',
  '平反向无功电度': '平反向无功电度',
  '谷正向有功电度': '谷正向有功电度',
  '谷反向有功电度': '谷反向有功电度',
  '谷正向无功电度': '谷正向无功电度',
  '谷反向无功电度': '谷反向无功电度',
  '负荷正向电量': '负荷正向电量',
  '负荷反向电量': '负荷反向电量',
  'PCC电表': 'PCC电表',
  '储能电表': '储能电表',
  '光伏电表': '光伏电表',
  '计量点电表': '计量点电表',
  '辅助用电电表': '辅助用电电表',
  '直流电表': '直流电表',
  '未知别名': '未知别名',
  '合闸': '合闸',
  '分闸': '分闸',
  '开关': '开关',
  '测点绑定': '测点绑定',
  '是否生效': '是否生效',
  '新建设备绑定测点': '新建设备绑定测点',
  '设备修改绑定测点': '设备修改绑定测点',
  '生效': '生效',
  '失效': '失效',
  '负为放电': '负为放电',
  '功率：分正负，正为放电，负为充电': '功率：分正负，正为放电，负为充电',
  '注：': '注：',
  '召测': '召测',
  '已召测': '已召测',
  '召测失败': '召测失败',
  '正在召测中，请稍后': '正在召测中，请稍后',
  '国家': '国家',
  '用于电网计算收益。': '用于电网计算收益。',
  '按照设置时间段给系统充放电。': '按照设置时间段给系统充放电。',
  '防止系统放电馈入电网。': '防止系统放电馈入电网。',
  '控制系统按其工作模式启停。': '控制系统按其工作模式启停。',
  '在手动模式下，控制系统输入出有功功率，正为放电，负为充电。': '在手动模式下，控制系统输入出有功功率，正为放电，负为充电。',
  '控制系统输出无功功率正为容性，负为感性。': '控制系统输出无功功率正为容性，负为感性。',
  '调节输出的有功功率和无功功率的比值。': '调节输出的有功功率和无功功率的比值。',
  '在手动控制下，设置其系统是否并入电网。': '在手动控制下，设置其系统是否并入电网。',
  '设置电池停止充电时SOC。': '设置电池停止充电时SOC。',
  '设置电池停止放电时SOC。': '设置电池停止放电时SOC。',
  '设置电池充电时的电流最大值。': '设置电池充电时的电流最大值。',
  '设置电池放电时的电流最大值。': '设置电池放电时的电流最大值。',
  '电池放电保护电压。': '电池放电保护电压。',
  '电池可放电恢复电压。': '电池可放电恢复电压。',
  '电池充电电保护电压。': '电池充电电保护电压。',
  '电池可充电恢复电压。': '电池可充电恢复电压。',
  '设置DC模块恒压模式下输出电压。': '设置DC模块恒压模式下输出电压。',
  '设置恒流模式下的输出电流。': '设置恒流模式下的输出电流。',
  '设置恒功率模式下的输出功率。': '设置恒功率模式下的输出功率。',
  '设置光伏功率最大值。': '设置光伏功率最大值。',
  '负载电表': '负载电表',
  '正向总电量': '正向总电量',
  '反向总电量': '反向总电量',
  '发电机功率': '发电机功率',
  '今天': '今天',
  '昨天': '昨天',
  '一周前': '一周前',
  '项目': '项目',
  '储能收益': '储能收益',
  '上网电量': '上网电量',
  '电网卖电收益': '电网卖电收益',
  '系统收益': '系统收益',
  '统计日期': '统计日期',
  '电网侧总充电量': '电网侧总充电量',
  '电网侧总放电量': '电网侧总放电量',
  '请选择有效的设备': '请选择有效的设备',
  '忘记密码？': '忘记密码？',
  '找回账号或者密码': '找回账号或者密码',
  '请输入您账号绑定的邮箱地址': '请输入您账号绑定的邮箱地址',
  '返回登录': '返回登录',
  '发送验证码': '发送验证码',
  '验证身份': '验证身份',
  '请输入验证码': '请输入验证码',
  '返回上一步': '返回上一步',
  '下一步': '下一步',
  '重置密码': '重置密码',
  '成功提示': '成功提示',
  '密码已重置成功啦': '密码已重置成功啦',
  '去登录': '去登录',
  '特别说明': '特别说明',
  '找回步骤：输入您想找回密码或者账号的邮箱地址 - 输入4位验证码，完成身份验证 - 设置新密码。': '找回步骤：输入您想找回密码或者账号的邮箱地址 - 输入4位验证码，完成身份验证 - 设置新密码。',
  '如果您忘记了邮箱地址或者没有邮箱，那么您应该联系管理员帮您重置您的密码，客服电话：': '如果您忘记了邮箱地址或者没有邮箱，那么您应该联系管理员帮您重置您的密码，客服电话：',
  '。': '。',
  '进行身份验证时，验证码有效时长为5分钟，如您在身份验证页面停留过久（5分钟），那么您将身份验证失败。': '进行身份验证时，验证码有效时长为5分钟，如您在身份验证页面停留过久（5分钟），那么您将身份验证失败。',
  '正在发送验证码，请稍后': '正在发送验证码，请稍后',
  '正在验证身份，请稍后': '正在验证身份，请稍后',
  '验证成功': '验证成功',
  '正在重置密码，请稍后': '正在重置密码，请稍后',
  '请输入密码': '请输入密码',
  '货币': '货币',
  '货币单位': '货币单位',
  '费率': '费率',
  '费率一': '费率一',
  '费率二': '费率二',
  '费率三': '费率三',
  '费率四': '费率四',
  '费率五': '费率五',
  'EN国家': 'EN国家',
  'EN货币单位': 'EN货币单位',
  '添加货币': '添加货币',
  '修改货币': '修改货币',
  '该方案的货币已修改，请重新添加一个方案': '该方案的货币已修改，请重新添加一个方案',
  '电表正向总电量': '电表正向总电量',
  '电表反向总电量': '电表反向总电量',
  '正向总电量': '正向总电量',
  '反向总电量': '反向总电量',
  '电池告警': '电池告警',
  '电池故障': '电池故障',
  '下载APP': '下载APP',
  '扫码下载APP': '扫码下载APP',
  '电价：电价货币为项目绑定的货币为准，如项目绑定货币为美元，那么电价为0.2美元': '电价：电价货币为项目绑定的货币为准，如项目绑定货币为美元，那么电价为0.2美元',
  '电价信息': '电价信息',
  '电量类型': '电量类型',
  '储能电量': '储能电量',
  '光伏电量': '光伏电量',
  '电网电量': '电网电量',
  '1、如若不想导出某一类型的电量，请不要勾选该类型电量': '1、如若不想导出某一类型的电量，请不要勾选该类型电量；',
  '2、是否计算差值，为某一时间段的汇总值。': '2、是否计算差值，为某一时间段的汇总值。',
  '导出的数据为你自己选择的数据': '导出的数据为你自己选择的数据',
  '数据分析报表': '数据分析报表',
  '不根据当地时间统计，根据创建项目所选时区统计': '不根据当地时间统计，根据创建项目所选时区统计',
  'AB线电压': 'AB线电压',
  'BC线电压': 'BC线电压',
  'CA线电压': 'CA线电压',
  '不记得原来的密码？': '不记得原来的密码？',
  '正在加载页面，请稍候！': '正在加载页面，请稍候！',
  '帮助中心': '帮助中心',
  '常见问题': '常见问题',
  '是否多屏': '是否多屏',
  '是': '是',
  '否': '否',
  '云平台PC端操作手册': '云平台PC端操作手册',
  '云平台PC端英文版操作手册': '云平台PC端英文版操作手册',
  '云平台常见问题': '云平台常见问题',
  '云平台英文版常见问题': '云平台英文版常见问题',
  '云平台APP操作手册': '云平台APP操作手册',
  '云平台APP英文版操作手册': '云平台APP英文版操作手册',
  '所属国家': '所属国家',
  '台': '台',
  '历史数据': '历史数据',
  '获取数据': '获取数据',
  '设备离线，不可获取': '设备离线，不可获取',
  '上传中': '上传中',
  '文件上传失败，不可下载或者删除': '文件上传失败，不可下载或者删除',
  '文件上传中，不可下载或者删除': '文件上传中，不可下载或者删除',
  '获取文件': '获取文件',
  '拉取oss服务器上的所有升级文件': '拉取oss服务器上的所有升级文件',
  '获取失败': '获取失败',
  '文件地区': '文件地区',
  '请输入ac': '请输入ac',
  '光伏消纳': '光伏消纳',
  '动态扩容': '动态扩容',
  '变压器容量': '变压器容量',
  '全选': '全选',
  '请稍等,数据还在请求中~': '请稍等,数据还在请求中~',
  '复制成功': '复制成功',
  '光储充系统(纯并网)': '光储充系统(纯并网)',
  '光储充系统(并离网)': '光储充系统(并离网)',
  '分配项目': '分配项目',
  '所要分配的用户': '所要分配的用户',
  '注：分配项目，会把项目所绑定的设备也一起被分配给用户。': '注：分配项目，会把项目所绑定的设备也一起被分配给用户。',
  '分配失败': '分配失败',
  '分配成功': '分配成功',
  '整机状态': '整机状态',
  '内风机状态': '内风机状态',
  '外风机状态': '外风机状态',
  '压缩机状态': '压缩机状态',
  '电加热状态': '电加热状态',
  '应急风机状态': '应急风机状态',
  '空调开关机状态': '空调开关机状态',
  '出水温度': '出水温度',
  '出水压力': '出水压力',
  '进水温度': '进水温度',
  '进水压力': '进水压力',
  '空调状态': '空调状态',
  '空调模式': '空调模式',
  '内循环': '内循环',
  '制热': '制热',
  '充电桩通信状态': '充电桩通信状态',
  '充电枪个数': '充电枪个数',
  '充电桩状态': '充电桩状态',
  '1#枪输出电流': '1#枪输出电流',
  '1#枪输出电压': '1#枪输出电压',
  '1#枪输出功率': '1#枪输出功率',
  '1#枪当前SOC': '1#枪当前SOC',
  '1#枪充电电量': '1#枪充电电量',
  '1#枪充电金额': '1#枪充电金额',
  '1#枪电池最高温度': '1#枪电池最高温度',
  '1#枪充电时长': '1#枪充电时长',
  '1#枪状态': '1#枪状态',
  '2#枪输出电流': '2#枪输出电流',
  '2#枪输出电压': '2#枪输出电压',
  '2#枪输出功率': '2#枪输出功率',
  '2#枪当前SOC': '2#枪当前SOC',
  '2#枪充电电量': '2#枪充电电量',
  '2#枪充电金额': '2#枪充电金额',
  '2#枪电池最高温度': '2#枪电池最高温度',
  '2#枪充电时长': '2#枪充电时长',
  '2#枪状态': '2#枪状态',
  '总输出功率': '总输出功率',
  '充电桩': '充电桩',
  '空闲': '空闲',
  '插枪': '插枪',
  '充电等待': '充电等待',
  '启动中': '启动中',
  '充电中': '充电中',
  '重连': '重连',
  '结算状态': '结算状态',
  '故障状态': '故障状态',
  '放电中': '放电中',
  '预约状态': '预约状态',
  '后台预约状态': '后台预约状态',
  '充电完成状态': '充电完成状态',
  'APP，预约状态': 'APP，预约状态',
  '试用期到，停止服务状态': '试用期到，停止服务状态',
  '枪功率': '枪功率',
  '枪': '枪',
  '请输入ICCID': '请输入ICCID',
  '长度为20个字符': '长度为20个字符',
  '分配SIM卡': '分配SIM卡',
  '未知': '未知',
  '注：ICCID在卡的后面，有20位字符。': '注：ICCID在卡的后面，有20位字符。',
  '提示：手动选择或者输入搜索选择': '提示：手动选择或者输入搜索选择。',
  '请选择要分配的用户': '请选择要分配的用户',
  '添加要绑定项目的设备': '添加要绑定项目的设备',
  '提示：点击上方地图自动生成地址或者手动输入，格式为：经度：113.94876， 纬度：22.636858': '提示：点击上方地图自动生成地址或者手动输入，格式为：经度：113.94876， 纬度：22.636858',
  '提示：手动输入或者点击上方地图自动生成地址': '提示：手动输入或者点击上方地图自动生成地址',
  '添加方案': '添加方案',
  '获取本地设备削峰填谷方案': '获取本地设备削峰填谷方案',
  '远程控制': '远程控制',
  '关闭远程控制': '关闭远程控制',
  '开启远程控制': '开启远程控制',
  '设置开启密码': '设置开启密码',
  'VNC模式': 'VNC模式',
  'SSH端口': 'SSH端口',
  'VNC端口': 'VNC端口',
  'VNC和SSH的端口不能重复': 'VNC和SSH的端口不能重复',
  '开启SSH': '开启SSH',
  '开启SSH+VNC': '开启SSH+VNC',
  '开启VNC': '开启VNC',
  '该设备已开启远程控制，是否需要关闭远程控制': '该设备已开启远程控制，是否需要关闭远程控制',
  '正在关闭中': '正在关闭中',
  '关闭远程成功': '关闭远程成功',
  '确认关闭': '确认关闭',
  '查看端口': '查看端口',
  '设备离线，不可操作': '设备离线，不可操作',
  '下发远程': '下发远程',
  '回复类型': '回复类型',
  '下发': '下发',
  '关闭': '关闭',
  '回复结果': '回复结果',
  '回复时间': '回复时间',
  '目录创建失败': '目录创建失败',
  '文件下载失败': '文件下载失败',
  '库文件缺失': '库文件缺失',
  '远程控制记录': '远程控制记录',
  '选择设备': '选择设备',
  '查看远程控制信息': '查看远程控制信息',
  '设备序列号': '设备序列号',
  '开启内网VNC': '开启内网VNC',
  '注：开启内网VNC的端口为5900。': '注：开启内网VNC的端口为5900。',
  '2FA验证': '2FA验证',
  '变压器温度': '变压器温度',
  '簇': '簇',
  '簇级传感器1': '簇级传感器1',
  '簇级传感器2': '簇级传感器2',
  '最高单体电压': '最高单体电压',
  '最低单体电压': '最低单体电压',
  '最高单体温度': '最高单体温度',
  '最低单体温度': '最低单体温度',
  '温升': '温升',
  '详情': '详情',
  '温度': '温度',
  '电压': '电压',
  '未知电表': '未知电表',
  '暂未配置电池电芯规格信息，请联系管理员或售后人员。': '暂未配置电池电芯规格信息，请联系管理员或售后人员。',
  '日期类型': '日期类型',
  '选择日期': '选择日期',
  '开始时间': '开始时间',
  '结束时间': '结束时间',
  '时间段': '时间段',
  '是否计算差值': '是否计算差值',
  '在线': '在线',
  '离线': '离线',
  '消防': '消防',
  '默认': '默认',
  '外设类型': '外设类型',
  '请选择外设类型': '请选择外设类型',
  '电流': '电流',
  '阻抗': '阻抗',
  '电芯数量': '电芯数量',
  '电压数量': '电压数量',
  '温度数量': '温度数量',
  '温升数量': '温升数量',
  '电流数量': '电流数量',
  '阻抗数量': '阻抗数量',
  '不能高于电芯数量': '不能高于电芯数量',
  '添加配置': '添加配置',
  '修改配置': '修改配置',
  '是否同步下发': '是否同步下发',
  '是：主从机同步下发；否：下发至主机。': '是：主从机同步下发；否：下发至主机。',
  '告警代码': '告警代码',
  '一月': '一月',
  '二月': '二月',
  '三月': '三月',
  '四月': '四月',
  '五月': '五月',
  '六月': '六月',
  '七月': '七月',
  '八月': '八月',
  '九月': '九月',
  '十月': '十月',
  '十一月': '十一月',
  '十二月': '十二月',
  '执行周期': '执行周期',
  '每天；': '每天；',
  '每周（1~7天）；': '每周（1~7天）；',
  '1~12个月。': '1~12个月。',
  '注：该执行周期只适用于削峰填谷。': '注：该执行周期只适用于削峰填谷。',
  '市电电表': '市电电表',
  '是否显示柴油机': '是否显示柴油机',
  '升级类型': '升级类型',
  '主屏': '主屏',
  '副屏': '副屏',
  '正母线电压': '正母线电压',
  '负母线电压': '负母线电压',
  '清空': '清空',
  '清空成功': '清空成功',
  '充电桩功率': '充电桩功率',
  '包': '包',
  '404错误!': '404错误!',
  '找不到网页！': '找不到网页！',
  '对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。': '对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。',
  '返回首页': '返回首页',
  '401错误!': '401错误!',
  '您没有访问权限！': '您没有访问权限！',
  '对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面。': '对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面。',
  '需要升级HMI跟模块': '需要升级HMI跟模块',
  '只需要升级HMI': '只需要升级HMI',
  '采集屏跟云平台通讯': '采集屏跟云平台通讯',
  '主屏版本': '主屏版本',
  '副屏版本': '副屏版本',
  '前往新平台': '前往新平台',
  '前往旧平台': '前往旧平台',
  '采集屏版本': '采集屏版本',
  '采集屏': '采集屏',
  'BMS类型': 'BMS类型',
  '宁德': '宁德',
  '协能': '协能',
  '高泰昊能': '高泰昊能',
  '华塑': '华塑',
  '高特': '高特',
  '华思': '华思',
  '小鸟': '小鸟',
  '山东威马': '山东威马',
  '亿纬锂电': '亿纬锂电',
  '力神': '力神',
  '帷幕-BCU': '帷幕-BCU',
  '宁德液冷': '宁德液冷',
  '三级宁德': '三级宁德',
  '优旦': '优旦',
  '欣旺达': '欣旺达',
  '沛城电子': '沛城电子',
  '帷幕-BCU2': '帷幕-BCU2',
  '群控能源GCE': '群控能源GCE',
  '高特三级bms': '高特三级bms',
  '科工': '科工',
  '描述': '描述',
  '失败是否重启': '失败是否重启',
  '该功能只有新版本才支持': '该功能只有新版本才支持',
  '通用版本': '通用版本',
  '定制版本': '定制版本',
  '联系管理员': '联系管理员',
  '告警详情': '告警详情',
  '告警信息': '告警信息',
  '问题分析': '问题分析',
  '问题原因': '问题原因',
  '处理办法': '处理办法',
  '涉及部件': '涉及部件',
  '暂无详细信息': '暂无详细信息',
  '格式不正确': '格式不正确',
  '域名': '域名',
  '中文网站名称': '中文网站名称',
  '英文网站名称': '英文网站名称',
  '登录logo': '登录logo',
  '导航栏logo': '导航栏logo',
  '中文大屏标题': '中文大屏标题',
  '英文大屏标题': '英文大屏标题',
  '宽度': '宽度',
  '高度': '高度',
  '中文大屏logo': '中文大屏logo',
  '英文大屏logo': '英文大屏logo',
  '添加域名': '添加域名',
  '修改域名': '修改域名',
  '请输入域名': '请输入域名',
  '请输入网站名称': '请输入网站名称',
  '请输入大屏标题': '请输入大屏标题',
  '应用名称': '应用名称',
  '强制升级': '强制升级',
  '平台': '平台',
  '包大小(bytes)': '包大小(bytes)',
  '包地址': '包地址',
  '增量升级': '增量升级',
  '增量包地址': '增量包地址',
  '升级说明': '升级说明',
  '添加包': '添加包',
  '修改包': '修改包',
  '二级角色部门': '二级角色部门',
  '中文logo': '中文logo',
  '英文logo': '英文logo',
  '缩放logo': '缩放logo',
  '主题': '主题',
  '主题颜色': '主题颜色',
  '侧边栏活跃背景色': '侧边栏活跃背景色',
  '侧边栏背景颜色': '侧边栏背景颜色',
  '侧边栏logo背景颜色': '侧边栏logo背景颜色',
  '侧边栏文字颜色': '侧边栏文字颜色',
  '侧边栏文字活跃颜色': '侧边栏文字活跃颜色',
  '页面文字颜色': '页面文字颜色',
  '认证失败，无法访问系统资源': '认证失败，无法访问系统资源',
  '当前操作没有权限': '当前操作没有权限',
  '访问资源不存在': '访问资源不存在',
  '系统未知错误，请反馈给管理员': '系统未知错误，请反馈给管理员',
  '不允许有中文字符': '不允许有中文字符',
  'MDC直流源': 'MDC直流源',
  '正向电量': '正向电量',
  '反向电量': '反向电量',
  '直流源功率': '直流源功率',
  '暂无数据': '暂无数据',
  '通知公告': '通知公告',
  '公告标题': '公告标题',
  '公告类型': '公告类型',
  '内容': '内容',
  '添加公告': '添加公告',
  '修改公告': '修改公告',
  '确定全部已读吗？': '确定全部已读吗？',
  '确定已读吗？': '确定已读吗？',
  '已读成功': '已读成功',
  '已读': '已读',
  '全部已读': '全部已读',
  '通知的用户': '通知的用户',
  '没有未读消息': '没有未读消息',
  '邮箱地址不能为空': '邮箱地址不能为空',
  '手机号码不能为空': '手机号码不能为空',
  'BMS升级文件': 'BMS升级文件',
  'TAR升级文件': 'TAR升级文件',
  '选择TAR文件': '选择TAR文件',
  'TAR版本文件': 'TAR版本文件',
  'INI版本文件': 'INI版本文件',
  '选择INI文件': '选择INI文件',
  '电表取值类型': '电表取值类型',
  '正向': '正向',
  '反向': '反向',
  '请到微信公众号进行充值，微信公众号为：': '请到微信公众号进行充值，微信公众号为：',
}
