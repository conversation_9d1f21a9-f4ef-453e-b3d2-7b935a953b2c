<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-01-03 17:56:01
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-07-03 15:15:16
 * @FilePath: \elecloud_platform-main\src\views\system\user\profile\userInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="auto">
    <!-- <el-form-item :label="$t('user.nickname')" prop="nickName">
      <el-input v-model="user.nickName" maxlength="30" />
    </el-form-item>  -->
    <el-form-item :label="$t('user.phone')" prop="phonenumber">
      <el-input v-model="user.phonenumber" maxlength="11" />
    </el-form-item>
    <el-form-item :label="$t('user.email')" prop="email">
      <el-input v-model="user.email" maxlength="50" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">{{ $t('common.save') }}</el-button>
      <el-button type="danger" size="mini" @click="close">{{ $t('common.Closure') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserProfile } from "@/api/system/user";

export default {
  props: {
    user: {
      type: Object
    }
  },
  data() {
    return {
      // 表单校验
      rules: {
        nickName: [
          { required: true, message: this.$t(`user['Nickname cannot be empty']`), trigger: "blur" }
        ],
        email: [
          { required: true, message: this.$t("邮箱地址不能为空"), trigger: "blur" },
          {
            type: "email",
            message: this.$t(`user['Please enter a valid email address']`),
            trigger: ["blur", "change"]
          }
        ],
        phonenumber: [
          { required: true, message: this.$t("手机号码不能为空"), trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t(`user['Please enter a valid mobile phone number']`),
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserProfile(this.user).then(response => {
            this.$modal.msgSuccess(this.$t(`common['Modify successfully']`));
          });
        }
      });
    },
    close() {
      this.$tab.closePage();
    }
  }
};
</script>
