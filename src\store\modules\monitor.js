import {
  deviceMonitoringDetailTop,
  deviceMonitoringDetailRight,
  powerAnalysisStatistics,
  selectDynamicGraph,
  electricStatistics,
  exportDeviceMonitoring,
  // 组合类型
  powerAnalysisStatisticsGroup,
  selectDynamicGraphGroup
} from '@/api/monitors/opticalstorage'
import { bindAliasQuery } from '@/api/operation/deviceBindAlias'
import {
  Message
} from 'element-ui';
import moment from 'moment'
import { handleExport } from '@/utils/export'
import i18n from '@/lang'
import { isGroupFn, isPowerFn, isBusFn } from '@/hook/useDeviceType'
import { maxBy, minBy, isEmpty } from 'lodash'
import { getCellConfig } from '@/api/monitors/param'

const user = {
  state: {
    baseInfo: {
      deviceName: '',
      deviceSerialNumber: '',
      deviceModel: '',
      projectName: '',
      deviceBatteryCapacity: '',
      deviceRatedPower: '',
      onLineState: '',
      dayOutputOfPlant: null,
      dayElectricityConsumption: null,
      projectAddress: '',
      deviceFactoryVersion: '',
      dayPhotovoltaicPowerCapacityCalculate: null
    },
    control: {},
    pcs_ac: [],
    pcs_dc: [],
    pcs_sts: [],
    pcs_bms: [],
    pcs_ele: [],
    pcs_io: [],
    pcs_cp: [],
    pcs_cell: [],
    isShowCell: 0,
    pcs_stsIo: [],
    pcs_bmsBau: [],
    /**
     * 功率分析
     */
    lineQueryInfo: {
      deviceSerialNumber: '',
      timeZone: '',
      date: ''
    },
    powerLineData: {},
    flowData: {
      power: 0, // 电网功率
      cell: 0, // 电池功率
      load: 0, // 负载功率
      photovoltaic: 0 // 光伏功率
    },
    /**
     * 用电量统计
     */
    queryInfo: {
      deviceSerialNumber: '',
      type: 1,
      endTime: '',
      startTime: ''
    },
    electricData: {},
    /**
     * 组合类型
     */
    groupData: {},
    groupList: [],
    currentNodeKey: '',
    currentNodeType: '',
    groupFlowData: {
      power: 0, // 电网功率
      cell: 0, // 电池功率
      load: 0, // 负载功率
      photovoltaic: 0 // 光伏功率
    },
    lineGroupQueryInfo: {
      deviceSerialNumber: '',
      timeZone: '',
      date: ''
    },
    powerGroupLineData: {},
    paramCurrentKey: '',
    /**
     * 别名
     */
    aliasArr: []
  },

  mutations: {
    SET_BASEINFO: (state, data) => {
      state.baseInfo = data
    },
    SET_CONTROL: (state, data) => {
      state.control = data
    },
    SET_PCSAC: (state, data) => {
      state.pcs_ac = data
    },
    SET_PCSDC: (state, data) => {
      state.pcs_dc = data
    },
    SET_PCSSTS: (state, data) => {
      state.pcs_sts = data
    },
    SET_PCSBMS: (state, data) => {
      state.pcs_bms = data
    },
    SET_PCSELE: (state, data) => {
      state.pcs_ele = data
    },
    SET_PCSIO: (state, data) => {
      state.pcs_io = data
    },
    SET_PCSCP: (state, data) => {
      state.pcs_cp = data
    },
    SET_PCSCELL: (state, data) => {
      state.pcs_cell = data
    },
    SET_ISSHOWCELL: (state, data) => {
      state.isShowCell = data
    },
    SET_PCSSTSIO: (state, data) => {
      state.pcs_stsIo = data
    },
    SET_PCSBMSBAU: (state, data) => {
      state.pcs_bmsBau = data
    },
    SET_LINEQUERYINFO: (state, data) => {
      state.lineQueryInfo = {
        ...state.lineQueryInfo,
        ...data
      }
    },
    SET_POWERLINEDATA: (state, data) => {
      state.powerLineData = data
    },
    SET_FLOWDATA: (state, data) => {
      state.flowData = data
    },
    SET_QUERYINFO: (state, data) => {
      state.queryInfo = {
        ...state.queryInfo,
        ...data
      }
    },
    SET_ELECTRICDATA: (state, data) => {
      state.electricData = data
    },
    /**
     *  组合类型
     */
    SET_GROUPDATA: (state, data) => {
      let d = _.merge(state.groupData, data) // 对象深度合并
      state.groupData = d

      let arr = []
      let keys = Object.keys(d).length ? Object.keys(d) : []
      keys.forEach((key) => {
        arr.push({
          id: key,
          ...d[key]
        })
      })
      state.groupList = arr
    },
    SET_CURRENTNODEKEY: (state, data) => {
      state.currentNodeKey = data
    },
    SET_CURRENTNODTYPE: (state, data) => {
      state.currentNodeType = data
    },
    SET_GROUPFLOWDATA: (state, data) => {
      state.groupFlowData = data
    },
    SET_LINEGROUPQUERYINFO: (state, data) => {
      state.lineGroupQueryInfo = {
        ...state.lineGroupQueryInfo,
        ...data
      }
    },
    SET_POWERGROUPLINEDATA: (state, data) => {
      state.powerGroupLineData = data
    },
    SET_PARAMCURRENTKEY: (state, data) => {
      state.paramCurrentKey = data
    },
    /**
     * 别名
     */
    SET_ALIASARR: (state, data) => {
      state.aliasArr = data
    }
  },

  actions: {
    deviceMonitoringDetailTopFn({
      commit
    }, queryInfo) {
      deviceMonitoringDetailTop({
        deviceSerialNumber: queryInfo.deviceSerialNumber,
        deviceType: queryInfo.deviceType
      }).then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        let data = res.data
        data.deviceBatteryCapacity = parseFloat(data.deviceBatteryCapacity)
        data.deviceRatedPower = parseFloat(data.deviceRatedPower)
        data.photovoltaicInstalledCapacity = parseFloat(data.photovoltaicInstalledCapacity)
        data.dayOutputOfPlant = parseFloat(data.dayOutputOfPlant)
        data.dayElectricityConsumption = parseFloat(data.dayElectricityConsumption)
        data.dayPhotovoltaicPowerCapacityCalculate = parseFloat(data.dayPhotovoltaicPowerCapacityCalculate)
        commit('SET_BASEINFO', data)
      })
    },
    // pcs
    async deviceMonitoringDetailRightFn({
      commit,
      state
    }, queryInfo) {
      const res = await deviceMonitoringDetailRight({
        deviceSerialNumber: queryInfo.deviceSerialNumber,
        type: queryInfo.type,
        deviceType: queryInfo.deviceType
      })
      if (res.code !== 200) new Error(Message.error(res.msg))
      // let isGroup = queryInfo.deviceType == 10000 || queryInfo.deviceType == 10001 || queryInfo.deviceType == 10002
      let isGroup = isGroupFn(queryInfo.deviceType)
      if (queryInfo.type == 'control') { // 本地控制器
        if (isGroup) { // 组合类型
          let obj = {}
          let controlObj = {}
          res.data.forEach(item => {
            if (item && item['jk_1031']) {
              let power = parseFloat(item['jk_1031']) + parseFloat(item['jk_1032']) + parseFloat(item['jk_1033'])
              item.power = power.toFixed(2)
            }
            obj[item.ac] = {
              control: item
            }
            controlObj[item.ac] = item
          })
          commit('SET_GROUPDATA', obj)
          return controlObj
        } else {
          let control = res.data ? res.data : {}
          if (control && control['jk_1031']) {
            let power = parseFloat(control['jk_1031']) + parseFloat(control['jk_1032']) + parseFloat(control['jk_1033'])
            control.power = power.toFixed(2)
          }
          commit('SET_CONTROL', control)
        }
      } else if (queryInfo.type == 'pcs') {
        function setData(data) {
          let obj = {}
          let keys = Object.keys(data)
          let ac = []
          let dc = []
          let sts = []
          if (keys.indexOf('infoRightAC') == -1) {
            ac = []
          } else {
            ac = data.infoRightAC
          }
          if (isGroup) {
            obj.ac = ac
          } else {
            commit('SET_PCSAC', ac) // AC
          }
          if (keys.indexOf('infoRightDC') == -1) {
            dc = []
          } else {
            dc = data.infoRightDC
          }
          if (isGroup) {
            obj.dc = dc
          } else {
            commit('SET_PCSDC', dc) // DC
          }
          if (keys.indexOf('infoRightSTS') == -1) {
            sts = []
          } else {
            sts = data.infoRightSTS
          }
          if (isGroup) {
            obj.sts = sts
          } else {
            commit('SET_PCSSTS', sts) // STS
          }

          return obj
        }
        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      } else if (queryInfo.type == 'bms') {
        function setData(data) {
          let obj = {}
          let bms = []
          if (!Object.keys(data).length) {
            bms = []
          } else {
            bms = data.infoRightBMS
          }
          if (isGroup) {
            obj.bms = bms
          } else {
            commit('SET_PCSBMS', bms) // bms
          }

          return obj
        }

        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      } else if (queryInfo.type == 'electricMeter') { // 电表
        function setData(data) {
          let obj = {}
          let ele = []
          if (!Object.keys(data).length) {
            ele = []
          } else {
            ele = data.infoRightElectricMeter
          }
          if (isGroup) {
            obj.ele = ele
          } else {
            commit('SET_PCSELE', ele) // ele
          }

          return obj
        }
        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      } else if (queryInfo.type == 'peripherals') { // 外设
        function setData(data) {
          let obj = {}
          let io = []
          if (!Object.keys(data).length) {
            io = []
          } else {
            io = data.infoRightPeripherals
          }
          if (isGroup) {
            obj.io = io
          } else {
            commit('SET_PCSIO', io) // io
          }

          return obj
        }
        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      } else if (queryInfo.type == 'chargingPile') { // 充电桩
        function setData(data) {
          let obj = {}
          let cp = []
          if (!Object.keys(data).length) {
            cp = []
          } else {
            cp = data.infoRightChargingPiles
          }
          if (isGroup) {
            obj.cp = cp
          } else {
            commit('SET_PCSCP', cp) // io
          }

          return obj
        }
        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      } else if (queryInfo.type == 'Firefighting') { // 消防
        function setData(data) {
          let obj = {}
          let stsIo = []
          if (!Object.keys(data).length) {
            stsIo = []
          } else {
            stsIo = data.infoRightFirefighting
          }
          if (isGroup) {
            obj.stsIo = stsIo
          } else {
            commit('SET_PCSSTSIO', stsIo) // io
          }

          return obj
        }
        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      } else if (queryInfo.type == 'BMSCell') { // 电芯
        function setData(data) {
          let obj = {}
          let cell = []
          if (!Object.keys(data).length) {
            cell = []
          } else {
            cell = data.infoRightBMSCell
          }
          if (isGroup) {
            obj.cell = cell
          } else {
            commit('SET_PCSCELL', cell) // bms
          }

          return obj
        }

        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      } else if (queryInfo.type == 'bms-bau') {
        function setData(data) {
          let obj = {}
          let bmsBau = []
          if (!Object.keys(data).length) {
            bmsBau = []
          } else {
            bmsBau = data['infoRightBMS-BAU']
          }
          if (isGroup) {
            obj.bmsBau = bmsBau
          } else {
            commit('SET_PCSBMSBAU', bmsBau) // bms
          }

          return obj
        }

        if (isGroup) {
          queryInfo.groupId.forEach((item, index) => {
            let itemObj = setData(res.data[index] ? res.data[index] : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(res.data)
        }
      }
      return
    },
    // 折线图
    powerAnalysisStatisticsFn({
      commit,
      state
    }, queryInfo) {
      powerAnalysisStatistics({
        ...state.lineQueryInfo,
        deviceSerialNumber: queryInfo.deviceSerialNumber,
        timeZone: queryInfo.timeZone
      }).then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        let times = res.data.map(item => moment(item.sdt).format('HH: mm'))
        let cells = res.data.map(item => item.jk_1071) // 电池功率
        // 电网功率，有sts时1031~1033，无1051
        let powerBoolean = isPowerFn(queryInfo.deviceType)
        let powers = res.data.map(item => {
          if (powerBoolean) {
            if (item.jk_1051 !== null) return item.jk_1051.toFixed(2)
            return item.jk_1051
          } else {
            if (item.jk_1031 !== null) return (item.jk_1031 + item.jk_1032 + item.jk_1033).toFixed(2)
            return item.jk_1015
          }
        })
        // 负载功率
        let loads = res.data.map(item => {
          if (item.jk_1015 !== null) return (item.jk_1015 + item.jk_1016 + item.jk_1017).toFixed(2)
          return item.jk_1015
        })
        let photovoltaics = res.data.map(item => item.jk_1074) // 光伏功率
        // 直流母线功率，储能变流器，用1056，不是用1077
        // let busBoolean = queryInfo.deviceType == 8 || queryInfo.deviceType == 10
        let busBoolean = isBusFn(queryInfo.deviceType)
        let bus = res.data.map(item => {
          if (busBoolean) {
            if (item.jk_1056 !== null) return item.jk_1056.toFixed(2)
            return item.jk_1056
          } else {
            if (item.jk_1077 !== null) return item.jk_1077.toFixed(2)
            return item.jk_1077
          }
        })
        // soc
        let soc = res.data.map(item => item.bms_4022s)
        commit('SET_POWERLINEDATA', {
          times,
          powers,
          cells,
          loads,
          photovoltaics,
          bus,
          chargingPile: res.data.map(item => item.deviceMonitoringInfoRightChargingPiles && item.deviceMonitoringInfoRightChargingPiles.length ? item.deviceMonitoringInfoRightChargingPiles : []),
          soc,
          em_10012s: res.data.map(item => isEmpty(item.em_10012s) ? 0.00 : Object.values(item.em_10012s).reduce((pre, cur) => pre + cur, 0)),
          jk103133: res.data.map(item => item.jk_1031 !== null ? (item.jk_1031 + item.jk_1032 + item.jk_1033).toFixed(2) : 0.00)
        })
      })
    },
    // 流动图
    selectDynamicGraphFn({
      commit
    }, queryInfo) {
      selectDynamicGraph({
        deviceSerialNumber: queryInfo.deviceSerialNumber,
      }).then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        // 电网功率，有sts时1031~1033，无1051
        // let powerBoolean = queryInfo.deviceType == 1 || queryInfo.deviceType == 2 || queryInfo.deviceType == 9 || queryInfo.deviceType == 10
        let powerBoolean = isPowerFn(queryInfo.deviceType)
        let power = powerBoolean ? (res.data.jk_1051 !== null ? res.data.jk_1051.toFixed(2) : 0.00) : ((res.data.jk_1031 + res.data.jk_1032 + res.data.jk_1033).toFixed(2))
        // 直流母线功率，储能变流器，用1056，不是用1077
        // let busBoolean = queryInfo.deviceType == 8 || queryInfo.deviceType == 10
        let busBoolean = isBusFn(queryInfo.deviceType)
        let bus = busBoolean ? (res.data.jk_1056 !== null ? res.data.jk_1056.toFixed(2) : 0.00) : (res.data.jk_1077 !== null ? res.data.jk_1077.toFixed(2) : 0.00)
        commit('SET_FLOWDATA', {
          power, // 电网功率
          cell: res.data.jk_1071 !== null ? res.data.jk_1071.toFixed(2) : 0.00, //电池功率
          load: (res.data.jk_1015 + res.data.jk_1016 + res.data.jk_1017).toFixed(2), // 负载功率
          photovoltaic: res.data.jk_1074 !== null ? res.data.jk_1074.toFixed(2) : 0.00, // 光伏功率
          bus: bus, // 直流母线功率
          chargingPiles: res.data.deviceMonitoringInfoRightChargingPiles ? res.data.deviceMonitoringInfoRightChargingPiles.map(item => {
            item.name = `${parseInt(item.dc) - 191000 + 1}#${i18n.t('充电桩')}`
            item.chargingPile_19006 = item.chargingPile_19006 !== null ? Number(item.chargingPile_19006).toFixed(2) : 0.00
            item.chargingPile_19017 = item.chargingPile_19017 !== null ? Number(item.chargingPile_19017).toFixed(2) : 0.00
            return item
          }) : [],
          em_10012s: isEmpty(res.data.em_10012s) ? 0.00 : Object.values(res.data.em_10012s).reduce((pre, cur) => pre + cur, 0),
          jk103133: res.data.jk31 !== null ? (res.data.jk_1031 + res.data.jk_1032 + res.data.jk_1033).toFixed(2) : 0.00
        })
      })
    },
    // 电量统计
    electricStatisticsFn({
      commit,
      state
    }, deviceSerialNumber) {
      return new Promise((resolve, reject) => {
        electricStatistics({
          ...state.queryInfo,
          deviceSerialNumber
        }).then(res => {
          if (res.code !== 200) return Message.error(res.msg)
          let electricMeteResListMain = {}
          if (res.data[0].electricMeterList) {
            let electricMeterList = res.data.map((item) => item.electricMeterList)
            let electricMeterFlatList = electricMeterList.flat(Infinity)
            let electricMeteResList = {}
            for (let i = 0; i < electricMeterList[0].length; i++) {
              electricMeteResList[`${electricMeterList[0][i].dcName}`] = [electricMeterFlatList
                .filter((item) => item.dcName === electricMeterList[0][i].dcName)
                .map((item) => item.reversePower), electricMeterFlatList
                  .filter((item) => item.dcName === electricMeterList[0][i].dcName)
                  .map((item) => item.forwardPower)]
              // electricMeteResList[`${electricMeterList[0][i].dcName}_${i18n.t('反向总电量')}`] = electricMeterFlatList
              //   .filter((item) => item.dcName === electricMeterList[0][i].dcName)
              //   .map((item) => item.reversePower)
              // electricMeteResList[`${electricMeterList[0][i].dcName}_${i18n.t('正向总电量')}`] = electricMeterFlatList
              //   .filter((item) => item.dcName === electricMeterList[0][i].dcName)
              //   .map((item) => item.forwardPower)
            }
            electricMeteResListMain = electricMeteResList
          }
          commit('SET_ELECTRICDATA', {
            times: res.data.map(item => item.dateTime),
            dischargeCapacityCalculate: res.data.map(item => item.dischargeCapacityCalculate), // 放电量
            chargeCapacityCalculate: res.data.map(item => item.chargeCapacityCalculate),
            photovoltaicPowerCapacityCalculate: res.data.map(item => item.photovoltaicPowerCapacityCalculate),
            jk_1083: res.data.map(item => item.jk_1083),
            jk_1084: res.data.map(item => item.jk_1084),
            electricMeterList: electricMeteResListMain,
            electricMeterListRes: res.data[0].electricMeterList ? res.data[0].electricMeterList : []
          })
          resolve()
        })
      })
    },
    // 导出报表
    exportDeviceMonitoringFn({
      state
    }, queryInfo) {
      return new Promise((resolve, reject) => {
        exportDeviceMonitoring({
          deviceSerialNumber: queryInfo.deviceSerialNumber,
          startTime: queryInfo.startTime,
          endTime: queryInfo.endTime,
          type: queryInfo.type,
          deviceType: queryInfo.deviceType,
          electricMeterList: queryInfo.electricMeterList
        }).then(res => {
          if (res.code) if (res.code !== 200) return Message.error(res.msg)
          handleExport(res, queryInfo.fileName)
          resolve()
        })
      })
    },
    /**
     * 组合类型
     */
    // 流动图
    selectDynamicGraphGroupFn({
      commit
    }, queryInfo) {
      selectDynamicGraphGroup({
        deviceSerialNumber: queryInfo.deviceSerialNumber,
      }).then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        let groupData = {}
        let groupPower = 0
        let groupCell = 0
        let groupLoad = 0
        let groupPhotovoltaic = 0
        let groupEm10012s = 0
        let groupJk103133s = 0
        queryInfo.groupId.forEach((item, index) => {
          let data = res.data.powerAnalysisStatistics[index]
          let type = queryInfo.groupType[index]
          let cell = data.jk_1071 !== null ? data.jk_1071.toFixed(2) : 0.00 // 电池功率
          groupCell += Number(cell)
          // 电网功率，有sts时1031~1033，无1051
          // let powerBoolean = type == 1 || type == 2 || type == 9 || type == 10
          let powerBoolean = isPowerFn(type)
          let power = powerBoolean ? (data.jk_1051 !== null ? data.jk_1051.toFixed(2) : 0.00) : ((data.jk_1031 + data.jk_1032 + data.jk_1033).toFixed(2))
          groupPower += Number(power)
          // 负载功率
          let load = (data.jk_1015 + data.jk_1016 + data.jk_1017).toFixed(2)
          groupLoad += Number(load)
          let photovoltaic = data.jk_1074 !== null ? data.jk_1074.toFixed(2) : 0.00 // 光伏功率
          groupPhotovoltaic += Number(photovoltaic)
          // 电表有功功率
          let em_10012s = isEmpty(data.em_10012s) ? 0.00 : Object.values(data.em_10012s).reduce((pre, cur) => pre + cur, 0)
          groupEm10012s += Number(em_10012s)
          // jk_1031+1032+1033
          let jk103133 = data.jk_1031 !== null ? (data.jk_1031 + data.jk_1032 + data.jk_1033).toFixed(2) : 0.00
          groupJk103133s += Number(jk103133)
          let label = ''
          if (index == 0) {
            label = `${i18n.t(`monitor['主机']`)} - ${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${i18n.t(`monitor['从机']`)} - ${index < 10 ? '0' + index : index}`
          }
          groupData[item] = {
            power,
            cell,
            load,
            photovoltaic,
            label,
            em_10012s,
            jk103133
          }
        })
        commit('SET_GROUPFLOWDATA', {
          power: res.data.groupData ? (res.data.zh_88100 !== null && res.data.zh_88100 ? res.data.zh_88100.toFixed(2) : 0.00) : groupPower.toFixed(2), // 电网功率
          cell: res.data.groupData ? (res.data.zh_88103 !== null && res.data.zh_88103 ? res.data.zh_88103.toFixed(2) : 0.00) : groupCell.toFixed(2), //电池功率
          load: res.data.groupData ? (res.data.zh_88101 !== null && res.data.zh_88101 ? res.data.zh_88101.toFixed(2) : 0.00) : groupLoad.toFixed(2), // 负载功率
          photovoltaic: res.data.groupData ? (res.data.zh_88102 !== null && res.data.zh_88102 ? res.data.zh_88102.toFixed(2) : 0.00) : groupPhotovoltaic.toFixed(2), // 光伏功率
          // bus: bus, // 直流母线功率
          groupData,
          em_10012s: res.data.groupData ? 0.00 : groupEm10012s.toFixed(2),
          jk103133: groupJk103133s.toFixed(2)
        })
      })
    },
    // 折线图
    powerAnalysisStatisticsGroupFn({
      commit, state
    }, queryInfo) {
      powerAnalysisStatisticsGroup({
        ...state.lineGroupQueryInfo,
        deviceSerialNumber: queryInfo.deviceSerialNumber,
        timeZone: queryInfo.timeZone,
      }).then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        if (!res.data.length) return
        let groupData = {}
        queryInfo.groupId.forEach((item, index) => {
          let data = res.data.map(item => item.powerAnalysisStatistics[index])
          let type = queryInfo.groupType[index]
          let cells = data.map(item => item.jk_1071) // 电池功率
          // 电网功率，有sts时1031~1033，无1051
          // let powerBoolean = type == 1 || type == 2 || type == 9 || type == 10
          let powerBoolean = isPowerFn(type)
          let powers = data.map(item => {
            if (powerBoolean) {
              if (item.jk_1051 !== null) return item.jk_1051.toFixed(2)
              return item.jk_1051
            } else {
              if (item.jk_1031 !== null) return (item.jk_1031 + item.jk_1032 + item.jk_1033).toFixed(2)
              return item.jk_1015
            }
          })
          // 负载功率
          let loads = data.map(item => {
            if (item.jk_1015 !== null) return (item.jk_1015 + item.jk_1016 + item.jk_1017).toFixed(2)
            return item.jk_1015
          })
          let photovoltaics = data.map(item => item.jk_1074) // 光伏功率
          let socs = data.map(item => item.bms_4022s) // soc
          let label = ''
          if (index == 0) {
            label = `${i18n.t(`monitor['主机']`)}(${index + 1 < 10 ? '0' + (index + 1) : index + 1})`
          } else {
            label = `${i18n.t(`monitor['从机']`)}(${index < 10 ? '0' + index : index})`
          }
          groupData[item] = {
            powers,
            cells,
            loads,
            socs,
            photovoltaics,
            label,
            em_10012s: data.map(item => isEmpty(item.em_10012s) ? 0.00 : Object.values(item.em_10012s).reduce((pre, cur) => pre + cur, 0)),
            jk103133: data.map(item => item.jk_1031 !== null ? (item.jk_1031 + item.jk_1032 + item.jk_1033).toFixed(2) : 0.00)
          }
        })
        let groupPowerS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur, curIndex) => {
          let powerBoolean = isPowerFn(queryInfo.groupType[curIndex])
          if (powerBoolean) {
            if (cur.jk_1051 !== null) return Number(Number(pre) + cur.jk_1051).toFixed(2)
            return Number(Number(pre) + cur.jk_1051)
          } else {
            if (cur.jk_1031 !== null) return Number(Number(pre) + (cur.jk_1031 + cur.jk_1032 + cur.jk_1033)).toFixed(2)
            return Number(Number(pre) + cur.jk_1015)
          }
        }, 0))
        let groupCellS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => Number(Number(pre) + cur.jk_1071).toFixed(2), 0))
        let groupLoadS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => {
          if (item.jk_1015 !== null) return Number(Number(pre) + (cur.jk_1015 + cur.jk_1016 + cur.jk_1017)).toFixed(2)
          return Number(Number(pre) + item.jk_1015)
        }, 0))
        let groupPhotovoltaicS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => Number(Number(pre) + cur.jk_1074).toFixed(2), 0))
        let groupEm10012s = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => {
          let em_10012s = isEmpty(cur.em_10012s) ? 0.00 : Object.values(cur.em_10012s).reduce((pre, cur) => pre + cur, 0)
          return Number(Number(pre) + em_10012s).toFixed(2)
        }, 0))
        let groupJk103133s = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => cur.jk_1031 !== null ? Number(Number(pre) + (cur.jk_1031 + cur.jk_1032 + cur.jk_1033)).toFixed(2) : 0.00, 0))
        commit('SET_POWERGROUPLINEDATA', {
          times: res.data.map(item => moment(item.sdt).format('HH: mm')),
          powers: res.data[0].groupData ? res.data.map(item => item.zh_88100) : groupPowerS,
          cells: res.data[0].groupData ? res.data.map(item => item.zh_88103) : groupCellS,
          loads: res.data[0].groupData ? res.data.map(item => item.zh_88101) : groupLoadS,
          photovoltaics: res.data[0].groupData ? res.data.map(item => item.zh_88102) : groupPhotovoltaicS,
          // bus
          groupData,
          em_10012s: res.data[0].groupData ? [] : groupEm10012s,
          jk103133: groupJk103133s
        })
      })
    },
    /**
     *  获取测点
     */
    async bindAliasQueryFn({ commit }, queryInfo) {
      const res = await bindAliasQuery(queryInfo)
      if (res.code !== 200) throw Message.error(res.msg)
      commit('SET_ALIASARR', res.data)
      return res.data
    },
    /**
     * 获取电池电芯数据
     */
    async getCellConfigFn({ state, commit }, { ac, deviceType, groupId }) {
      const res = await getCellConfig({ ac: ac })
      if (res.code !== 200) new Error(Message.error(res.msg))
      if (!res.data) {
        commit('SET_ISSHOWCELL', 0)
      } else {
        let isGroup = isGroupFn(deviceType)
        let { cellNumber, voltageNumber, temperatureNumber, electricityNumber, impedanceNumber, temperatureRiseNumber } = res.data
        function setData(data) {
          let cell = JSON.parse(JSON.stringify(data))
          cell.forEach(item => {
            let clusterLength = !isEmpty(item.bms_7101_7612) ? Object.keys(item?.bms_7101_7612).length / voltageNumber : 0
            item.clusterData = []
            for (let i = 1; i <= clusterLength; i++) {
              let cluster = {
                positive: !isEmpty(item.bms_9701_9800) ? item.bms_9701_9800[`bms_${9700 + ((i * 2) - 1)}`] : null, // 正极
                negative: !isEmpty(item.bms_9701_9800) ? item.bms_9701_9800[`bms_${9700 + (i * 2)}`] : null, // 正极
                data: []
              }
              for (let j = 1; j <= cellNumber; j++) {
                let point = {}
                if (!isEmpty(item.bms_7101_7612)) {
                  point.cellVoltage = j <= voltageNumber ? item.bms_7101_7612[`bms_${7100 + (voltageNumber * i) - (voltageNumber - j)}`] : '--' // 电压
                  point.voltageName = j <= voltageNumber ? `bms_${7100 + (voltageNumber * i) - (voltageNumber - j)}` : '--'
                } else {
                  point.cellVoltage = '--' // 电压
                  point.voltageName = j <= voltageNumber ? `bms_${7100 + (voltageNumber * i) - (voltageNumber - j)}` : '--'
                }
                if (!isEmpty(item.bms_7613_8124)) {
                  point.cellTemperature = j <= temperatureNumber ? item.bms_7613_8124[`bms_${7612 + (temperatureNumber * i) - (temperatureNumber - j)}`] : '--' // 温度
                  point.temperatureName = j <= temperatureNumber ? `bms_${7612 + (temperatureNumber * i) - (temperatureNumber - j)}` : '--'
                } else {
                  point.cellTemperature = '--' // 温度
                  point.temperatureName = j <= temperatureNumber ? `bms_${7612 + (temperatureNumber * i) - (temperatureNumber - j)}` : '--'
                }
                if (!isEmpty(item.bms_9150_9661)) {
                  point.cellTemperatureRise = j <= temperatureRiseNumber ? item.bms_9150_9661[`bms_${9149 + (temperatureRiseNumber * i) - (temperatureRiseNumber - j)}`] : '--' // 温升
                  point.TemperatureRiseName = j <= temperatureRiseNumber ? `bms_${9149 + (temperatureRiseNumber * i) - (temperatureRiseNumber - j)}` : '--'
                } else {
                  point.cellTemperatureRise = '--' // 温升
                  point.TemperatureRiseName = j <= temperatureRiseNumber ? `bms_${9149 + (temperatureRiseNumber * i) - (temperatureRiseNumber - j)}` : '--'
                }
                if (!isEmpty(item.bms_8125_8637)) {
                  point.cellCurrent = j <= electricityNumber ? item.bms_8125_8637[`bms_${8124 + (electricityNumber * i) - (electricityNumber - j)}`] : '--' // 电流
                  point.currentName = j <= electricityNumber ? `bms_${8124 + (electricityNumber * i) - (electricityNumber - j)}` : '--'
                } else {
                  point.cellCurrent = j <= '--' // 电流
                  point.currentName = j <= electricityNumber ? `bms_${8124 + (electricityNumber * i) - (electricityNumber - j)}` : '--'
                }
                if (!isEmpty(item.bms_8638_9149)) {
                  point.cellImpedance = j <= impedanceNumber ? item.bms_8638_9149[`bms_${8637 + (impedanceNumber * i) - (impedanceNumber - j)}`] : '--' // 阻抗
                  point.impedanceName = j <= impedanceNumber ? `bms_${8637 + (impedanceNumber * i) - (impedanceNumber - j)}` : '--'
                } else {
                  point.cellImpedance = '--' // 阻抗
                  point.impedanceName = j <= impedanceNumber ? `bms_${8637 + (impedanceNumber * i) - (impedanceNumber - j)}` : '--'
                }
                cluster.data.push(point)
              }
              item.clusterData.push({
                ...cluster,
                maxVoltage: maxBy(cluster.data, (item1) => { if (item1.cellVoltage != '--') return item1.cellVoltage })?.cellVoltage,
                minVoltage: minBy(cluster.data, (item1) => { if (item1.cellVoltage != '--') return item1.cellVoltage })?.cellVoltage,
                maxTemperature: maxBy(cluster.data, (item1) => { if (item1.cellTemperature != '--') return item1.cellTemperature })?.cellTemperature,
                minTemperature: minBy(cluster.data, (item1) => { if (item1.cellTemperature != '--') return item1.cellTemperature })?.cellTemperature,
                maxTemperatureRise: maxBy(cluster.data, (item1) => { if (item1.cellTemperatureRise != '--') return item1.cellTemperatureRise })?.cellTemperatureRise,
                minTemperatureRise: minBy(cluster.data, (item1) => { if (item1.cellTemperatureRise != '--') return item1.cellTemperatureRise })?.cellTemperatureRise,
              })
            }
          })

          if (isGroup) {
            return {
              cell
            }
          } else {
            commit('SET_PCSCELL', cell)
          }
        }
        if (isGroup) {
          groupId.forEach((item, index) => {
            let data = state.groupList.find(item1 => item1.id == item)
            let itemObj = setData(data?.cell ? data.cell : {})
            commit('SET_GROUPDATA', {
              [item]: itemObj
            })
          })
        } else {
          setData(state.pcs_cell)
        }
        commit('SET_ISSHOWCELL', 1)
      }
    }
  }
}

export default user
