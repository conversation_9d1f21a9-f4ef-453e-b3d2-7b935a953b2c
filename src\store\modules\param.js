import {
  macInfo,
  addMAC,
  sendParamMAC,
  editMAC,
  mdcInfo,
  addMDC,
  sendParamMDC,
  editMDC,
  bmsInfo,
  addBMS,
  sendParamBMS,
  editBMS,
  systemInfo,
  addSystem,
  sendParamSystem,
  editSystem,
  upgradeInfo,
  sendParamUpdate,
  getUpgradeProgress,
  onOffInfo,
  addOnOff,
  sendParamOnOff,
  editOnOff,
  getJsonData,
  sendParamIoOnOff
} from '@/api/monitors/param'
import {
  allJfpg
} from '@/api/operation/jfpg'
import {
  allPrice
} from '@/api/operation/price'
import {
  backupAll
} from '@/api/operation/backupScheme'
import {
  ossList
} from '@/api/operation/upgrade'
import {
  Message
} from 'element-ui';

const param = {
  namespaced: true,
  state: {
    macInfo: {},
    mdcInfo: {},
    bmsInfo: {},
    testResult: 0, // 召测结果，1为成功，2为失败
    systemInfo: {},
    onOffInfo: {},
    upgradeInfo: {},
    upgradeProgressInfo: [],
    jfpgOptions: [],
    priceOptions: [],
    backupOptions: [],
    ossOptions: [],
  },
  mutations: {
    SET_MACINFO: (state, data) => {
      state.macInfo = data
    },
    SET_MDCINFO: (state, data) => {
      state.mdcInfo = data
    },
    SET_BMSINFO: (state, data) => {
      state.bmsInfo = data
    },
    SET_TESTRESULT: (state, data) => {
      state.testResult = data
    },
    SET_SYSTEMINFO: (state, data) => {
      state.systemInfo = data
    },
    SET_ONOFFINFO: (state, data) => {
      state.onOffInfo = data
    },
    SET_UPGRADEINFO: (state, data) => {
      state.upgradeInfo = data
    },
    SET_UPGRADEPROGRESSINFO: (state, data) => {
      state.upgradeProgressInfo = data
    },
    SET_JFPGOPTIONS: (state, data) => {
      state.jfpgOptions = data
    },
    SET_PRICEOPTIONS: (state, data) => {
      state.priceOptions = data
    },
    SET_BACKUPOPTIONS: (state, data) => {
      state.backupOptions = data
    },
    SET_OSSOPTIONS: (state, data) => {
      state.ossOptions = data
    },
  },

  actions: {
    /**
     *
     * MAC
     */
    async macInfoFn({
      commit
    }, queryInfo) {
      const res = await macInfo(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      commit('SET_MACINFO', res.data)
      return res.data
    },
    async addMACFn({
      commit, dispatch
    }, data) {
      const res = await addMAC(data)
        if (res.code !== 200) return Message.error(res.msg)
      return
    },
    async editMACFn({
      commit, dispatch
    }, data) {
      const res = await editMAC(data)
        if (res.code !== 200) return Message.error(res.msg)
        return
    },
    // 参数下发
    sendParamMACFn({state}, data) {
      return sendParamMAC(data).then(res => {
        if (res.code !== 200) {
          Message.error(res.msg)
          throw res.msg
        }
      })
    },
    /**
     *
     * 获取削峰填谷及分时电价下拉框数据
     */
    // 削峰填谷及分时电价
    allJfpgFn({
      commit
    }) {
      allJfpg().then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        commit('SET_JFPGOPTIONS', res.data)
      })
    },
    async allPriceFn({
      commit
    }, queryInfo) {
      const res= await allPrice(queryInfo)
      if (res.code !== 200) throw Message.error(res.msg)
      commit('SET_PRICEOPTIONS', res.data)
      return
    },
    allBackUpFn({
      commit
    }) {
      backupAll().then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        commit('SET_BACKUPOPTIONS', res.data)
      });
    },
    /**
     *
     * MDC
     */
    async mdcInfoFn({
      commit
    }, queryInfo) {
      const res = await mdcInfo(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      commit('SET_MDCINFO', res.data)
      return res.data
    },
    async addMDCFn({
      commit
    }, data) {
      const res = await addMDC(data)
      if (res.code !== 200) return Message.error(res.msg)
      return
    },
    async editMDCFn({
      commit, dispatch
    }, data) {
      const res = await editMDC(data)
      if (res.code !== 200) return Message.error(res.msg)
      return
    },
    // 参数下发
    sendParamMDCFn({ state }, data) {
      return sendParamMDC(data).then(res => {
        if (res.code !== 200) {
          Message.error(res.msg)
          throw res.msg
        }
      })
    },
    /**
    *
    * BMS
    */
    async bmsInfoFn({
      commit
    }, queryInfo) {
      const res = await bmsInfo(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      commit('SET_BMSINFO', res.data)
      return res.data
    },
    async addBMSFn({
      commit
    }, data) {
      const res = await addBMS(data)
        if (res.code !== 200) return Message.error(res.msg)
        return
    },
    async editBMSFn({
      commit, dispatch
    }, data) {
      const res = await editBMS(data)
      if (res.code !== 200) return Message.error(res.msg)
      return
    },
    // 参数下发
    sendParamBMSFn({ state }, data) {
      return sendParamBMS(data).then(res => {
        if (res.code !== 200) {
          Message.error(res.msg)
          throw res.msg
        }
      })
    },
    /**
    *
    * 策略
    */
    async systemInfoFn({
      commit, dispatch
    }, queryInfo) {
      const res = await systemInfo(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      if (!res.data) return
      if (queryInfo.type == 'one') {
        if (res.data.setting1900 == "1") {
          dispatch('getJsonDataFn', { ac: queryInfo.ac, cutTop: 1 }).then(() => {
            commit('SET_TESTRESULT', 1)
          }).catch(() => {
            commit('SET_TESTRESULT', 2)
          })
        }
      }
      commit('SET_SYSTEMINFO', res.data)
      return res.data
    },
    async addSystemFn({
      commit
    }, data) {
      const res = await addSystem(data)
        if (res.code !== 200) return Message.error(res.msg)
        return
    },
    async editSystemFn({
      commit, dispatch
    }, data) {
      const res = await editSystem(data)
      if (res.code !== 200) return Message.error(res.msg)
      return
    },
    // 参数下发
    sendParamSystemFn({ state }, data) {
      return sendParamSystem(data).then(res => {
        if (res.code !== 200) {
          Message.error(res.msg)
          throw res.msg
        }
      })
    },
    /**
     * 升级
     */
    async upgradeInfoFn({
      commit
    }, queryInfo) {
      const res = await upgradeInfo(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      commit('SET_UPGRADEINFO', res.data)
      return res.data
    },
    // 参数下发
    sendParamUpdateFn({ state }, data) {
      return sendParamUpdate(data).then(res => {
        if (res.code !== 200) {
          Message.error(res.msg)
          throw res.msg
        }
      })
    },
    allOssListFn({
      commit
    }, queryInfo) {
      ossList({
        pageNum: 1,
        pageSize: 100,
        ...queryInfo
      }).then(res => {
        if (res.code !== 200) return Message.error(res.msg)
        commit('SET_OSSOPTIONS', res.rows)
      });
    },
    async getUpgradeProgressFn({ commit }, queryInfo) {
      const res = await getUpgradeProgress(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      commit('SET_UPGRADEPROGRESSINFO', res.data)
      return res.data
    },
    /**
    *
    * 系统开关机
    */
    async onOffInfoFn({
      commit
    }, queryInfo) {
      const res = await onOffInfo(queryInfo)
      if (res.code !== 200) return Message.error(res.msg)
      commit('SET_ONOFFINFO', res.data)
      return res.data
    },
    async addOnOffFn({
      commit
    }, data) {
      const res = await addOnOff(data)
        if (res.code !== 200) return Message.error(res.msg)
        return
    },
    async editOnOffFn({
      commit, dispatch
    }, data) {
      const res = await editOnOff(data)
      if (res.code !== 200) return Message.error(res.msg)
      return
    },
    // 参数下发
    sendParamOnOffFn({ state }, data) {
      return sendParamOnOff(data).then(res => {
        if (res.code !== 200) {
          Message.error(res.msg)
          throw res.msg
        }
      })
    },
    /**
     * 获取设备参数，无返回结果
     */
    async getJsonDataFn({}, data) {
      const res = await getJsonData(data)
      if (res.code !== 200) new Error(res.msg)
      return
    },
    /**
     * 动态测点下发
     */
    async sendParamIoOnOff({ }, data) {
      const res = await sendParamIoOnOff(data)
      if (res.code !== 200) throw Message.error(res.msg)
    }
  }
}

export default param
