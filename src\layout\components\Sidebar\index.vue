<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-01-03 17:56:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-17 10:44:14
 * @FilePath: \elecloud_platform-main\src\layout\components\Sidebar\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div :class="{ 'has-logo': showLogo }"
    :style="{ backgroundColor: 'var(--menu-back-ground)' }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper" :style="{height: showLogo ? 'calc(100% - 114px)' :'calc(100% - 50px)'}">
      <el-menu :default-active="activeMenu" :collapse="isCollapse"
        background-color="var(--menu-back-ground)"
        text-color="var(--base-menu-color)"
        :unique-opened="true" active-text-color="var(--base-menu-color-active)" :collapse-transition="false" mode="vertical">
        <sidebar-item v-for="(route, index) in sidebarRouters" :key="route.path + index" :item="route"
          :base-path="route.path" style="padding: 0 12px;" />
      </el-menu>
    </el-scrollbar>
    <div class="sidebar-version" v-if="$auth.hasRoleOr(['admin', 'OPS'])">V{{ packageJson.version }}</div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";
import packageJson from "../../../../package.json";

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      packageJson
    }
  },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    }
  }
};
</script>
<style>
.el-menu-item {
  font-size: 16px;
  display: flex;
  align-items: center;
}

.el-submenu__title {
  font-size: 16px;
  display: flex;
  align-items: center;
}

.el-submenu__title i {
  font-size: 14px;
  font-weight: 600;
}

.sidebar-version {
  color: #fff;
  position: absolute;
  bottom: 0px;
  text-align: center;
  width: 100%;
  height: 50px;
  font-size: 16px;
  line-height: 50px;
}
</style>
