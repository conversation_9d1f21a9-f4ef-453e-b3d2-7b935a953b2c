/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-03-05 16:51:49
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-03-11 10:40:16
 * @FilePath: \elecloud_platform-main\src\api\operation\backupScheme.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取列表
export function commandLogList(queryInfo) {
  return request({
    url: '/system/commandLog/list',
    method: 'get',
    params: queryInfo
  })
}

// 获取vnc信息
export function getSelectVncInfo(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/selectVncInfo/${queryInfo.ac}/${queryInfo.uuid}`,
    method: 'get'
  })
}
