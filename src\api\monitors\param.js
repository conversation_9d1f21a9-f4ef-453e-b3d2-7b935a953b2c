/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-03-04 09:01:07
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-05-30 16:26:34
 * @FilePath: \elecloud_platform-main\src\api\monitors\param.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

/**
 * MAC
 */
// 获取MAC参数信息
export function macInfo(queryInfo) {
  return request({
    url: `/system/MACsetting/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}

// 新增MAC参数
export function addMAC(data) {
  return request({
    url: '/system/MACsetting',
    method: 'post',
    data
  })
}

// 参数下发MAC
export function sendParamMAC(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonMAC',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 修改
export function editMAC(data) {
  return request({
    url: '/system/MACsetting',
    method: 'put',
    data
  })
}

/**
 *
 * MDC
 */
// 获取MDC参数信息
export function mdcInfo(queryInfo) {
  return request({
    url: `/system/MDCsetting/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}

// 新增MDC参数
export function addMDC(data) {
  return request({
    url: '/system/MDCsetting',
    method: 'post',
    data
  })
}

// 参数下发MDC
export function sendParamMDC(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonMDC',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 修改
export function editMDC(data) {
  return request({
    url: '/system/MDCsetting',
    method: 'put',
    data
  })
}

/**
 *
 * 电池
 */
// 获取电池参数信息
export function bmsInfo(queryInfo) {
  return request({
    url: `/system/batterySetting/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}

// 新增电池参数
export function addBMS(data) {
  return request({
    url: '/system/batterySetting',
    method: 'post',
    data
  })
}

// 参数下发电池
export function sendParamBMS(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonBattery',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 修改
export function editBMS(data) {
  return request({
    url: '/system/batterySetting',
    method: 'put',
    data
  })
}

/**
 *
 * 策略
 */
// 获取策略参数信息
export function systemInfo(queryInfo) {
  return request({
    url: `/system/policySetting/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}

// 新增策略参数
export function addSystem(data) {
  return request({
    url: '/system/policySetting',
    method: 'post',
    data
  })
}

// 参数下发策略
export function sendParamSystem(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonPolicy',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 修改
export function editSystem(data) {
  return request({
    url: '/system/policySetting',
    method: 'put',
    data
  })
}

/**
 * 升级
 */
// 获取升级参数信息
export function upgradeInfo(queryInfo) {
  return request({
    url: `/system/upgradeSetting/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}
// 参数下发
export function sendParamUpdate(data) {
  return request({
    url: '/system/sendMqtt/upgradeJson',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 获取升级进度
export function getUpgradeProgress(queryInfo) {
  return request({
    url: `/system/deviceMonitoring/selectUpgradeInfo/${queryInfo.ac}/${queryInfo.uuid}`,
    method: 'get'
  })
}

/**
 *
 * 系统开关机
 */
// 获取系统开关机参数信息
export function onOffInfo(queryInfo) {
  return request({
    url: `/system/setting/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}

// 新增系统开关机参数
export function addOnOff(data) {
  return request({
    url: '/system/setting',
    method: 'post',
    data
  })
}

// 参数下发系统开关机
export function sendParamOnOff(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonSystem',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 修改
export function editOnOff(data) {
  return request({
    url: '/system/setting',
    method: 'put',
    data
  })
}


// 获取设备参数值
export function getJsonData(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonTimerTask',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 参数下发，动态测点
export function sendParamIoOnOff(data) {
  return request({
    url: '/system/sendMqtt/argumentsJsonDynamicPoint',
    method: 'post',
    data,
    timeout: 65000
  })
}

// 获取电芯数据配置详细信息
export function getCellConfig(queryInfo) {
  return request({
    url: `/system/BMSCellConfig/getInfoByAc/${queryInfo.ac}`,
    method: 'get'
  })
}
