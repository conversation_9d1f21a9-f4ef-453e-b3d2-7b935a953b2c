<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-08-18 11:57:16
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-18 16:15:50
 * @FilePath: \elecloud_platform-main\src\components\DeptTree\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="box-tree elevation-4">
    <div class="head-container">
      <el-input v-model="deptName" :placeholder="$t(`dept['Please enter department name']`)" clearable size="small"
        prefix-icon="el-icon-search" style="margin-bottom: 20px" />
    </div>
    <div class="head-container" style="height: 95%;overflow: auto;">
      <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false" :filter-node-method="filterNode"
        ref="deptTreeRef" node-key="id" default-expand-all highlight-current @node-click="handleNodeClick" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { deptTreeSelect } from "@/api/system/user";

const emits = defineEmits(['nodeClick'])

// 部门名称
const deptName = ref(undefined)
const defaultProps = ref({
  children: "children",
  label: "label"
})
// 部门树选项
const deptOptions = ref([])
const deptTreeRef = ref(null)

// 筛选节点
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
}
// 节点单击事件
const handleNodeClick = (data) => {
  emits('nodeClick', data.id)
}

// 根据名称筛选部门树
watch(deptName, (val) =>  deptTreeRef.value.filter(val))

/** 查询部门下拉树结构 */
const getDeptTree = () => {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data;
  });
}
getDeptTree()
</script>

<style lang="scss" scoped>
.box-tree {
  width: 320px;
  background-color: #fff;
  margin-right: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  padding: 20px;
}
</style>
