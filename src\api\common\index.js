/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-02-23 11:45:04
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-02-23 14:54:17
 * @FilePath: \elecloud_platform-main\src\api\common\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取地址
export function getAddressByIP(ip) {
  return request({
    url: `/common/getAddressByIP/${ip}`,
    method: 'get'
  })
}
