/*
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-12 16:48:50
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-03-19 17:28:11
 * @FilePath: \elecloud_platform-main\src\utils\parseBinaryToText.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 将十进制转二进制的反向数组数据根据点位展示具体信息
 */
import { decimalToBinaryReverseArray } from './ruoyi'
import i18n from '@/lang'

/**
 * num为十进制
 */
/**
 * 监控显示屏
 */
// 系统工作模式 1001 bit3、bit4、bit5
export const getWorkState = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 4) return i18n.t('common.normal')
  if (res.length == 4) return i18n.t(`monitor['Battery rechargeable no']`)
  if (res.length == 5) return i18n.t(`monitor['Battery dischargeable no']`)
  if (res.length >= 6) {
    let bit3 = res[3]
    let bit4 = res[4]
    let bit5 = res[5]
    if (bit3 == 1 && bit4 == 0 && bit5 == 0) return i18n.t(`monitor['Battery rechargeable no']`)
    if (bit3 == 0 && bit4 == 1 && bit5 == 0) return i18n.t(`monitor['Battery dischargeable no']`)
    if (bit3 == 0 && bit4 == 0 && bit5 == 1) return i18n.t('common.BatteryNoStartable')
    if (bit3 == 1 && bit4 == 0 && bit5 == 1) return i18n.t('common.BatteryNoStartable')
    if (bit3 == 1 && bit4 == 1 && bit5 == 1) return i18n.t('common.normal')
    return '--'
  }
}
// 监控列表，系统工作模式 1001 bit3\bit4，bit1\bit2，优先显示bit3\bit4
export const getListWorkState = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 4) return getOnAndOff(num)
  if (res.length == 4) return i18n.t(`monitor['Battery rechargeable no']`)
  if (res.length == 5) return i18n.t(`monitor['Battery dischargeable no']`)
  if (res.length >= 6) {
    let bit3 = res[3]
    let bit4 = res[4]
    let bit5 = res[5]
    if (bit3 == 1 && bit4 == 0) return i18n.t(`monitor['Battery rechargeable no']`)
    if (bit3 == 0 && bit4 == 1) return i18n.t(`monitor['Battery dischargeable no']`)
    // if (bit3 == 0 && bit4 == 0 && bit5 == 1) return i18n.t('common.BatteryNoStartable')
    // if (bit3 == 1 && bit4 == 0 && bit5 == 1) return i18n.t('common.BatteryNoStartable')
    if (bit3 == 0 && bit4 == 0) return getOnAndOff(num)
    return '--'
  }
}


// 系统工作模式 1001 bit1、bit2
export const getOnAndOff = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 2) return '--'
  if (res.length == 2) return res[1] == 1 ? i18n.t('common.TurnOn') : '--'
  if (res.length > 2) {
    if (res[1] == 1) {
      return i18n.t('common.TurnOn')
    } else if (res[2] == 1) {
      return i18n.t('common.Closure')
    } else {
      return '--'
    }
  }
}

// 并离网状态 1001 bit14
export const get1001Bit14 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 15) return i18n.t('common.GridConnection')
  return res[14] == 1 ? i18n.t('common.OffGrid') : i18n.t('common.GridConnection')
}

// IO状态位 1003
export const get1003 = (num, bit) => {
  let n = parseInt(num)
  // if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (bit == 2 && res[2] == 0) return i18n.t('common.normal')
  if (bit == 2 && res[2] == 1) return i18n.t('common.fault')
  if (bit == 3 && res[3] == 0) return i18n.t('common.normal')
  if (bit == 3 && res[3] == 1) return i18n.t('common.fault')
  if (bit == 4 && res[4] == 0) return i18n.t('common.normal')
  if (bit == 4 && res[4] == 1) return i18n.t('common.fault')
  if (bit == 5 && res[5] == 0) return i18n.t('common.normal')
  if (bit == 5 && res[5] == 1) return i18n.t('common.fault')
  // if (res.length < 3) return '--'
  // if (res.length == 3) {
  //   if (bit == 2 && res[2] == 0) return i18n.t('common.normal')
  //   if (bit == 2 && res[2] == 1) return i18n.t('common.fault')
  //   if (bit == 3) return '--'
  //   if (bit == 4) return '--'
  //   if (bit == 5) return '--'
  // }
  // if (res.length == 4) {
  //   if (bit == 2 && res[2] == 0) return i18n.t('common.normal')
  //   if (bit == 2 && res[2] == 1) return i18n.t('common.fault')
  //   if (bit == 3 && res[3] == 0) return i18n.t('common.normal')
  //   if (bit == 3 && res[3] == 1) return i18n.t('common.fault')
  //   if (bit == 4) return '--'
  //   if (bit == 5) return '--'
  // }
  // if (res.length == 5) {
  //   if (bit == 2 && res[2] == 0) return i18n.t('common.normal')
  //   if (bit == 2 && res[2] == 1) return i18n.t('common.fault')
  //   if (bit == 3 && res[3] == 0) return i18n.t('common.normal')
  //   if (bit == 3 && res[3] == 1) return i18n.t('common.fault')
  //   if (bit == 4 && res[4] == 0) return i18n.t('common.normal')
  //   if (bit == 4 && res[4] == 1) return i18n.t('common.fault')
  //   if (bit == 5) return '--'
  // }
  // if (res.length == 6) {
  //   if (bit == 2 && res[2] == 0) return i18n.t('common.normal')
  //   if (bit == 2 && res[2] == 1) return i18n.t('common.fault')
  //   if (bit == 3 && res[3] == 0) return i18n.t('common.normal')
  //   if (bit == 3 && res[3] == 1) return i18n.t('common.fault')
  //   if (bit == 4 && res[4] == 0) return i18n.t('common.normal')
  //   if (bit == 4 && res[4] == 1) return i18n.t('common.fault')
  //   if (bit == 5 && res[5] == 0) return i18n.t('common.normal')
  //   if (bit == 5 && res[5] == 1) return i18n.t('common.fault')
  // }
}

// 自动模式状态 1001 bit15 策略状态
export const get1001Bit15 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 16) return i18n.t(`common['停止']`)
  return res[15] == 1 ? i18n.t(`common.running`) : i18n.t(`common['停止']`)
}

/**
 * AC
 */
// 并离网状态 2015 bit4
export const get2015Bit4 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 5) return i18n.t('common.GridConnection')
  return res[4] == 1 ? i18n.t('common.OffGrid') : i18n.t('common.GridConnection')
}

// 充放电 2015 bit5
export const get2015Bit5 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 6) return i18n.t('common.Discharge')
  return res[5] == 1 ? i18n.t('common.Charge') : i18n.t('common.Discharge')
}

// 运行状态 2015 bit6
export const get2015Bit6 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 7) return i18n.t('common.Shutdown')
  return res[6] == 1 ? i18n.t('common.running') : i18n.t('common.Shutdown')
}

// 故障状态 2015 bit7
export const get2015Bit7 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 8) return i18n.t('common.normal')
  if (res.length >= 8) {
    return res[7] == 1 ? i18n.t('common.fault') : i18n.t('common.normal')
  }
}

/**
 * DC
 */
// 运行状态 3004 bit6
export const get3004Bit6 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 7) return i18n.t('common.Shutdown')
  return res[6] == 1 ? i18n.t('common.running') : i18n.t('common.Shutdown')
}

// 故障状态 3004 bit7
export const get3004Bit7 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 8) return i18n.t('common.normal')
  return res[7] == 1 ? i18n.t('common.fault') : i18n.t('common.normal')
}

/**
 * STS
 */
// 设备状态 3502 bit0
export const get3002Bit0 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length == 1 && res[0] == 1) return i18n.t('common.Closure2')
  if (res.length == 1 && res[0] == 0) return i18n.t('common.switchOff')
  return res[0] == 1 ? i18n.t('common.Closure2') : i18n.t('common.switchOff')
}

// 发电机状态 3502 bit10,0断开,1闭合
export const isShow3502Bit10 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 10) return false
  if (res.length == 10) {
    return res[10] == 1 ? true : false
  }
  if (res.length > 11) {
    return res[10] == 1 ? true : false
  }
}

/**
 * BMS
 */
// 电池空调状态 4058
export const get4058 = (num, bit) => {
  let n = parseInt(num)
  // if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  if (bit == 1 && res[1] == 0) return i18n.t(`common['停止']`)
  if (bit == 1 && res[1] == 1) return i18n.t('common.running')
  if (bit == 2 && res[2] == 0) return i18n.t(`common['停止']`)
  if (bit == 2 && res[2] == 1) return i18n.t('common.running')
  if (bit == 3 && res[3] == 0) return i18n.t(`common['停止']`)
  if (bit == 3 && res[3] == 1) return i18n.t('common.running')
  if (bit == 4 && res[4] == 0) return i18n.t(`common['停止']`)
  if (bit == 4 && res[4] == 1) return i18n.t('common.running')
  if (bit == 5 && res[5] == 0) return i18n.t(`common['停止']`)
  if (bit == 5 && res[5] == 1) return i18n.t('common.running')
  if (bit == 6 && res[6] == 0) return i18n.t(`common['停止']`)
  if (bit == 6 && res[6] == 1) return i18n.t('common.running')
  // if (res.length > 7) return '--'
  // if (res.length <= 7) {
  //   if (res.length == 1) {
  //     if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  //     if (bit == 1) return '--'
  //     if (bit == 2) return '--'
  //     if (bit == 3) return '--'
  //     if (bit == 4) return '--'
  //     if (bit == 5) return '--'
  //     if (bit == 6) return '--'
  //   }
  //   if (res.length == 2) {
  //     if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  //     if (bit == 1 && res[1] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 1 && res[1] == 1) return i18n.t('common.running')
  //     if (bit == 2) return '--'
  //     if (bit == 3) return '--'
  //     if (bit == 4) return '--'
  //     if (bit == 5) return '--'
  //     if (bit == 6) return '--'
  //   }
  //   if (res.length == 3) {
  //     if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  //     if (bit == 1 && res[1] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 1 && res[1] == 1) return i18n.t('common.running')
  //     if (bit == 2 && res[2] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 2 && res[2] == 1) return i18n.t('common.running')
  //     if (bit == 3) return '--'
  //     if (bit == 4) return '--'
  //     if (bit == 5) return '--'
  //     if (bit == 6) return '--'
  //   }
  //   if (res.length == 4) {
  //     if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  //     if (bit == 1 && res[1] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 1 && res[1] == 1) return i18n.t('common.running')
  //     if (bit == 2 && res[2] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 2 && res[2] == 1) return i18n.t('common.running')
  //     if (bit == 3 && res[3] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 3 && res[3] == 1) return i18n.t('common.running')
  //     if (bit == 4) return '--'
  //     if (bit == 5) return '--'
  //     if (bit == 6) return '--'
  //   }
  //   if (res.length == 5) {
  //     if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  //     if (bit == 1 && res[1] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 1 && res[1] == 1) return i18n.t('common.running')
  //     if (bit == 2 && res[2] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 2 && res[2] == 1) return i18n.t('common.running')
  //     if (bit == 3 && res[3] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 3 && res[3] == 1) return i18n.t('common.running')
  //     if (bit == 4 && res[4] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 4 && res[4] == 1) return i18n.t('common.running')
  //     if (bit == 5) return '--'
  //     if (bit == 6) return '--'
  //   }
  //   if (res.length == 6) {
  //     if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  //     if (bit == 1 && res[1] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 1 && res[1] == 1) return i18n.t('common.running')
  //     if (bit == 2 && res[2] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 2 && res[2] == 1) return i18n.t('common.running')
  //     if (bit == 3 && res[3] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 3 && res[3] == 1) return i18n.t('common.running')
  //     if (bit == 4 && res[4] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 4 && res[4] == 1) return i18n.t('common.running')
  //     if (bit == 5 && res[5] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 5 && res[5] == 1) return i18n.t('common.running')
  //     if (bit == 6) return '--'
  //   }
  //   if (res.length == 7) {
  //     if (bit == 0 && res[0] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 0 && res[0] == 1) return i18n.t('common.running')
  //     if (bit == 1 && res[1] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 1 && res[1] == 1) return i18n.t('common.running')
  //     if (bit == 2 && res[2] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 2 && res[2] == 1) return i18n.t('common.running')
  //     if (bit == 3 && res[3] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 3 && res[3] == 1) return i18n.t('common.running')
  //     if (bit == 4 && res[4] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 4 && res[4] == 1) return i18n.t('common.running')
  //     if (bit == 5 && res[5] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 5 && res[5] == 1) return i18n.t('common.running')
  //     if (bit == 6 && res[6] == 0) return i18n.t(`common['停止']`)
  //     if (bit == 6 && res[6] == 1) return i18n.t('common.running')
  //   }
  // }
}
