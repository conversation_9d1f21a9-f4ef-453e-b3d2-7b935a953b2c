@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

:root {
  --primary-color: #0093b6;// 主题色
  --base-sidebar-width: 270px; // 侧边栏宽度
  --base-menu-color: #fff; // 侧边栏文字颜色
  --base-menu-color-active: #fff; // 侧边栏激活文字颜色
  --base-color: #000000E6; // 页面文字颜色
  --menu-back-ground: #0093b6; // 侧边栏背景颜色
  --menu-logo-back-ground: #0093b6; // 侧边栏logo背景颜色
  --menu-back-ground-active: #29a4c2; // 主题浅色
}


body {
  height: 100%;
  // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  // overflow-x: hidden;

  font-family: Open Sans, Inter, sans-serif, -apple-system, blinkmacsystemfont, Segoe UI, roboto, Helvetica Neue, arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol !important;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
  // overflow: hidden;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 18px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.header-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: var(--base-color);
}

.input_box {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  color: var(--base-color);


  .header-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
  }

  .input_ment {
    margin-right: 20px;
    display: inline-block;

    span {
      font-size: 14px;
    }

    .el-select {
      width: 140px;
    }
  }

  .input_ment:last-child {
    margin: 0;
  }
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.suffix {
  position: absolute;
  height: 100%;
  right: 5px;
  top: 0;
  text-align: center;
  color: #C0C4CC;
  transition: all 0.3s;
  pointer-events: none;
}

.amap-logo {
  display: none !important;
}

.amap-copyright {
  display: none !important;
}

.red {
  color: #d81e06;
}

.green {
  color: #67c23a;
}

.warn {
  color: #E6A23C;
}

// 复制
.copy {
  cursor: pointer;
  margin-left: 5px;
}

.copy:hover {
  color: #409eff;
}

.page-box {
  width: 100%;
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.page-two-box {
  display: flex;
  width: 100%;
  height: 100%;
}

.page-two-box-content {
  background: #fff;
  border-radius: 8px;
  flex: 1;
  overflow: auto;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.elevation-6 {
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, .2), 0 6px 10px 0 rgba(0, 0, 0, .14), 0 1px 18px 0 rgba(0, 0, 0, .12) !important;
}
.elevation-4 {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, .2),0 4px 5px 0 rgba(0, 0, 0, .14),0 1px 10px 0 rgba(0, 0, 0, .12)!important;
}

// 告警详情
.primary {
  color: var(--primary-color)
}
.error {
  color: #ff4949;
}

.detail_box {
  color: #333;
  &-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  &-content {
    padding-left: 20px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
    div {
      display: flex;
      align-items: center;
      span:first-child {
        color: #606266;
      }
    }
  }
}
.detail_problem {
  color: #333;
  margin-top: 20px;
  &-small-box {
    padding-left: 20px;
  }
  &-big-title {
    font-size: 16px;
    font-weight: 600;
  }
  &-title {
    color: #000;
    margin: 20px 0 10px 0;
  }
  &-content {
    width: 100%;
    padding: 20px;
    background: #f5f5f5;
    border: 1px solid rgba(218, 225, 240, 0.3);
    border-radius: 3px;
    line-height: 1.5;
  }
}

#nprogress .bar {
  background: var(--primary-color) !important;
}
#nprogress .peg {
  background: 0 0 10px var(--primary-color), 0 0 5px var(--primary-color) !important;
}