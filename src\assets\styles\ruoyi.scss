 /**
 * 通用css样式布局处理
 * Copyright (c) 2019 ruoyi
 */

 /** 基础通用 **/
 .pt5 {
   padding-top: 5px;
 }

 .pr5 {
   padding-right: 5px;
 }

 .pb5 {
   padding-bottom: 5px;
 }

 .mt5 {
   margin-top: 5px;
 }

 .mr5 {
   margin-right: 5px;
 }

 .mb5 {
   margin-bottom: 5px;
 }

 .mb8 {
   margin-bottom: 8px;
 }

 .ml5 {
   margin-left: 5px;
 }

 .mt10 {
   margin-top: 10px;
 }

 .mr10 {
   margin-right: 10px;
 }

 .mb10 {
   margin-bottom: 10px;
 }

 .ml10 {
   margin-left: 10px;
 }

 .mt20 {
   margin-top: 20px;
 }

 .mr20 {
   margin-right: 20px;
 }

 .mb20 {
   margin-bottom: 20px;
 }

 .ml20 {
   margin-left: 20px;
 }

 .h1,
 .h2,
 .h3,
 .h4,
 .h5,
 .h6,
 h1,
 h2,
 h3,
 h4,
 h5,
 h6 {
   font-family: inherit;
   font-weight: 500;
   line-height: 1.1;
   color: inherit;
 }

 .el-message-box__status+.el-message-box__message {
   word-break: break-word;
 }

 .el-dialog:not(.is-fullscreen) {
   margin-top: 6vh !important;
 }

 .el-dialog__wrapper.scrollbar .el-dialog .el-dialog__body {
   overflow: auto;
   overflow-x: hidden;
   max-height: 70vh;
   padding: 10px 20px 0;
 }

 .el-table {

   .el-table__header-wrapper,
   .el-table__fixed-header-wrapper {
     th {
       word-break: break-word;
       background-color: #f8f8f9;
       color: #515a6e;
       height: 40px;
       font-size: 16px;
     }
   }

   .el-table__body-wrapper {
     .el-button [class*="el-icon-"]+span {
       margin-left: 1px;
     }
   }
 }

 /** 表单布局 **/
 .form-header {
   font-size: 16px;
   color: #6379bb;
   border-bottom: 1px solid #ddd;
   margin: 8px 10px 25px 10px;
   padding-bottom: 5px
 }

 /** 表格布局 **/
 .pagination-container {
   // position: relative;
   // height: 25px;
   // margin-bottom: 10px;
   // margin-top: 15px;
   // padding: 10px 20px !important;
 }

 /* tree border */
 .tree-border {
   margin-top: 5px;
   border: 1px solid #e5e6e7;
   background: #FFFFFF none;
   border-radius: 4px;
 }

 // .pagination-container .el-pagination {
 // 	right: 0;
 // 	position: absolute;
 // }

 @media (max-width : 768px) {
   .pagination-container .el-pagination>.el-pagination__jump {
     display: none !important;
   }

   .pagination-container .el-pagination>.el-pagination__sizes {
     display: none !important;
   }
 }

 .el-table .fixed-width .el-button--mini {
   padding-left: 0;
   padding-right: 0;
   width: inherit;
 }

 /** 表格更多操作下拉样式 */
 .el-table .el-dropdown-link,
 .el-table .el-dropdown-selfdefine {
   cursor: pointer;
   margin-left: 5px;
 }

 .el-table .el-dropdown,
 .el-icon-arrow-down {
   font-size: 16px;
 }

 .el-tree-node__content>.el-checkbox {
   margin-right: 8px;
 }

 .list-group-striped>.list-group-item {
   border-left: 0;
   border-right: 0;
   border-radius: 0;
   padding-left: 0;
   padding-right: 0;
 }

 .list-group {
   padding-left: 0px;
   list-style: none;
 }

 .list-group-item {
   border-bottom: 1px solid #e7eaec;
   border-top: 1px solid #e7eaec;
   margin-bottom: -1px;
   padding: 11px 0px;
   font-size: 16px;
 }

 .pull-right {
   float: right !important;
 }

 .el-card__header {
   padding: 14px 15px 7px;
   min-height: 40px;
 }

 .el-card__body {
   padding: 15px 20px 20px 20px;
 }

 .card-box {
   padding-right: 15px;
   padding-left: 15px;
   margin-bottom: 20px;
 }

 /* button color */
 .el-button--cyan.is-active,
 .el-button--cyan:active {
   background: #20B2AA;
   border-color: #20B2AA;
   color: #FFFFFF;
 }

 .el-button--cyan:focus,
 .el-button--cyan:hover {
   background: #48D1CC;
   border-color: #48D1CC;
   color: #FFFFFF;
 }

 .el-button--cyan {
   background-color: #20B2AA;
   border-color: #20B2AA;
   color: #FFFFFF;
 }

 /* text color */
 .text-navy {
   color: #1ab394;
 }

 .text-primary {
   color: inherit;
 }

 .text-success {
   color: #1c84c6;
 }

 .text-info {
   color: #23c6c8;
 }

 .text-warning {
   color: #f8ac59;
 }

 .text-danger {
   color: #ed5565;
 }

 .text-muted {
   color: #888888;
 }

 /* image */
 .img-circle {
   border-radius: 50%;
 }

 .img-lg {
   width: 120px;
   height: 120px;
 }

 .avatar-upload-preview {
   position: relative;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
   width: 200px;
   height: 200px;
   border-radius: 50%;
   box-shadow: 0 0 4px #ccc;
   overflow: hidden;
 }

 /* 拖拽列样式 */
 .sortable-ghost {
   opacity: .8;
   color: #fff !important;
   background: #42b983 !important;
 }

 .top-right-btn {
   position: relative;
   float: right;
 }
