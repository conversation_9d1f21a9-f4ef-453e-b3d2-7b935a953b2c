<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2024-01-03 17:56:01
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-07-03 16:49:22
 * @FilePath: \elecloud_platform-main\src\views\system\user\profile\resetPwd.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="auto">
    <el-form-item :label="$t('user.oldPassword')" prop="oldPassword">
      <el-input v-model="user.oldPassword" :placeholder="$t(`user['Please enter your old password']`)" type="password" show-password/>
    </el-form-item>
    <el-form-item :label="$t('user.newPassword')" prop="newPassword">
      <el-input v-model="user.newPassword" :placeholder="$t(`user['Please enter your new password']`)" type="password" show-password/>
    </el-form-item>
    <el-form-item :label="$t('user.confirmPassword')" prop="confirmPassword">
      <el-input v-model="user.confirmPassword" :placeholder="$t(`user['Please confirm the new password']`)" type="password" show-password/>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">{{ $t('common.save') }}</el-button>
      <el-link type="info" :underline="false" style="margin-left: 20px" @click="$router.push('/forgetPwd')">{{ $t('不记得原来的密码？') }}</el-link>
      <!-- <el-button type="danger" size="mini" @click="close">{{ $t('common.Closure') }}</el-button> -->
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserPwd } from "@/api/system/user";

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error(this.$t('user.two')));
      } else {
        callback();
      }
    };
    return {
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: this.$t(`user['Old password cannot be empty']`), trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: this.$t(`user['New password cannot be empty']`), trigger: "blur" },
          { min: 6, max: 20, message: this.$t(`user['6 to 20 characters in length']`), trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: this.$t(`user['Confirm password cannot be empty']`), trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(response => {
            this.$modal.msgSuccess(this.$t(`common['Modify successfully']`));
          });
        }
      });
    },
    close() {
      this.$tab.closePage();
    }
  }
};
</script>
