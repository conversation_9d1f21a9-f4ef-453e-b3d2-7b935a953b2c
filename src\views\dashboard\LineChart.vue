<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';
import resize from './mixins/resize'
import _ from 'lodash'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '98%'
    },
    height: {
      type: String,
      default: '95%'
    },
    xData: {
      type: Array,
      required: true
    },
    yData: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
    }
  },
  watch: {
    yData: {
      deep: true,
      handler(newValue, oldValue) {
        if (this.yData.length) this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      if (this.chart) this.chart.dispose()
      this.chart = echarts.init(this.$el)
      this.chart.clear();
      let options = {
        // title: {
        //   subtext: `${this.$t('common.unit')}：kWh`
        // },
        xAxis: {
          type: 'category',
          data: this.xData,
          axisTick: {
            show: false
          }
        },
        dataZoom: [
          {
            type: 'inside',
          }
        ],
        grid: {
          left: '2%',
          right: '2%',
          bottom: 10,
          top: 45,
          containLabel: true
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10],
          formatter: (params) => {
            let htmlStart = `<div><div>${params[0].name}</div>`
            params.forEach((param, index) => {
              if (!this.yData.length) return
              let name = null
              let dc = this.yData[index].module
              if (dc.indexOf('13') !== -1) {
                name = `${parseInt(dc) - 131000 + 1}#Monet-AC`
              } else if (dc.indexOf('14') !== -1) {
                name = `${parseInt(dc) - 141000 + 1}#Monet-DC`
              } else if (dc.indexOf('16') !== -1) {
                name = `${parseInt(dc) - 161000 + 1}#BMS`
              } else {
                name = null
              }
              htmlStart += `
                <div style="display: flex;align-items: center;">
                  <span style="display: inline-block;background: ${param.color};width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                  ${this.yData[index].ac}${name !== null ? ' '+'-'+' '+name+' -': ''} ${this.yData[index].moduleName !== null ? ' '+'-'+' '+this.yData[index].moduleName+' -': ''} ${param.seriesName}：
                  <span style="font-weight: 600;margin-right: 3px;">${param.value !== null && param.value !== undefined ? _.round(param.value, 2) : '--'}</span> ${this.yData[index].unit}
                </div>`
            })
            return htmlStart + '</div>'
          }
        },
        yAxis: {
          type: 'value',
          splitLine: { show: false },
        },
        legend: {
          type: 'scroll',
          icon: 'circle'
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: '',
            textAlign: 'center',
            fill: '#000',
            width: 30,
            height: 30,
            fontSize: 16
          }
        },
        series: [],
        color: [
          '#2ec7c9',
          '#b6a2de',
          '#5ab1ef',
          '#ffb980',
          '#d87a80',
          '#8d98b3',
        ]
      }
      options.series = []
      this.yData.forEach(item => {
        options.series.push(
          {
            name: item.name,
            data: item.values,
            type: 'line',
            symbol: 'none',
            smooth: true,
            ac: item.ac,
            unit: item.unit,
            module: item.module
          }
        )
      })
      options && this.chart.setOption(options)
    }
  }
}
</script>
