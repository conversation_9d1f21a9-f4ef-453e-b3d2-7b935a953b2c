<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: Administrator <EMAIL>
 * @LastEditTime: 2025-03-03 16:03:50
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <div v-for="(ioItem, index) in stsIo" :key="ioItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ $t('消防') }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot"
        style="margin-bottom: 20px">
        <el-descriptions-item :label="$t('monitor.onLineState')">
          <!-- <span v-if="ioItem['ff_21000']">{{ ioItem['ff_21000'] == 1 ? $t('alarm.title'): $t('common.normal') }}</span> -->
          <span v-if="ioItem.onLineState">{{ $t(ioItem.onLineState) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <template v-for="i in 200">
          <el-descriptions-item :label="getPointInfo(ioItem, 'ff_21001_21100', i).alias"
            v-if="i <= 100 && getPointInfo(ioItem, 'ff_21001_21100', i).alias != $t('未知别名')" :key="i">
            <span v-if="getPointInfo(ioItem, 'ff_21001_21100', i).value">{{ getPointInfo(ioItem, 'ff_21001_21100',
              i).value == 1 ? $t('common.Closure2') : $t('common.switchOff') }}</span>
            <span v-else>--</span>
          </el-descriptions-item>
          <el-descriptions-item :label="getPointInfo(ioItem, 'ff_21101_21200', i).alias"
            v-if="i > 100 && getPointInfo(ioItem, 'ff_21101_21200', i).alias != $t('未知别名')" :key="i">
            <span v-if="getPointInfo(ioItem, 'ff_21101_21200', i).value">{{ getPointInfo(ioItem, 'ff_21101_21200',
              i).value == 1 ? $t('common.Closure2') : $t('common.switchOff') }}</span>
            <span v-else>--</span>
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
  </div>
</template>
<script>
export default {
  name: "device",
  data() {
    return {
    };
  },
  computed: {
    stsIo() {
      let data = this.$store.state.monitor.pcs_stsIo
      data.forEach(item => {
        for (let i = 1; i < 101; i++) {
          let key = `21${String(i).padStart(3, '0')}`
          let value = this.aliasArr.find(item => item.point == key)
          if (value) {
            item.ff_21001_21100[`ff_${key}_alias`] = value.alias
          } else {
            item.ff_21001_21100[`ff_${key}_alias`] = this.$t('未知别名')
          }
        }
        for (let i = 101; i < 201; i++) {
          let key = `21${String(i).padStart(3, '0')}`
          let value = this.aliasArr.find(item => item.point == key)
          if (value) {
            item.ff_21101_21200[`ff_${key}_alias`] = value.alias
          } else {
            item.ff_21101_21200[`ff_${key}_alias`] = this.$t('未知别名')
          }
        }
      })
      return data
    },
    // 别名
    aliasArr() {
      let data = this.$store.state.monitor.aliasArr
      return data
    },
    getPointInfo() {
      return (item, type, index) => {
        let key = `21${String(index).padStart(3, '0')}`
        return {
          alias: item[type][`ff_${key}_alias`],
          value: item[type][`ff_${key}`]
        }
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;

  .el-button {
    float: right;
    margin-bottom: 20px;
  }

  .el_box {
    border-top: 1px solid #BFBFBF;
    border-left: 1px solid #BFBFBF;
    margin-bottom: 30px;

    .el-col {
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #BFBFBF;
      border-right: 1px solid #BFBFBF;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .el-col::after {
      content: attr(data-label);
      display: none;
    }

    .el-col:hover {
      overflow: visible;
      text-overflow: clip;
    }

    .left {
      text-align: left;
      padding: 0 10px 0 10px;
    }

    .right {
      text-align: right;
      padding: 0 10px 0 10px;
    }
  }

}
</style>
