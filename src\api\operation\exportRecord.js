/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-03-05 16:51:49
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-04-01 17:00:30
 * @FilePath: \elecloud_platform-main\src\api\operation\backupScheme.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取列表
export function exportRecordList(queryInfo) {
  return request({
    url: '/system/exportLog/list',
    method: 'get',
    params: queryInfo
  })
}

// 删除
export function deleteExportRecord(queryInfo) {
  return request({
    url: `/system/exportLog/${queryInfo.ids}`,
    method: 'delete'
  })
}

// 下载文件
export function downloadOss(queryInfo) {
  return request({
    url: '/common/downloadOSS',
    method: 'get',
    params: queryInfo,
    responseType: "blob",
    timeout: 60000
  })
}
