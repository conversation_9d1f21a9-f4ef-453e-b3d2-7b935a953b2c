<template>
  <!-- SIM列表 -->
  <div class="page-two-box">
    <div style="position: absolute;left: 0;top: 45%;" @click="handleTreeExpand">
      <svg-icon icon-class="tree-expand" class-name="icon" style="height: 50px;"
        :style="{ transform: treeExpand ? 'rotate(0deg)' : 'rotate(180deg)' }" />
    </div>
    <DeptTree @nodeClick="nodeClick" v-show="treeExpand" />
    <div class="page-two-box-content">
      <div class="input_box">
        <div class="header-title">
          {{ $route.meta.title }}
        </div>
        <div>
          <div class="input_ment">
            <el-select v-model="queryInfo.status" clearable :placeholder="$t(`sim['Please select iot card status']`)">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="input_ment">
            <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
              <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
                <el-option :label="'SN'" value="ac"></el-option>
                <el-option :label="$t('sim.card')" value="sim"></el-option>
                <el-option :label="'ICCID'" value="iccid"></el-option>
                <el-option :label="$t('创建人员')" value="createBy"></el-option>
              </el-select>
            </el-input>
          </div>
          <div class="input_ment">
            <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
              }}</el-button>
          </div>
          <div class="input_ment">
            <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus" v-hasPermi="['system:sim:add']">{{
              $t('sim.add') }}</el-button>
          </div>
        </div>
      </div>
      <div class="table_box">
        <!-- table -->
        <el-table :data="tableData" v-loading="loading" style="width: 100%;">
          <el-table-column type="index" label="#" width="60" align="center" />
          <el-table-column prop="sim" :label="$t('sim.card')" show-overflow-tooltip min-width="180" align="center">
            <template slot-scope="scope">
              {{ scope.row.sim }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.sim"
                v-clipboard:success="copySuccess"></i>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="$t('sim.status')" show-overflow-tooltip min-width="120" align="center">
            <template slot-scope="scope">
              <el-tag :type="getStatusCom(scope.row.status).type">{{ getStatusCom(scope.row.status).label }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="iccid" label="ICCID" show-overflow-tooltip width="240" align="center">
            <template slot-scope="scope">
              {{ scope.row.iccid }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.iccid"
                v-clipboard:success="copySuccess"></i>
            </template>
          </el-table-column>
          <el-table-column prop="packageName" :label="$t('sim.plan')" show-overflow-tooltip min-width="120"
            align="center" />
          <el-table-column prop="usedFlow" :label="$t('sim.use')" show-overflow-tooltip width="200" align="center" />
          <el-table-column prop="residualFlow" :label="$t('sim.residue')" show-overflow-tooltip width="200"
            align="center" />
          <el-table-column prop="ac" :label="$t('sim.Bsn')" show-overflow-tooltip width="200" align="center">
            <template slot-scope="scope">
              {{ scope.row.ac }}<i class="el-icon-copy-document copy" v-clipboard:copy="scope.row.ac"
                v-clipboard:success="copySuccess"></i>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" :label="$t('创建人员')" show-overflow-tooltip align="center" min-width="140" />
          <el-table-column prop="startTime" :label="$t('sim.startTime')" show-overflow-tooltip min-width="140"
            align="center" />
          <el-table-column prop="endTime" :label="$t('sim.expire')" show-overflow-tooltip min-width="140"
            align="center" />
          <el-table-column fixed="right" :label="$t('common.handle')" width="180" align="center">
            <template slot-scope="scope">
              <el-button @click="handleRechargeClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:sim:recharge']" style="padding: 0;">{{ $t('sim.topUp') }}</el-button>
              <el-button @click="handleAllotClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:sim:allotSim']" style="padding: 0;">{{ $t('分配SIM卡') }}</el-button>
              <el-button @click="handleDeleteClick(scope.row)" type="text" size="small"
                v-hasPermi="['system:sim:delete']" style="padding: 0;">{{ $t('common.delete') }}</el-button>
              <!-- <el-button @click="handleClick(scope.row)" type="text" size="small">{{ $t('sim.unBind') }}</el-button>
            <el-button @click="del(scope.row)" type="text" size="small">{{ $t('common.logout') }}</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
          @pagination="getList" style="margin-top: 20px;text-align: right;" />
      </div>
      <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px"
        :title="$t('sim.add')">
        <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
          <el-form-item :label="`${$t('sim.sn')}`" prop="ac">
            <el-input v-model="ruleForm.ac"
              :placeholder="$t(`device['Please enter machine serial number (screen)']`)" />
          </el-form-item>
          <el-form-item label="ICCID" prop="iccid" class="form-iccid">
            <span slot="label" style="display: flex;align-items: center;">
              ICCID
              <el-popover placement="bottom" trigger="hover">
                <div style="display: flex;justify-content: center;flex-direction: column;align-items: center;">
                  <!-- <img src="../../../assets/images/sim.jpg" style="width: 120px;height: 100px;" /> -->
                  <img style="width: 120px;height: 100px;"
                    :src="'data:image/jpeg;base64,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******************************************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'" />
                </div>
                <div slot="reference" style="margin-left: 3px;">
                  <i class="el-icon-question"></i>
                </div>
              </el-popover>
            </span>
            <el-input v-model="ruleForm.iccid" :placeholder="$t('请输入ICCID')" maxlength="20" show-word-limit />
          </el-form-item>
        </el-form>
        <span>{{ $t('注：ICCID在卡的后面，有20位字符。') }}</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
        </span>
      </el-dialog>
      <!-- 充值页面 -->
      <el-dialog :visible.sync="dialogUrlVisible" center :title="$t('sim.topUp')" :modal-append-to-body="false"
        width="600px" :destroy-on-close="true">
        <div v-if="env == 'zh' || 'test'">
          {{ $t('请到微信公众号进行充值，微信公众号为：') }} <span style="color: red;font-weight: 600;">中创物联网</span>
        </div>
        <i-frame :src="url" style="height: 400px" v-else />
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogUrlVisible = false">{{ $t('common.Closure') }}</el-button>
        </span>
      </el-dialog>
      <!-- 分配SIM -->
      <el-dialog :visible.sync="dialogAllotVisible" center :modal-append-to-body="false" width="600px"
        :title="$t('分配SIM卡')">
        <div style="margin-bottom: 20px">{{ $t('提示：手动选择或者输入搜索选择') }}</div>
        <el-form :model="allotForm" :rules="allotRules" ref="allotFormRef" label-width="auto">
          <el-form-item :label="`${$t('所要分配的用户')}`" prop="userId">
            <el-select v-model="allotForm.userId" :placeholder="$t('common.select')" filterable style="width: 100%;">
              <el-option v-for="item in userList" :key="item.userId" :label="item.userName" :value="item.userId" />
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCancelAllotClick()">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleConfirmAllot('allotFormRef')">{{ $t('common.confirm') }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { simList, addSim, deleteSim, rechargeSim, unbindSim, allotSIM, deleteSIM } from '@/api/property/sim'
import { listUser } from '@/api/system/user'

import iFrame from "@/components/iFrame/index";
import DeptTree from '@/components/DeptTree'

export default {
  components: { iFrame, DeptTree },
  data() {
    return {
      options: [
        {
          value: undefined,
          label: this.$t('common.all')
        },
        {
          value: 2,
          label: this.$t('sim.active')
        },
        {
          value: 3,
          label: this.$t('common.Shutdown')
        },
        {
          value: 7,
          label: this.$t('sim.off')
        },
        {
          value: 1,
          label: this.$t('sim.cActive')
        },
        {
          value: 6,
          label: this.$t('sim.test')
        },
        {
          value: 11,
          label: this.$t('sim.stock')
        },
        {
          value: 15,
          label: this.$t('sim.pre')
        },
        {
          value: 88,
          label: this.$t('sim.maintaining')
        },
      ],
      dialogVisible: false,
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      ruleForm: {
        ac: '',
        iccid: ''
      },
      rules: {
        ac: [
          { required: true, message: this.$t(`device['Please enter machine serial number (screen)']`), trigger: 'blur' }
        ],
        iccid: [
          { required: true, message: this.$t('请输入ICCID'), trigger: 'blur' },
          { min: 20, max: 20, message: this.$t('长度为20个字符'), trigger: 'blur' }
        ],
      },
      url: '',
      dialogUrlVisible: false,
      /**
       * 分配sim
       */
      allotRules: {
        userId: [
          { required: true, message: this.$t('请选择要分配的用户'), trigger: 'blur' }
        ],
      },
      userList: [],
      allotForm: {
        simId: '',
        userId: '',
      },
      dialogAllotVisible: false,
      // 搜索
      searchKey: 'ac',
      searchValue: '',
      treeExpand: false
    };
  },
  computed: {
    getStatusCom() {
      return (status) => {
        if (!status) return {
          label: this.$t('未知'),
          type: ''
        }
        let label = this.options.find(item => item.value == status).label
        let type = ''
        if (status == 2) {
          type = ''
        } else if (status == 3) {
          type = 'warning'
        } else if (status == 7) {
          type = 'danger'
        } else {
          type = 'info'
        }
        return {
          label,
          type
        }
      }
    },
    ...mapGetters([
      'userInfo',
    ]),
    env() {
      return process.env.VUE_APP_SCREEN
    }
  },
  mounted() {
    this.getList()
    this.listUserFn()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    del(row) {
      console.log(row, '注销')
    },
    handleClick(row) {
      console.log(row, '解绑');
    },
    // 获取列表
    getList() {
      this.loading = true
      simList({
        pageNum: this.queryInfo.pageNum,
        pageSize: this.queryInfo.pageSize,
        status: this.queryInfo.status,
        [this.searchKey]: this.searchValue,
        deptId: this.queryInfo.deptId
      }).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          addSim({
            ac: this.ruleForm.ac,
            iccid: this.ruleForm.iccid
          }).then(response => {
            if (response.code !== 200) return this.$message({
              type: 'error',
              message: this.$t(`common['Addition Failed']`)
            });
            this.$message({
              type: 'success',
              message: this.$t(`common['Added successfully']`)
            })
            this.getList()
            this.dialogVisible = false
            this.dialogQueryVisible = false
          })
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogVisible = true;
      this.ruleForm = {
        ac: '',
        iccid: ''
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    // 充值
    handleRechargeClick(row) {
      if (this.env == 'zh' || this.env == 'test' || this.env == 'dev') {
        this.dialogUrlVisible = true
      } else {
        this.url = ''
        rechargeSim({ iccid: row.iccid }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Addition Failed']`)
          });
          // this.url = res.data
          // this.dialogUrlVisible = true
          window.open(res.data, '_blank');
        })
      }
    },
    // 复制成功
    copySuccess() {
      this.$modal.msgSuccess(this.$t('复制成功'))
    },
    /**
     * 分配SIM
     */
    handleAllotClick(row) {
      this.allotForm.simId = row.id
      this.dialogAllotVisible = true
    },
    handleCancelAllotClick() {
      this.dialogAllotVisible = false
      this.allotForm.userId = ''
    },
    handleConfirmAllot(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          allotSIM({
            simId: this.allotForm.simId,
            userId: this.allotForm.userId
          }).then(response => {
            if (response.code !== 200) return this.$message({
              type: 'error',
              message: this.$t('分配失败')
            });
            this.$message({
              type: 'success',
              message: this.$t('分配成功')
            })
            this.getList()
            this.dialogAllotVisible = false
          })
        }
      });
    },
    // 获取用户
    async listUserFn() {
      const res = await listUser({
        pageNum: 1,
        pageSize: 9999,
        deptId: this.userInfo.deptId
      })
      this.userList = res.rows.filter(item => item.deptId !== this.userInfo.deptId)
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t('device.deleteDeviceHint'), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        // let api = this.adminRole ? deleteAdminDevice: deleteDevice
        let api = deleteSIM
        api({ id: row.id }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
    nodeClick(id) {
      this.queryInfo.deptId = id
      this.getList()
    },
    handleTreeExpand() {
      this.treeExpand = !this.treeExpand
    }
  },
}
</script>

<style lang="scss" scoped>
:deep(.form-iccid) {
  .el-form-item__label {
    width: 80px !important;
    display: flex
  }
}
</style>
