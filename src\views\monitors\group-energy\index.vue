<!-- 储能 -->
<template>
  <div id="box">
    <div class="header">
      <div>
        <el-tooltip class="item" effect="dark" :content="control && control['onLineState']" placement="top">
          <svg-icon icon-class="solve" style="margin-right: 5px;" v-if="control['onLineState'] == '在线'" />
          <svg-icon icon-class="solve-off" style="margin-right: 5px;" v-else-if="control['onLineState'] == '离线'" />
          <svg-icon icon-class="solve-off" style="margin-right: 5px;" v-else />
        </el-tooltip>
        <span style="margin-right: 10px;">{{ baseInfo.projectName }} - <b>{{ baseInfo.deviceName }}</b></span>
      </div>
      <div>

        <span style="margin-right: 5px;color: #999;">{{ control.sdt }}({{ baseInfo.timeZone }})</span>
        <el-tooltip class="item" effect="dark" :content="$t('tagsView.refresh')" placement="top">
          <el-button size="mini" circle icon="el-icon-refresh" @click="handleRefreshClick(1)" />
        </el-tooltip>
      </div>
    </div>
    <div class="top">
      <div class="top_box">
        <div class="icon_box">
          <svg-icon icon-class="capacity" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.capacity }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem1') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.deviceBatteryCapacity" :startVal="0" :endVal="baseInfo.deviceBatteryCapacity"
              :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box">
        <div class="icon_box">
          <svg-icon icon-class="ratedPower" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.ratedPower }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem2') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.deviceRatedPower" :startVal="0" :endVal="baseInfo.deviceRatedPower"
              :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kW</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="isShowPhotovoltaicInstalledCapacity">
        <div class="icon_box">
          <svg-icon icon-class="capacityPower" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.capacityPower }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem6') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.photovoltaicInstalledCapacity" :startVal="0"
              :endVal="baseInfo.photovoltaicInstalledCapacity" :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWp</span>
          </div>
        </div>
      </div>
      <div class="top_box">
        <div class="icon_box">
          <svg-icon icon-class="running" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.running }" />
        </div>
        <div class="data_box">
          <div style="margin-bottom: 10px">
            <div class="p">{{ $t('monitor.topItem3') }}</div>
            <span v-if="getStatus == $t('common.offline') || getStatus == $t('common.Closure')" class="red">{{ getStatus }}</span>
            <span v-else-if="getStatus == $t('common.online') || getStatus == $t('common.TurnOn')" class="green">{{ getStatus }}</span>
            <span v-else>{{ getStatus }}</span>
          </div>
        </div>
      </div>
      <div class="top_box" v-if="isShowPhotovoltaicInstalledCapacity">
        <div class="icon_box">
          <svg-icon icon-class="guangfu" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.guangfu }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem7') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.dayPhotovoltaicPowerCapacityCalculate" :startVal="0"
              :endVal="baseInfo.dayPhotovoltaicPowerCapacityCalculate" :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box">
        <div class="icon_box">
          <svg-icon icon-class="charge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.charge }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem4') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.dayOutputOfPlant" :startVal="0" :endVal="baseInfo.dayOutputOfPlant"
              :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
      <div class="top_box">
        <div class="icon_box">
          <svg-icon icon-class="discharge" class-name="icon" class="svg-img" :style="{ color: $store.state.common.baseImg.discharge }" />
        </div>
        <div class="data_box">
          <div class="p">{{ $t('monitor.topItem5') }}</div>
          <div style="margin-bottom: 10px">
            <count-to v-if="baseInfo.dayElectricityConsumption" :startVal="0"
              :endVal="baseInfo.dayElectricityConsumption" :duration="2000" :decimals="2" />
            <span v-else>0.00</span>
            <span class="unit">kWh</span>
          </div>
        </div>
      </div>
    </div>
    <div class="tabs">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :label="$t('DataSummary')" name="operations">
          <operations ref="operationsRef" :lineDateInit="control.sdt"></operations>
        </el-tab-pane>
        <el-tab-pane :label="$t('faultMessage')" name="third">
          <fault ref="faultRef"></fault>
        </el-tab-pane>
        <el-tab-pane :label="$t(`param['参数设置']`)" name="parameter" v-if="!isUserRole">
          <parameter v-if="activeName == 'parameter'"></parameter>
        </el-tab-pane>
        <el-tab-pane :label="$t('历史数据')" name="history" v-if="isShowHistory()">
          <History v-if="activeName == 'history'"></History>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import operations from "./operations.vue";
import fault from "./fault.vue";
import parameter from "./parameter.vue";
import History from './history.vue'
import CountTo from 'vue-count-to'
import { getOnAndOff } from '@/utils/parseBinaryToText'
import auth from '@/plugins/auth'
import { isPhotovoltaicFn, isEnergyFn } from '@/hook/useDeviceType'

// import autofit from 'autofit.js'

export default {
  components: {
    operations,
    fault,
    parameter,
    CountTo,
    History
  },
  data() {
    return {
      activeName: 'operations',
      nowTime: '',
      time: ''
    }
  },
  mounted() {
    // autofit.init({
    //   dh: 1080,
    //   dw: 1920,
    //   el: "#box",
    //   resize: true
    // })
    this.handleRefreshClick(2)

    this.time = setInterval(() => {
      this.handleRefreshClick()
    }, 300000);
  },
  destroyed() {
    clearInterval(this.time)
  },
  computed: {
    baseInfo() {
      return this.$store.state.monitor.baseInfo
    },
    control() {
      let currentNodeKey = this.$store.state.monitor.currentNodeKey
      let data = this.$store.state.monitor.groupList[0]
      if (data && Object.keys(data).length == 11) return data.control
      return {
        ac: "null",
        createTime: null,
        isAnalysis: 0,
        jk_1000: null,
        jk_1001: null,
        jk_1002: null,
        jk_1003: null,
        jk_1004: null,
        jk_1005: null,
        jk_1015: null,
        jk_1016: null,
        jk_1017: null,
        jk_1031: null,
        jk_1032: null,
        jk_1033: null,
        jk_1051: null,
        jk_1074: null,
        jk_1092: null,
        jk_1093: null,
        jk_1094: null,
        jk_1095: null,
        jk_1105: null,
        jk_1106: null,
        onLineState: "离线",
        sdt: null,
      }
    },
    type() {
      return this.$route.query.type
    },
    // 光伏装机容量
    isShowPhotovoltaicInstalledCapacity() {
      return this.type == 10000 || this.type == 10001
    },
    // 储能充放电量
    isShowDayOutputOfPlant() {
      return isEnergyFn(this.type)
    },
    // 是否为三级用户
    isUserRole() {
      let roles = this.$store.state.user.roles
      return roles.findIndex(item => item == 'common') !== -1
    },
    getStatus() {
      if (this.control.isAnalysis == 0) {
        return this.control.onLineState == '在线' ? this.$t('common.online') : this.$t('common.offline')
      } else if (this.control.isAnalysis == 1) {
        return this.getOnAndOffFn(this.control['jk_1001'])
      } else {
        return '--'
      }
    }
  },
  methods: {
    isShowHistory() {
      return auth.hasPermi('system:serviceData:list')
    },
    handleClick(tab, event) {
      if (tab.name == 'operations') {
        this.$store.dispatch('powerAnalysisStatisticsGroupFn', {
          deviceSerialNumber: this.$route.query.id,
          deviceType: this.$route.query.type,
          timeZone: this.$route.query.time,
          groupId: this.$route.query.groupId.split(','),
          groupType: this.$route.query.groupType.split(','),
        })
        this.$store.dispatch('selectDynamicGraphGroupFn', {
          deviceSerialNumber: this.$route.query.id,
          deviceType: this.$route.query.type,
          groupId: this.$route.query.groupId.split(','),
          groupType: this.$route.query.groupType.split(','),
        })
        this.$store.dispatch('electricStatisticsFn', this.$route.query.id)
      } else if (tab.name == 'third') {
        this.$refs.faultRef.getList()
      }
    },
    handleRefreshClick(type) {
      // type为1是点击刷新按钮获取统计
      // type为2是下发拉取参数只需调用一次
      if (type == 1) {
        this.$store.dispatch('electricStatisticsFn', this.$route.query.id)
      }
      this.nowTime = this.parseTime(new Date())
      this.$store.dispatch('deviceMonitoringDetailTopFn', {
        deviceSerialNumber: this.$route.query.id,
        deviceType: this.$route.query.type
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', {
        deviceSerialNumber: this.$route.query.id,
        type: 'control',
        deviceType: this.$route.query.type
      }).then((res) => {
        this.$store.commit('SET_LINEGROUPQUERYINFO', {
          date: res[this.$route.query.groupId.split(',')[0]].sdt
        })
        this.$store.dispatch('powerAnalysisStatisticsGroupFn', {
          deviceSerialNumber: this.$route.query.id,
          deviceType: this.$route.query.type,
          timeZone: this.$route.query.time,
          groupId: this.$route.query.groupId.split(','),
          groupType: this.$route.query.groupType.split(','),
        })
        if (type == 2) this.getJsonFn(res)
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', {
        deviceSerialNumber: this.$route.query.id,
        type: 'pcs',
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(',')
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', {
        deviceSerialNumber: this.$route.query.id,
        type: 'bms',
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(',')
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 电表
        deviceSerialNumber: this.$route.query.id,
        type: 'electricMeter',
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(',')
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 外设
        deviceSerialNumber: this.$route.query.id,
        type: 'peripherals',
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(',')
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 充电桩
        deviceSerialNumber: this.$route.query.id,
        type: 'chargingPile',
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(',')
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // 电芯
        deviceSerialNumber: this.$route.query.id,
        type: 'BMSCell',
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(',')
      })
      this.$store.dispatch('deviceMonitoringDetailRightFn', { // bms-bau
        deviceSerialNumber: this.$route.query.id,
        type: 'bms-bau',
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(',')
      })
      this.$store.dispatch('selectDynamicGraphGroupFn', {
        deviceSerialNumber: this.$route.query.id,
        deviceType: this.$route.query.type,
        groupId: this.$route.query.groupId.split(','),
        groupType: this.$route.query.groupType.split(','),
      })
    },
    getOnAndOffFn(num) {
      return getOnAndOff(num)
    },
    // 下发拉取参数
    getJsonFn(groupData) {
      function getControlData(ac) {
        let data = groupData[ac]
        if (data) return data
        return {
          ac: "null",
          createTime: null,
          isAnalysis: 0,
          jk_1000: null,
          jk_1001: null,
          jk_1002: null,
          jk_1003: null,
          jk_1004: null,
          jk_1005: null,
          jk_1015: null,
          jk_1016: null,
          jk_1017: null,
          jk_1031: null,
          jk_1032: null,
          jk_1033: null,
          jk_1051: null,
          jk_1056: null,
          jk_1074: null,
          jk_1077: null,
          jk_1092: null,
          jk_1093: null,
          jk_1094: null,
          jk_1095: null,
          jk_1105: null,
          jk_1106: null,
          onLineState: "离线",
          sdt: null,
        }
      }

      let groupList = this.$route.query.groupId.split(',')
      groupList.forEach((item, index) => {
        let control = getControlData(item)
        if (control.onLineState && control['onLineState'] != '离线') {
          // 类型 1:策略类 2:开关机 5:MAC 6:MDC 7:电池
          // if (!index) {
            this.$store.dispatch('param/getJsonDataFn', { ac: item, types: [1, 2, 5, 6, 7] })
            this.$store.dispatch('param/systemInfoFn', { ac: control.ac, type: 'one' })
        }
      })

    }
  },
}
</script>

<style lang="scss" scoped>
#box {
  /* min-height: 100vh; */
  width: 100%;
  background-color: #f7f7f7;
  /* position: absolute; */
  padding: 10px;
  overflow: auto !important;
  height: 100%;
  min-width: 1912px;

  .header {
    border-radius: 14px;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    line-height: 50px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .top {
    border-radius: 14px;
    height: 100px;
    background-color: white;
    display: flex;
    margin-bottom: 10px;

    .top_box {
      flex: 1;
      display: flex;

      .icon_box {
        flex: 0.5;

        .eding-img {
          width: 60px;
          height: 60px;
          margin-top: 20px;
        }

        img {
          height: 64px;
          width: 64px;
          float: right;
          margin-top: 20px;
          margin-right: 10px;
        }
      }

      .data_box {
        flex: 1;
        display: flex;
        flex-direction: column;
        /* justify-content: center; */
        justify-content: space-evenly;

        .p {
          color: var(--base-color);
          /* margin-top: 20px; */
          /* margin-bottom: 10px; */
          display: block;
          text-align: left;
          font-size: 16px;
          height: 42px;
          display: flex;
          align-items: center;
        }

        span {
          font-size: 24px;
          font-weight: 600;
          text-align: left;
        }

        .unit {
          font-size: 14px;
          font-weight: 400;
        }
      }

    }
  }

  .tabs {
    background-color: white;
    border-radius: 14px;
    padding: 5px 20px;

    .income_box {
      div {
        display: inline-block;
        border: 1px solid #D6D6D6;
        padding: 5px 10px 5px 10px;
        margin-right: 20px;
        border-radius: 6px;
      }
    }

  }
}

.svg-img {
  height: 64px;
  width: 64px;
  float: right;
  margin-top: 20px;
  margin-right: 10px;
}
</style>
