<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-10-19 08:28:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-19 19:52:06
 * @FilePath: \办公文档\代码\新建文件夹\src\views\monitors\products\device\controller.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<template>
  <div class="home">
    <div v-for="(bmsItem, index) in bms" :key="bmsItem.dc" style="margin-bottom: 20px;">
      <el-descriptions :column="1" border labelClassName="desc-top" contentClassName="cont-top">
        <el-descriptions-item>
          {{ bmsItem.name }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3" border labelClassName="desc-bot" contentClassName="cont-bot">
        <el-descriptions-item :label="$t('monitor.bmsCS')">
          <span>{{ getStatus(index) }}</span>
        </el-descriptions-item>
        <!-- 4000 0：正常，1：告警 -->
        <el-descriptions-item :label="$t('电池告警')">
          <span v-if="bmsItem['bms_4000']">{{ bmsItem['bms_4000'] == '1' ? $t('alarm.title') :
            $t('common.normal') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4001 0：正常，1：故障 -->
        <el-descriptions-item :label="$t('电池故障')">
          <span v-if="bmsItem['bms_4001']">{{ bmsItem['bms_4001'] == '1' ? $t('common.fault') :
            $t('common.normal') }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4022 -->
        <el-descriptions-item :label="'SOC'">
          <span v-if="bmsItem['bms_4022']">{{ bmsItem['bms_4022'] }}%</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4023 -->
        <el-descriptions-item :label="'SOH'">
          <span v-if="bmsItem['bms_4023']">{{ bmsItem['bms_4023'] }}%</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4020 -->
        <el-descriptions-item :label="$t('monitor.bms_4020')">
          <span v-if="bmsItem['bms_4020']">{{ bmsItem['bms_4020'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4036 -->
        <el-descriptions-item :label="$t('monitor.bms_4036')" v-if="!isEmptyFn(bmsItem['bms_4036'])">
          <span v-if="bmsItem['bms_4036']">{{ bmsItem['bms_4036'] }}kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4037 -->
        <el-descriptions-item :label="$t('monitor.bms_4037')" v-if="!isEmptyFn(bmsItem['bms_4037'])">
          <span v-if="bmsItem['bms_4037']">{{ bmsItem['bms_4037'] }}kWh</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4021 -->
        <el-descriptions-item :label="$t('monitor.bms_4021')">
          <span v-if="bmsItem['bms_4021']">{{ bmsItem['bms_4021'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4031 -->
        <el-descriptions-item :label="$t('monitor.bms_4031')">
          <span v-if="bmsItem['bms_4031']">{{ bmsItem['bms_4031'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4030 -->
        <el-descriptions-item :label="$t('monitor.bms_4030')">
          <span v-if="bmsItem['bms_4030']">{{ bmsItem['bms_4030'] }}A</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4025 -->
        <el-descriptions-item :label="$t('monitor.bms_4025')">
          <span v-if="bmsItem['bms_4025']">{{ bmsItem['bms_4025'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4026 -->
        <el-descriptions-item :label="$t('monitor.bms_4026')" v-if="!isEmptyFn(bmsItem['bms_4026'])">
          <span v-if="bmsItem['bms_4026']">{{ bmsItem['bms_4026'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4029 -->
        <el-descriptions-item :label="$t('monitor.bms_4029')" v-if="!isEmptyFn(bmsItem['bms_4029'])">
          <span v-if="bmsItem['bms_4029']">{{ bmsItem['bms_4029'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4024 -->
        <el-descriptions-item :label="$t('monitor.bms_4024')">
          <span v-if="bmsItem['bms_4024']">{{ bmsItem['bms_4024'] }}V</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4028 -->
        <el-descriptions-item :label="$t('monitor.bms_4028')">
          <span v-if="bmsItem['bms_4028']">{{ bmsItem['bms_4028'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4027 -->
        <el-descriptions-item :label="$t('monitor.bms_4027')">
          <span v-if="bmsItem['bms_4027']">{{ bmsItem['bms_4027'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4041 -->
        <el-descriptions-item :label="$t('monitor.bms_4041')" v-if="!isEmptyFn(bmsItem['bms_4041'])">
          <span v-if="bmsItem['bms_4041']">{{ bmsItem['bms_4041'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4054 -->
        <el-descriptions-item :label="$t('monitor.bms_4054')" v-if="!isEmptyFn(bmsItem['bms_4054'])">
          <span v-if="bmsItem['bms_4054']">{{ bmsItem['bms_4054'] }}mV</span>
          <span v-else>--</span>
        </el-descriptions-item>

        <!-- 4053 -->
        <el-descriptions-item :label="$t('monitor.bms_4053')" v-if="!isEmptyFn(bmsItem['bms_4053'])">
          <span v-if="bmsItem['bms_4053']">{{ bmsItem['bms_4053'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4043 -->
        <el-descriptions-item :label="$t('monitor.bms_4043')" v-if="!isEmptyFn(bmsItem['bms_4033'])">
          <span v-if="bmsItem['bms_4043']">{{ bmsItem['bms_4043'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4045 -->
        <el-descriptions-item :label="$t('monitor.bms_4045')" v-if="!isEmptyFn(bmsItem['bms_4045'])">
          <span v-if="bmsItem['bms_4045']">{{ bmsItem['bms_4045'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4047 -->
        <el-descriptions-item :label="$t('monitor.bms_4047')" v-if="!isEmptyFn(bmsItem['bms_4047'])">
          <span v-if="bmsItem['bms_4047']">{{ bmsItem['bms_4047'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4039 -->
        <el-descriptions-item :label="$t('monitor.bms_4039')" v-if="!isEmptyFn(bmsItem['bms_4039'])">
          <span v-if="bmsItem['bms_4039']">{{ bmsItem['bms_4039'] }}kΩ</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4038 -->
        <el-descriptions-item :label="$t('monitor.bms_4038')" v-if="!isEmptyFn(bmsItem['bms_4038']) && isShowV75019()">
          <span v-if="bmsItem['bms_4038']">{{ bmsItem['bms_4038'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 4055 -->
        <el-descriptions-item :label="$t('monitor.bms_4055')" v-if="!isEmptyFn(bmsItem['bms_4055']) && isShowV75019()">
          <span v-if="bmsItem['bms_4055']">{{ bmsItem['bms_4055'] }}%</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 电池空调状态 -->
        <el-descriptions-item :label="$t('整机状态')" v-if="bmsItem['bms_4058']">
          <span v-if="bmsItem['bms_4058']">{{ get4058Fn(bmsItem['bms_4058'], 0) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('内风机状态')" v-if="bmsItem['bms_4058']">
          <span v-if="bmsItem['bms_4058']">{{ get4058Fn(bmsItem['bms_4058'], 1) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('外风机状态')" v-if="bmsItem['bms_4058']">
          <span v-if="bmsItem['bms_4058']">{{ get4058Fn(bmsItem['bms_4058'], 2) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('压缩机状态')" v-if="bmsItem['bms_4058']">
          <span v-if="bmsItem['bms_4058']">{{ get4058Fn(bmsItem['bms_4058'], 3) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('电加热状态')" v-if="bmsItem['bms_4058']">
          <span v-if="bmsItem['bms_4058']">{{ get4058Fn(bmsItem['bms_4058'], 4) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('应急风机状态')" v-if="bmsItem['bms_4058']">
          <span v-if="bmsItem['bms_4058']">{{ get4058Fn(bmsItem['bms_4058'], 5) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('空调开关机状态')" v-if="bmsItem['bms_4058']">
          <span v-if="bmsItem['bms_4058']">{{ get4058Fn(bmsItem['bms_4058'], 6) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 出水温度 -->
        <el-descriptions-item :label="$t('出水温度')" v-if="bmsItem['bms_4059']">
          <span v-if="bmsItem['bms_4059']">{{ bmsItem['bms_4059'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('进水温度')" v-if="bmsItem['bms_4060']">
          <span v-if="bmsItem['bms_4060']">{{ bmsItem['bms_4060'] }}℃</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <!-- 出水压力 -->
        <el-descriptions-item :label="$t('出水压力')" v-if="bmsItem['bms_4061']">
          <span v-if="bmsItem['bms_4061']">{{ bmsItem['bms_4061'] }}Bar</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('进水压力')" v-if="bmsItem['bms_4062']">
          <span v-if="bmsItem['bms_4062']">{{ bmsItem['bms_4062'] }}Bar</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('空调状态')" v-if="bmsItem['bms_4063']">
          <span v-if="bmsItem['bms_4063']">{{ get4063(bmsItem['bms_4063']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('空调模式')" v-if="bmsItem['bms_4064']">
          <span v-if="bmsItem['bms_4064']">{{ get4064(bmsItem['bms_4064']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('BMS类型')" v-if="bmsItem['bms_4003'] && checkRoleFn(['admin'])">
          <span v-if="bmsItem['bms_4003']">{{ getBmsTypeFn(bmsItem['bms_4003']) }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('monitor.acVersion')" v-if="bmsItem['bms_4082']">
          <span v-if="bmsItem['bms_4082']">{{ bmsItem['bms_4003'] }}</span>
          <span v-else>--</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
import { get4058 } from '@/utils/parseBinaryToText'
import { isEmpty } from 'lodash'
import { checkRole } from '@/utils/permission'
import { bmsTypeOptions, bmsAirModeOptions, bmsAirStatusOptions } from '@/constant'

export default {
  name: "device",
  props: {
    type: {
      type: String,
      default: 'bms'
    }
  },
  computed: {
    bms() {
      let data = null
      if (this.type == 'bms') {
        data = this.$store.state.monitor.pcs_bms
      } else {
        data = this.$store.state.monitor.pcs_bmsBau
      }
      data.forEach(item => {
        item.name = this.type == 'bms' ? `${parseInt(item.dc) - 161000 + 1}#BMS` : `${parseInt(item.dc) - 241000 + 1}#BMS-BAU`
      })
      return data
    },
    getStatus() {
      return (index) => {
        if (this.bms[index].isAnalysis == 0) {
          return this.bms.onLineState == '在线' ? this.$t('common.online') : this.$t('common.offline')
        } else if (this.bms[index].isAnalysis == 1) {
          return this.bms[index]['bms_4002'] == '1' ? this.$t('common.fault') : this.$t('common.normal')
        } else {
          return '--'
        }
      }
    },
    get4063() {
      return (num) => {
        let value = bmsAirStatusOptions.find(item => item.value == num)
        if (value) return value.label
        return '--'
      }
    },
    get4064() {
      return (num) => {
        let value = bmsAirModeOptions.find(item => item.value == num)
        if (value) return value.label
        return '--'
      }
    },
    control() {
      return this.$store.state.monitor.control
    },
    getBmsTypeFn() {
      return (type) => {
        let value = bmsTypeOptions.find(item => item.value == type)
        if (value) return value.label
        return '--'
      }
    }
  },
  methods: {
    get4058Fn(num, bit) {
      return get4058(num, bit)
    },
    isEmptyFn(data) {
      return isEmpty(data)
    },
    isShowV75019() {
      let versionStart = this.control?.jk_1000?.split('V')[1].split('.')[0]
      let versionTwo = this.control?.jk_1000?.split('V')[1].split('.')[1]
      let versionThere = this.control?.jk_1000?.split('V')[1].split('.')[2]
      if (versionStart == 7) if (versionTwo == 5019) return false
      return true
    },
    checkRoleFn(value) {
      return checkRole(value)
    }
  }
};
</script>
<style lang="scss" scoped>
.home {
  padding-right: 15px;
  text-align: center;
  height: 100%;
}
</style>
