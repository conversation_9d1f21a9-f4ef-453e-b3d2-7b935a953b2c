<template>
  <div class="box">
    <div class="tree elevation-4">
      <div class="header-title">{{ $t('home.pieRadio2') }}</div>
      <div>
        <el-input v-model="filterText" :placeholder="$t(`device['Please enter device serial number']`)" clearable
          size="small" prefix-icon="el-icon-search" style="margin: 20px 0;" />
      </div>
      <el-tree ref="treeRef" :data="data" node-key="treeId" :props="defaultProps" default-expand-all
        :expand-on-click-node="false" :check-strictly="true" @node-click="handleNodeClick"
        :filter-node-method="filterNode" />
    </div>
    <div class="cont elevation-4">
      <div class="input_box">
        <div class="header-title">
          {{ $route.meta.title }}
          <el-tooltip class="item" effect="dark" :content="$t('tagsView.refresh')">
            <i class="el-icon-refresh" style="font-size: 18px;margin-left: 3px;cursor: pointer;" @click="getList"></i>
          </el-tooltip>
        </div>
        <div>
          <div class="input_ment">
            <el-radio-group v-model="dateType" size="medium" class="input_ment" @input="changeDateType">
              <el-radio-button :label="$t('date.day')"></el-radio-button>
              <el-radio-button :label="$t('date.month')"></el-radio-button>
            </el-radio-group>
          </div>
          <div class="input_ment">
            <el-date-picker v-model="date" :type="datePickerType" valueFormat='yyyy-MM-dd' @change="handleDateChange"
              :picker-options="pickerOptions">
            </el-date-picker>
          </div>
          <div class="input_ment">
            <el-button type="primary" @click="handleExportBtnClick">{{ $t('common.exportReport') }}</el-button>
          </div>
        </div>
      </div>
      <div class="table_box" v-loading="loading">
        <line-chart :xData="lineTimes" :yData="lineValues" v-if="queryInfo.dateType == 0" />
        <bar-echart :xData="lineTimes" :yData="lineValues" v-if="queryInfo.dateType == 1" />
        <!-- <el-empty style="height: 100%;" v-else></el-empty> -->
      </div>
    </div>

    <el-dialog :visible.sync="dialogVisible" :title="$t('export.exportDetail')" center :modal-append-to-body="false"
      width="600px"">
      <el-form :model="form" label-width="auto">
      <el-row :gutter="20">
        <el-col>
          <el-form-item :label="$t('电量类型')">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">{{ $t('全选')
              }}</el-checkbox>
            <el-checkbox-group v-model="form.electricTypeList" @change="handleCheckedChange">
              <el-checkbox v-for="item in electricTypeOptions" :label="item.label" :name="item.label"
                :key="item.value" />
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t('日期类型')" prop="dateType">
            <el-radio-group v-model="form.dateType1" size="medium" class="input_ment" @input="changeDateTypeExport">
              <el-radio-button :label="$t('date.day')"></el-radio-button>
              <el-radio-button :label="$t('date.month')"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t('选择日期')" prop="date" :rules="[{required: true, message: $t('选择日期')}]">
            <el-date-picker v-model="form.date" :type="form.datePickerType" valueFormat='yyyy-MM-dd'
              @change="handleDateChangeExport" :picker-options="pickerOptions" style="width: 182px">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t('是否计算差值')" prop="calculate" v-if="form.type == 0">
            <el-radio-group v-model="form.calculate" style="width: 100%" @input="handleCalculateChange">
              <el-radio :label="1">{{ $t('menu.yes') }}</el-radio>
              <el-radio :label="null">{{ $t('menu.no') }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item :label="$t('时间段')" v-if="form.type == 0 && form.calculate == 1">
            <el-row :gutter="20">
              <el-col :span="10" style="padding-left: 0;">
                <el-form-item prop="timeRange1" :rules="[{required: true, message: $t('开始时间')}]">
                  <el-time-select v-model="form.timeRange1" :placeholder="$t('开始时间')"
                    :picker-options="{ step: '00:05', start: '00:00', end: '23:59' }" value-format="HH:mm:ss"
                    @change="handleTimeRangeChangeExport" style="width: 100%">
                  </el-time-select>
                </el-form-item>
              </el-col>
              <el-col :span="2">-</el-col>
              <el-col :span="10" style="padding-left: 0;">
                <el-form-item prop="timeRange2" :rules="[{required: true, message: $t('结束时间')}]">
                  <el-time-select v-model="form.timeRange2" :placeholder="$t('结束时间')"
                    :picker-options="{ step: '00:05', start: '00:00', end: '23:59', minTime: form.timeRange1 }"
                    value-format="HH:mm:ss" @change="handleTimeRangeChangeExport" style="width: 100%">
                  </el-time-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
      </el-form>
      <div style="display: flex;">
        <div>
          {{ $t('注：') }}
        </div>
        <div style="flex: 1">
          <div>{{ $t('1、如若不想导出某一类型的电量，请不要勾选该类型电量') }}</div>
          <div style="margin-top: 10px;">{{ $t('2、是否计算差值，为某一时间段的汇总值。') }}</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm">{{ $t('common.export') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { electricList, electricExport, electricDetail, electricDetailExport } from '@/api/operation/electric'
import { handleExport } from '@/utils/export'
import _ from 'lodash'
import { getProjectTree } from '@/api/test/dataanalysis'
import { deviceTypeSingleOptions, deviceTypeGroupOptions } from '@/hook/useDeviceType'

import LineChart from '../../dashboard/LineChart'
import barEchart from './barEchart.vue'

export default {
  components: {
    LineChart,
    barEchart
  },
  data() {
    return {
      tableData: [],
      loading: false,
      queryInfo: {
      },
      date: undefined, // 获取当前日期
      dateType: this.$t('date.day'),
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [{
          text: this.$t('今天'),
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: this.$t('昨天'),
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: this.$t('一周前'),
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      filterText: '',
      lineTimes: [],
      lineValues: [],
      dialogVisible: false,
      form: {
        electricTypeList: [],
        type: 0,
        dateType1: this.$t('date.day'),
        datePickerType: 'date',
        date: '',
        timeRange1: '00:00',
        timeRange2: '23:55',
        calculate: null
      },
      electricTypeOptions: [],
      dateType: this.$t('date.day'),
      type: 0,
      datePickerType: 'date',
      // 全选
      checkAll: false,
      isIndeterminate: true
    };
  },
  mounted() {
    this.queryInfo.startDate = `${this.$moment(new Date()).format('YYYY-MM-DD')} 00:00:00`
    this.queryInfo.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    this.queryInfo.dateType = this.type
    this.date = this.$moment(new Date()).format('YYYY-MM-DD')
    this.getProjectTreeFn()
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val);
    }
  },
  computed: {
    typeOptions() {
      return [...deviceTypeSingleOptions, ...deviceTypeGroupOptions]
    }
  },
  methods: {
    // 获取列表
    getList() {
      this.loading = true
      this.lineValues = []
      this.electricTypeOptions = []
      electricDetail(this.queryInfo).then(res => {
        let data = res.data
        if (this.queryInfo.dateType == 0) {
          this.lineTimes = data.map(item => this.$moment(item.mqttTime).format('HH:mm'))
        } else {
          this.lineTimes = data.map(item => this.$moment(item.mqttTime).format('MM-DD'))
        }
        if (data.length > 0) {
          let electricDcNames = new Set()
          let electricDcs = new Set()
          data.forEach(item => {
            if (item.electricMeterList && item.electricMeterList.length) item.electricMeterList.forEach(item1 => {
              electricDcNames.add(item1.dcName)
              electricDcs.add(item1.dc)
            })
          })
          // const keys = Object.keys(data[0])
          // if (keys.findIndex(item => item == 'chargeCapacitySum') !== -1) this.lineValues.push({
          // 储能\pcs
          if (this.queryInfo.deviceType != 13) {
            this.lineValues.push({
              name: this.$t('monitor.topItem4'),
              values: data.map(item => item.chargeCapacitySum != null ? _.round(item.chargeCapacitySum, 2) : item.chargeCapacitySum),
              ac: this.queryInfo.ac,
              unit: 'kWh',
              module: '',
              moduleName: null
            }, {
              name: this.$t('monitor.topItem5'),
              values: data.map(item => item.dischargeCapacitySum != null ? _.round(item.dischargeCapacitySum, 2) : item.dischargeCapacitySum),
              ac: this.queryInfo.ac,
              unit: 'kWh',
              module: '',
              moduleName: null
            })
            this.electricTypeOptions.push({
              label: this.$t('储能电量'),
              value: 0
            })
          }
          // 电网
          if (data[0].matchSTS) {
            this.lineValues.push({
              name: this.$t('电网侧总充电量'),
              values: data.map(item => item.jk1085Calculate != null ? _.round(item.jk1085Calculate, 2) : item.jk1085Calculate),
              ac: this.queryInfo.ac,
              unit: 'kWh',
              module: '',
              moduleName: null
            }, {
              name: this.$t('电网侧总放电量'),
              values: data.map(item => item.jk1086Calculate != null ? _.round(item.jk1086Calculate, 2) : item.jk1086Calculate),
              ac: this.queryInfo.ac,
              unit: 'kWh',
              module: '',
              moduleName: null
            })
            this.electricTypeOptions.push({
              label: this.$t('电网电量'),
              value: 2
            })
          }
          // 光伏
          if (data[0].matchPhotovoltaic) {
            this.lineValues.push({
              name: this.$t('monitor.topItem7'),
              values: data.map(item => item.photovoltaicPowerCapacitySum != null ? _.round(item.photovoltaicPowerCapacitySum, 2) : item.photovoltaicPowerCapacitySum),
              ac: this.queryInfo.ac,
              unit: 'kWh',
              module: '',
              moduleName: null
            })
            this.electricTypeOptions.push({
              label: this.$t('光伏电量'),
              value: 1
            })
          }
          // 电表
          if (Array.from(electricDcNames).length) {
            Array.from(electricDcNames).forEach((item, index) => {
              this.lineValues.push({
                name: `${item}_${this.$t('正向总电量')}`,
                values: data.map(item1 => {
                  if (item1.electricMeterList && item1.electricMeterList.length) {
                    if (item1.electricMeterList.findIndex(item2 => item2.dcName == item) !== -1) {
                      return _.round(item1.electricMeterList.find(item2 => item2.dcName == item).forwardPower, 2)
                    } else {
                      return null
                    }
                  } return null
                }),
                ac: this.queryInfo.ac,
                unit: 'kWh',
                module: '',
                moduleName: null
              }, {
                name: `${item}_${this.$t('反向总电量')}`,
                values: data.map(item1 => {
                  if (item1.electricMeterList && item1.electricMeterList.length) {
                    if (item1.electricMeterList.findIndex(item2 => item2.dcName == item) !== -1) {
                      return _.round(item1.electricMeterList.find(item2 => item2.dcName == item).reversePower, 2)
                    } else {
                      return null
                    }
                  } else {
                    null
                  }
                }),
                ac: this.queryInfo.ac,
                unit: 'kWh',
                module: '',
                moduleName: null
              })
              this.electricTypeOptions.push({
                label: item,
                value: Array.from(electricDcs)[index],
                text: 'Meter'
              })
            })
          }
          // 直流源
          if (this.queryInfo.deviceType == 13) {
            this.lineValues.push({
              name: this.$t('正向向总电量'),
              values: data.map(item => item.jk1083Calculate != null ? _.round(item.jk1083Calculate, 2) : item.jk1083Calculate),
              ac: this.queryInfo.ac,
              unit: 'kWh',
              module: '',
              moduleName: null
            })
            this.lineValues.push({
              name: this.$t('反向总电量'),
              values: data.map(item => item.jk1084Calculate != null ? _.round(item.jk1084Calculate, 2) : item.jk1084Calculate),
              ac: this.queryInfo.ac,
              unit: 'kWh',
              module: '',
              moduleName: null
            })
            this.electricTypeOptions.push({
              label: this.$t('直流源'),
              value: 4
            })
          }
        }
        this.form.electricTypeList = [this.electricTypeOptions[0].label]
        this.isIndeterminate = true
        this.loading = false
      });
    },
    handleDateChange(date) {
      if (!date) return
      if (this.type == 0) {
        let nowDate = this.$moment(new Date()).format('YYYY-MM-DD')
        if (this.$moment(date).isBefore(nowDate)) {
          this.queryInfo.startDate = `${date} 00:00:00`
          this.queryInfo.endDate = `${date} 23:59:59`
        } else if (date == nowDate) {
          this.queryInfo.startDate = `${date} 00:00:00`
          this.queryInfo.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        }
      } else {
        let nowDate = this.$moment(new Date()).format('YYYY-MM')
        if (this.$moment(date).isBefore(nowDate)) {
          this.queryInfo.startDate = `${this.$moment(date).startOf('month').format("YYYY-MM-DD")} 00:00:00`
          this.queryInfo.endDate = `${this.$moment(date).endOf('month').format("YYYY-MM-DD")} 23:59:59`
        } else if (this.$moment(date).format('YYYY-MM') == nowDate) {
          this.queryInfo.startDate = `${date} 00:00:00`
          this.queryInfo.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        }
      }
      this.getList()
    },
    changeDateType(e) {
      if (e == this.$t('date.day')) {
        this.queryInfo.startDate = `${this.$moment(new Date()).format('YYYY-MM-DD')} 00:00:00`
        this.queryInfo.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.date = this.$moment(new Date()).format('YYYY-MM-DD')
        this.type = 0
        this.queryInfo.dateType = 0
        this.datePickerType = 'date'
      } else {
        this.queryInfo.startDate = `${this.$moment(new Date()).startOf('month').format("YYYY-MM-DD")} 00:00:00`
        this.queryInfo.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.date = this.$moment(new Date()).format('YYYY-MM')
        this.type = 1
        this.queryInfo.dateType = 1
        this.datePickerType = 'month'
      }
      this.getList()
    },
    handleExportBtnClick() {
      if (!this.electricTypeOptions.length) return this.$message({
        type: 'error',
        message: this.$t('请稍等,数据还在请求中~')
      })
      this.form.startDate = `${this.$moment(new Date()).format('YYYY-MM-DD')} 00:00:00`
      this.form.endDate = `${this.$moment(new Date()).format('YYYY-MM-DD')} 23:59:59`
      this.form.dateType = this.form.type
      this.form.date = this.$moment(new Date()).format('YYYY-MM-DD')
      this.form.electricTypeList = [this.electricTypeOptions[0].label]
      this.form.timeRange1 = '00:00'
      this.form.timeRange2 = '23:55'
      this.form.calculate = null
      this.isIndeterminate = true
      this.dialogVisible = true
    },
    handleExportClick() {
      let electricTypeList = this.electricTypeOptions.filter(item => this.form.electricTypeList.findIndex(i => i == item.label && item.text !== 'Meter') != -1).map(item => item.value)
      let electricMeterList = this.electricTypeOptions.filter(item => this.form.electricTypeList.findIndex(i => i == item.label && item.text == 'Meter') != -1).map(item => {
        return {
          dc: item.value,
          dcName: item.label
        }
      })
      this.$modal.loading(`${this.$t(`oss['正在下载文件，请稍候']`)}...`);
      electricDetailExport({
        ac: this.queryInfo.ac,
        startDate: this.form.startDate,
        endDate: this.form.endDate,
        timeZone: this.queryInfo.timeZone,
        electricTypeList,
        dateType: this.form.dateType,
        electricMeterList,
        calculate: this.form.calculate
      }).then(res => {
        if (res.code) if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let fileName = ''
        fileName = `${this.queryInfo.startDate}_${this.$t(`log['电量统计报表']`)}`
        handleExport(res, fileName)
        this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    // 获取树形
    getProjectTreeFn() {
      this.data = []
      getProjectTree({
        deviceType: 0
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let items = res.data
        let deviceType = items[0].children[0].deviceType
        if (deviceType == 10000 || deviceType == 10001 || deviceType == 10002) {
          this.queryInfo.ac = items[0].children[0].children[0].ac
          this.queryInfo.timeZone = items[0].children[0].children[0].timeZone
          this.queryInfo.deviceType = items[0].children[0].children[0].deviceType
        } else {
          this.queryInfo.ac = items[0].children[0].ac
          this.queryInfo.timeZone = items[0].children[0].timeZone
          this.queryInfo.deviceType = items[0].children[0].deviceType
        }
        this.data = items
        this.$nextTick(() => {
          this.$refs.treeRef.setCurrentKey(items[0].children[0].treeId)
        })
        this.getList()
      })
    },
    handleNodeClick(data, node) {
      if (!data.ac) return this.$message({
        type: 'warning',
        message: this.$t('请选择有效的设备')
      })
      if (data.deviceType == 10000 || data.deviceType == 10001 || data.deviceType == 10002) return this.$message({
        type: 'warning',
        message: this.$t('请选择有效的设备')
      })
      this.queryInfo.ac = data.ac
      this.queryInfo.timeZone = data.timeZone
      this.queryInfo.deviceType = data.deviceType
      this.getList()
    },
    filterNode(value, data) {
      if (!value) return true;
      if (!data.ac) return false;
      return data.ac.indexOf(value) !== -1;
    },
    // 导出
    handleConfirm() {
      this.handleExportClick()
    },
    // 全选
    handleCheckAllChange(val) {
      this.form.electricTypeList = val ? this.electricTypeOptions.map(item => item.label) : [];
      this.isIndeterminate = false;
    },
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.electricTypeOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.electricTypeOptions.length;
    },
    // 导出日期
    handleDateChangeExport(date) {
      if (!date) return
      if (this.form.type == 0) {
        let nowDate = this.$moment(new Date()).format('YYYY-MM-DD')
        if (this.$moment(date).isBefore(nowDate)) {
          this.form.startDate = `${date} 00:00:00`
          this.form.endDate = `${date} 23:59:59`
        } else if (date == nowDate) {
          this.form.startDate = `${date} 00:00:00`
          this.form.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        }
        this.form.timeRange1 = '00:00'
        this.form.timeRange2 = '23:55'
      } else {
        let nowDate = this.$moment(new Date()).format('YYYY-MM')
        if (this.$moment(date).isBefore(nowDate)) {
          this.form.startDate = `${this.$moment(date).startOf('month').format("YYYY-MM-DD")} 00:00:00`
          this.form.endDate = `${this.$moment(date).endOf('month').format("YYYY-MM-DD")} 23:59:59`
        } else if (this.$moment(date).format('YYYY-MM') == nowDate) {
          this.form.startDate = `${date} 00:00:00`
          this.form.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        }
      }
    },
    changeDateTypeExport(e) {
      if (e == this.$t('date.day')) {
        this.form.startDate = `${this.$moment(new Date()).format('YYYY-MM-DD')} 00:00:00`
        this.form.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.form.date = this.$moment(new Date()).format('YYYY-MM-DD')
        this.form.type = 0
        this.form.dateType1 = this.$t('date.day')
        this.form.dateType = 0
        this.form.datePickerType = 'date'
        this.form.timeRange1 = '00:00'
        this.form.timeRange2 = '23:55'
      } else {
        this.form.startDate = `${this.$moment(new Date()).startOf('month').format("YYYY-MM-DD")} 00:00:00`
        this.form.endDate = this.$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.form.date = this.$moment(new Date()).format('YYYY-MM')
        this.form.type = 1
        this.form.dateType1 = this.$t('date.month')
        this.form.dateType = 1
        this.form.datePickerType = 'month'
        this.form.calculate = null
      }
    },
    handleTimeRangeChangeExport(e) {
      if (!this.form.date) return this.$message({
        type: 'warning',
        message: this.$t('请先选择日期')
      })
      if (!this.form.timeRange1 || !this.form.timeRange2) return
      this.form.startDate = `${this.form.startDate.split(' ')[0]} ${this.form.timeRange1}:00`
      this.form.endDate = `${this.form.endDate.split(' ')[0]} ${this.form.timeRange2}:00`
    },
    handleCalculateChange(e) {
      if (e == 1) {
        this.form.timeRange1 = '00:00'
        this.form.timeRange2 = '23:55'
        this.form.startDate = `${this.form.startDate.split(' ')[0]} ${this.form.timeRange1}:00`
        this.form.endDate = `${this.form.endDate.split(' ')[0]} ${this.form.timeRange2}:00`
      } else {
        this.form.startDate = `${this.form.startDate.split(' ')[0]} 00:00:00`
        this.form.endDate = `${this.form.endDate.split(' ')[0]} 23:59:59`
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;

  .tree {
    padding: 20px;
    width: 320px;
    background-color: #fff;
    margin-right: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

    :deep(.el-tree) {
      height: 90%;
      overflow: auto;

      .el-tree-node__content {
        height: 40px;
        font-size: 16px;
        /* border-radius: 8px; */
        /* margin-bottom: 10px; */
      }

      .is-current>.el-tree-node__content {
        background-color: #f6f6f6;
        font-weight: 600;
        color: var(--base-color);
      }
      .el-tree-node__content:hover, .el-upload-list__item:hover {
        background-color: #f6f6f6;
      }
    }
  }

  .cont {
    background: #fff;
    border-radius: 8px;
    flex: 1;
    overflow: auto;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  }

  .table_box {
    height: 92%;
  }
}

::v-deep .alarm-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-table__footer-wrapper tbody td.el-table__cell {
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}
</style>
