<template>
  <div class="navbar" v-if='!state'>
    <div class="navbar-left">
      <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
        @toggleClick="toggleSideBar" />

      <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav" />
      <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />
    </div>

    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <lang-select class="right-menu-item hover-effect"></lang-select>

        <div class="right-menu-item hover-effect" @click="handleNoticeClick">
          <el-badge :value="unreadCount" :hidden="unreadCount <= 0">
            <svg-icon icon-class="bell-outline" style="width: 20px;height: 20px;"></svg-icon>
          </el-badge>
        </div>

        <el-tooltip :content="$t('settings.screen')" effect="dark" placement="bottom"
          class="right-menu-item hover-effect">
          <screenfull id="screenfull" />
        </el-tooltip>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <el-avatar :size="'medium'" :src="avatar"></el-avatar>
          <div class="name">{{ name }}</div>
          <svg-icon icon-class="chevron-down" style="width: 24px;height: 24px;"></svg-icon>
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>{{ $t('PersonalCenter') }}</el-dropdown-item>
          </router-link>
          <!-- <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item> -->
          <el-dropdown-item @click.native="logout">
            <span>{{ $t('SignOut') }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import LangSelect from '@/components/LangSelect'
import manualSelect from '@/components/manualSelect'

import { getUnreadNoticeCount } from '@/api/system/notice'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc,
    LangSelect,
    manualSelect
  },
  props: {
    state: {
      type: Array
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      'name'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    },
    unreadCount() {
      return this.$store.state.notice.unreadCount
    }
  },
  data() {
    return {
      isShow: false,
      time: null
    }
  },
  mounted() {
    this.getListNotice()
    this.time = setInterval(() => {
      this.getListNotice()
    }, 300000)
  },
  onMounted() {
    clearInterval(this.time)
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm(this.$t('login.logOut'), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      }).catch(() => { });
    },
    getListNotice() {
      this.$store.dispatch('notice/getUnreadNoticeCountFn')
      console.log(this.unreadCount)
    },
    handleNoticeClick() {
      this.$router.push('/notice/center')
    }
  },

}
</script>

<style lang="scss" scoped>
.navbar {
  height: 64px;
  width: 100%;
  position: relative;
  background: #fff;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, .2), 0 4px 5px 0 rgba(0, 0, 0, .14), 0 1px 10px 0 rgba(0, 0, 0, .12);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;

  &-left {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .hamburger-container {
    line-height: 64px;
    height: 100%;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: #f5f5f5;
    }
  }

  .right-menu {
    display: flex;
    align-items: center;
    height: 100%;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      height: 100%;
      font-size: 16px;
      color: #5a5e66;
      display: flex;
      align-items: center;
      padding: 0 10px;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: #f5f5f5;
        }
      }
    }

    .avatar-container {
      margin-right: 20px;

      .avatar-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        height: 100%;
        font-size: 16px;

        .name {
          margin: 0 10px;
          margin-right: 4px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 15px;
          font-size: 16px;
        }
      }
    }
  }
}

.list-item {
  padding: 10px;
  border-bottom: 1px solid #f5f5f5;
}
</style>
