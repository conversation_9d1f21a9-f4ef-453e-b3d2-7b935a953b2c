/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-05-27 11:54:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-06-12 10:18:23
 * @FilePath: \elecloud_platform-main\src\api\operation\alias.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 获取列表
export function aliasList(queryInfo) {
  return request({
    url: '/system/point/list',
    method: 'get',
    params: queryInfo
  })
}

// 新增
export function addAlias(data) {
  return request({
    url: '/system/point/add',
    method: 'post',
    data
  })
}

//  修改
export function editAlias(data) {
  return request({
    url: '/system/point/update',
    method: 'post',
    data
  })
}

// 删除
export function deleteAlias(queryInfo) {
  return request({
    url: `/system/point/${queryInfo.id}`,
    method: 'delete'
  })
}
