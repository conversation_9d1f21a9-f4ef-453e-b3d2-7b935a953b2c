<template>
  <div class="page-box elevation-4">
    <div class="input_box">
      <div class="header-title">
        {{ $route.meta.title }}
      </div>
      <div>
        <div class="input_ment">
          <el-input :placeholder="$t(`common['Please enter']`)" v-model="searchValue" clearable>
            <el-select v-model="searchKey" slot="prepend" :placeholder="$t('common.select')" style="width: 150px">
              <el-option :label="$t(`common['时区']`)" value="timeZone"></el-option>
            </el-select>
          </el-input>
        </div>
        <div class="input_ment">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
            }}</el-button>
        </div>
        <div class="input_ment">
          <el-button type="primary" @click="handleAddClick()" icon="el-icon-plus">{{
            $t('common.add') }}</el-button>
        </div>
      </div>
    </div>
    <div class="table_box">
      <!-- table -->
      <el-table :data="tableData" v-loading="loading" style="width: 100%;">
        <el-table-column type="index" label="#" width="60" align="center" />
        <el-table-column prop="timeZoneAddress" :label="$t(`common['时区地址']`)" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row[getPropFn] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="timeZone" :label="$t(`common['时区']`)" show-overflow-tooltip align="center" />
        <el-table-column fixed="right" :label="$t('common.handle')" width="150" align="center">
          <template slot-scope="scope">
            <el-button @click="handleEditClick(scope.row)" type="text" size="small">{{ $t('common.edit') }}</el-button>
            <el-button @click="handleDeleteClick(scope.row)" type="text" size="small">{{ $t('common.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
        @pagination="getList" style="margin-top: 20px;text-align: right;" />
    </div>


    <el-dialog :visible.sync="dialogVisible" center :modal-append-to-body="false" width="600px" :title="dialogTitle">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="auto">
        <el-form-item :label="$t(`common['时区']`)" prop="timeZone">
          <el-input v-model="ruleForm.timeZone" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t(`common['时区地址']`)" prop="timeZoneAddress">
          <el-input v-model="ruleForm.timeZoneAddress" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t(`common['时区地址']`) + '(EN)'" prop="timeZoneAddressUs">
          <el-input v-model="ruleForm.timeZoneAddressUs" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
        <el-form-item :label="$t(`common['时区地址']`) + '(IT)'" prop="timeZoneAddressIt">
          <el-input v-model="ruleForm.timeZoneAddressIt" :placeholder="$t(`common['Please enter']`)" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancelClick()">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm('ruleFormRef')">{{ $t('common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { timeList, addTime, editTime, deleteTime } from '@/api/operation/time'

export default {
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单
      ruleForm: {
        timeZoneAddress: '',
        timeZone: '',
      },
      rules: {
        timeZoneAddress: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        timeZoneAddressUs: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        timeZoneAddressIt: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' }
        ],
        timeZone: [
          { required: true, message: this.$t(`common['Please enter']`), trigger: 'blur' },
          {
            pattern: /^[+-](0[0-9]|1[0-4]):(00|15|30|45)$/,
            message: this.$t('格式不正确'),
            trigger: ['blur', 'change'],
          }
        ],
      },
      dialogTitle: '添加时区',
      // 搜索
      searchKey: 'timeZone',
      searchValue: '',
    };
  },
  computed: {
    getPropFn() {
      let lang = this.$store.getters.language
      switch (lang) {
        case 'zh':
          return 'timeZoneAddress'
        case 'en':
          return 'timeZoneAddressUs'
        case 'it':
          return 'timeZoneAddressIt'
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      timeList({...this.queryInfo, [this.searchKey]: this.searchValue}).then(res => {
        let data = res.rows
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleCancelClick() {
      this.dialogVisible = false;
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == '添加时区') {
            this.addTimeFn()
          } else {
            this.editTimeFn()
          }
        }
      });
    },
    // 添加
    handleAddClick() {
      this.dialogVisible = true;
      this.dialogTitle = '添加时区'
      this.ruleForm = {
        timeZoneAddress: '',
        timeZone: '',
        timeZoneAddressUs: '',
        timeZoneAddressIt: '',
      }
      this.$nextTick(() => {
        this.resetForm('ruleForm')
      })
    },
    addTimeFn() {
      addTime({
        timeZoneAddress: this.ruleForm.timeZoneAddress,
        timeZoneAddressUs: this.ruleForm.timeZoneAddressUs,
        timeZoneAddressIt: this.ruleForm.timeZoneAddressIt,
        timeZone: this.ruleForm.timeZone,
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Addition Failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Added successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    handleEditClick(row) {
      this.dialogTitle = '修改时区'
      this.dialogVisible = true;
      this.ruleForm = {
        ...row
      }
    },
    editTimeFn() {
      editTime({
        timeZoneAddress: this.ruleForm.timeZoneAddress,
        timeZoneAddressUs: this.ruleForm.timeZoneAddressUs,
        timeZoneAddressIt: this.ruleForm.timeZoneAddressIt,
        timeZone: this.ruleForm.timeZone,
        id: this.ruleForm.id
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: this.$t(`common['Change failed']`)
        });
        this.$message({
          type: 'success',
          message: this.$t(`common['Modify successfully']`)
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    // 删除
    handleDeleteClick(row) {
      this.$confirm(this.$t(`menu['Are you sure to delete the data item?']`), this.$t('common.systemPrompt'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        deleteTime({
          id: row.id
        }).then(res => {
          if (res.code !== 200) return this.$message({
            type: 'error',
            message: this.$t(`common['Deleted Failed']`)
          });
          this.getList()
          this.$message({
            type: 'success',
            message: this.$t(`common['Deleted successfully']`)
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t(`common['Deletion Cancelled']`)
        });
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 30px;
  width: 100%;
  background-color: white;
  padding-bottom: 10px;
  border-radius: 12px;
}
</style>
