/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-02 11:07:42
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-02 17:35:46
 * @FilePath: \elecloud_platform-main\src\constant\deviceType.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import i18n from '@/lang'

/**
 *  RT01 光储系统(并离网)协议版本:风冷电池VB03液冷电池VB02
    RT02 光储变流器(并离网)
    RT03 电池系统
    RT04 光伏控制系统
    RT05 光储变流器(纯并网)
    RT06 光储系统(纯并网)风冷电池 VB03 液冷电池VB02
    RT07 储能系统(纯并网)风冷电池 VB03 液冷电池VB02
    RTO8 储能变流器(纯并网)
    RT09 储能系统(并离网)风冷电池 VB03 液冷电池VB02
    RT10 储能变流器(并离网)
    RT11 光储充系统(纯并网)风冷电池VB03液冷电池VB02
    RT12 光储充系统(并离网)1风冷电池 VB03 液冷电池VB02
 */

export const deviceTypeSingleOptions = [
  {
    value: 1,
    label: i18n.t('deviceType.type1')
  }, {
    value: 2,
    label: i18n.t('deviceType.type2')
  }, {
    value: 3,
    label: i18n.t('deviceType.type3')
  },
  {
    value: 4,
    label: i18n.t('deviceType.type4')
  },
  {
    value: 5,
    label: i18n.t('deviceType.type5')
  },
  {
    value: 6,
    label: i18n.t('deviceType.type6')
  },
  {
    value: 7,
    label: i18n.t('deviceType.type7')
  },
  {
    value: 8,
    label: i18n.t('deviceType.type8')
  },
  {
    value: 9,
    label: i18n.t('deviceType.type9')
  },
  {
    value: 10,
    label: i18n.t('deviceType.type10')
  },
  {
    value: 11,
    label: i18n.t('光储充系统(纯并网)')
  },
  {
    value: 12,
    label: i18n.t('光储充系统(并离网)')
  },
  {
    value: 13,
    label: i18n.t('MDC直流源')
  },
]

/**
 *  10000 组合光储系统
    10001 组合光伏控制系统
    10002 组合储能系统
 */
export const deviceTypeGroupOptions = [
  {
    value: 10000,
    label: i18n.t(`monitor['组合光储系统']`)
  },
  {
    value: 10001,
    label: i18n.t(`monitor['组合光伏控制系统']`)
  },
  {
    value: 10002,
    label: i18n.t(`monitor['组合储能系统']`)
  }
]

/**
 * ems
 */
export const deviceTypeEmsOptions = [
  {
    value: 99999,
    label: 'EMS'
  }
]

/**
 * 是否为组合设备类型
 */
export const isGroupFn = (type) => deviceTypeGroupOptions.some(item => item.value == type)

/**
 * 是否为ems设备类型
 */
export const isEmsFn = (type) => deviceTypeEmsOptions.some(item => item.value == type)

/**
 * 电网功率，有sts时1031~1033（并离网），无1051（纯并网）
 */
export const isPowerFn = (type) => type == 1 || type == 2 || type == 9 || type == 10 || type == 12

/**
 * 有光伏的
 */
export const isPhotovoltaicFn = (type) => type == 1 || type == 2 || type == 4 || type == 5 || type == 6 || type == 11 || type == 12

/**
 * 有储能的
 */
export const isEnergyFn = (type) => type == 1 || type == 2 || type == 3 || type == 5 || type == 6 || type == 7 || type == 8 || type == 9 || type == 10 || type == 11 || type == 12

/**
 * 有电网的
 */
export const isGirdFn = (type) => type == 1 || type == 2 || type == 5 || type == 6 || type == 7 || type == 8 || type == 9 || type == 10 || type == 11 || type == 12

/**
 * 有电池的
 */
export const isCellFn = (type) => type == 1 || type == 3 || type == 6 || type == 7 || type == 9 || type == 11 || type == 12

/**
 * 直流母线功率，储能变流器，用1056，不是用1077
 */
export const isBusFn = (type) => type == 8 || type == 10
export const isBusFn2 = (type) => type == 2 || type == 3 || type == 4 || type == 5 || type == 8 || type == 10

/**
 * 获取设备类型
 */
export const getDeviceType = (type, needGroup, needEms = true) => {
  let options = []
  options = needGroup ? [...deviceTypeSingleOptions, ...deviceTypeGroupOptions] : deviceTypeSingleOptions
  options = needEms ? [...options, ...deviceTypeEmsOptions] : options
  let label = options.find(item => item.value == type)?.label
  return label ?? ''
}
