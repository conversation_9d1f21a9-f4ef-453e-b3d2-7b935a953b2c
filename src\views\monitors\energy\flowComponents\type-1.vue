<template>
  <div class="position_box" style="width: 680px;">
    <div class="flow_box" style="flex: 1.5;">
      <!-- 电网 -->
      <div class="flow">
        <div class="dianzhan" style="left: -35px;z-index: 2;">
          <div style="margin-right: 30px;">
            <svg-icon icon-class="flow_ac" class-name="icon" class="svg-img" :style="{ color: !isShowDiesel ? $store.state.common.flowImg.flow_ac: '#8A8A8A' }" />
          </div>
          <div>
            <svg-icon icon-class="flow_en" class-name="icon" class="svg-img" :style="{ color: !isShowDiesel ? '#8A8A8A': $store.state.common.flowImg.flow_en }" />
          </div>
          <div class="flow-detail">
            <!-- <div>电网</div> -->
            <div v-if="!isShowDiesel">
              {{ $t('monitor.flowItem1') }}：<span>{{ flowData.power ? flowData.power : 0 }}</span>&nbsp;kW
            </div>
            <div v-else>
              {{ $t('发电机功率') }}：<span>{{ flowData.power ? flowData.power : 0 }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <div class="dianchi">
          <svg-icon icon-class="flow_de" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_de }" />
        </div>
        <!-- 柴油机 -->
        <div style=" position: absolute;right: 8px;" :style="{zIndex: isShowDiesel ? 1: 0}">
          <svg width="300px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <!-- 正 -->
            <path id="path1" stroke-width="2" fill="transparent" :stroke="isShowDiesel ? `url('#grad1')`: '#8a8a8a'"
              d="M 40 46 L40 100 Q40 110 50 110 L272 110" v-if="flowData.power < -1" />
            <!-- 反 -->
            <path id="path1" stroke-width="2" fill="transparent" :stroke="isShowDiesel ? `url('#grad1')`: '#8a8a8a'"
              d="M 272 110 L50 110 Q40 110 40 100  L40 46 " v-else-if="flowData.power > 1" />
            <path id="path1" stroke-width="2" fill="transparent" :stroke="isShowDiesel ? `url('#grad1')`: '#8a8a8a'"
              d="M 272 110 L50 110 Q40 110 40 100  L40 46 " v-else />

            <defs>
              <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stop-color="#436EE3" />
                <stop offset="100%" stop-color="#F1A121" />
              </linearGradient>
            </defs>
            <!-- 空心圆 -->
            <circle r="10" class="circle_1" v-if="showCircle('power') && isShowDiesel">
              <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
              <animateMotion dur="3s" repeatCount="indefinite">
                <mpath href="#path1" />
              </animateMotion>
            </circle>
            <circle r="7" class="circle_1" fill="white" v-if="showCircle('power') && isShowDiesel">
              <animateMotion dur="3s" repeatCount="indefinite">
                <mpath href="#path1" />
              </animateMotion>
            </circle>
          </svg>
        </div>
        <!-- 电网 -->
        <div style="position: absolute;right: 28px;top: 0;" :style="{zIndex: isShowDiesel ? 0: 1}">
          <svg width="380px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <!-- 正 -->
            <path id="path5" stroke-width="2" fill="transparent" :stroke="isShowDiesel ? '#8a8a8a': `url('#grad1')`"
              d="M 40 56 L40 100 Q40 110 50 110 L373 110" v-if="flowData.power < -1" />
            <!-- 反 -->
            <path id="path5" stroke-width="2" fill="transparent" :stroke="isShowDiesel ? '#8a8a8a': `url('#grad1')`"
              d="M 373 110 L50 110 Q40 110 40 100  L40 56 " v-else-if="flowData.power > 1" />
            <path id="path5" stroke-width="2" fill="transparent" :stroke="isShowDiesel ? '#8a8a8a': `url('#grad1')`"
              d="M 373 110 L50 110 Q40 110 40 100  L40 56 " v-else />
            <defs>
              <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stop-color="#436EE3" />
                <stop offset="100%" stop-color="#F1A121" />
              </linearGradient>
            </defs>
            <!-- 空心圆 -->
            <circle r="10" class="circle_1" v-if="showCircle('power') && !isShowDiesel">
              <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
              <animateMotion dur="3s" repeatCount="indefinite">
                <mpath href="#path5" />
              </animateMotion>
            </circle>
            <circle r="7" class="circle_1" fill="white" v-if="showCircle('power') && !isShowDiesel">
              <animateMotion dur="3s" repeatCount="indefinite">
                <mpath href="#path5" />
              </animateMotion>
            </circle>
          </svg>
        </div>
      </div>
      <!-- 负载 -->
      <div class="flow">
        <div class="diandeng">
          <svg-icon icon-class="flow_load" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_load }" />
          <div class="flow-detail">
            <!-- <div>负载</div> -->
            <div>
              {{ $t('monitor.flowItem3') }}：<span>{{ flowData.load }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="position: absolute;right: 8px;">
          <!-- 正 -->
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M40,50 L40,20 Q40 10 50 10  L272,10" v-if="flowData.load < -1" />
          <!-- 反 -->
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M272,10 L50,10 Q40 10 40 20  L40 50" v-else-if="flowData.load > 1" />
          <path id="path2" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M272,10 L50,10 Q40 10 40 20  L40 50" v-else />
          <defs>
            <linearGradient id="grad2" x1="0%" y1="100%" x2="100%" y2="0%" gradientUnits="userSpaceOnUse">
              <stop offset="0%" stop-color="#34BE76" />
              <stop offset="100%" stop-color="#F1A31C" />
            </linearGradient>
          </defs>
          <!-- 空心圆 -->
          <circle r="10" class="circle_1" v-if="showCircle('load')">
            <animate attributeName="fill" values="#F1A121;#436EE3" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('load')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path2" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
    <div class="flow_box">
      <!-- 光伏 -->
      <div class="flow">
        <div class="guangfu" style="padding-left: 0;">
          <div class="flow-detail" style="margin: 0;margin-right: 10px;padding-right: 3px">
            <!-- <div>光伏</div> -->
            <div>
              {{ $t('monitor.flowItem2') }}：<span>{{ flowData.photovoltaic ? flowData.photovoltaic : 0 }}</span>&nbsp;kW
            </div>
          </div>
          <svg-icon icon-class="flow_pv" class-name="icon" class="svg-img" :style="{ color: $store.state.common.flowImg.flow_pv }" />
        </div>
        <svg width="300px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-left: 4px;">
          <!-- 正 -->
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M250,70 L250,100 Q250 110 240 110  L20,110" v-if="flowData.photovoltaic > 1" />
          <!-- 反 -->
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M20,110 L240,110 Q250 110 250 100   L250,70" v-else-if="flowData.photovoltaic < -1" />
          <path id="path3" stroke-width="2" fill="transparent" stroke="url('#grad2')"
            d="M20,110 L240,110 Q250 110 250 100   L250,70" v-else />
          <defs>
            <linearGradient id="grad2" x1="1" y1="0" x2="0" y2="1">
              <stop offset="0" stop-color="#14B9AE" />
              <stop offset="1" stop-color="#F4A21B" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('photovoltaic')">
            <animate attributeName="fill" values="#F4A21B;#14B9AE" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path3" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('photovoltaic')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path3" />
            </animateMotion>
          </circle>
        </svg>
      </div>
      <!-- 电池 -->
      <!-- M 26 10 L 240 10 Q 250 10 250 20 L 250 50 -->
      <div class="flow">
        <div class="transformer1">
          <Bms :value="soc"></Bms>
          <div class="flow-detail" style="margin: 0 10px 0 0;">
            <!-- <div>电池</div> -->
            <div>
              {{ $t('monitor.flowItem4') }}：<span>{{ flowData.cell }}</span>&nbsp;kW
            </div>
          </div>
        </div>
        <svg width="300px" height="120px" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-left: 4px;">
          <!-- 正 -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')"
            d="M250,60 L250,20 Q250 10 240 10 L20 10" v-if="flowData.cell > 1" />
          <!-- 反  -->
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')"
            d="M 20 10 L 240 10 Q 250 10 250 20 L 250 60" v-else-if="flowData.cell < -1" />
          <path id="path4" stroke-width="2" fill="transparent" stroke="url('#grad4')"
            d="M 20 10 L 240 10 Q 250 10 250 20 L 250 60" v-else />

          <defs>
            <linearGradient id="grad4" x1="100%" y1="0" x2="0" y2="0">
              <stop offset="0%" stop-color="#F76655" />
              <stop offset="100%" stop-color="#F7A11A" />
            </linearGradient>
          </defs>
          <circle r="10" class="circle_1" v-if="showCircle('cell')">
            <animate attributeName="fill" values="#F7A11A;#F76655" dur="3s" repeatCount="indefinite" />
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
          <circle r="7" class="circle_1" fill="white" v-if="showCircle('cell')">
            <animateMotion dur="3s" repeatCount="indefinite">
              <mpath href="#path4" />
            </animateMotion>
          </circle>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import { isShow3502Bit10 } from '@/utils/parseBinaryToText'

import Bms from './bms.vue'

export default {
  components: { Bms },
  computed: {
    flowData() {
      return this.$store.state.monitor.flowData
    },
    // 流动拓补图展示圆
    showCircle() {
      let control = this.$store.state.monitor.control
      let flowData = this.$store.state.monitor.flowData
      return (name) => {
        if (!control) return false
        if (control.onLineState == '离线') return false
        if (-1 <= flowData[name] && flowData[name] <= 1) {
          return false
        } else if (flowData[name] == 0) {
          return false
        } else {
          return true
        }
      }
    },
    // 是否显示发电机
    isShowDiesel() {
      let sts = this.$store.state.monitor.pcs_sts
      if (sts.length) return isShow3502Bit10(sts[0]['sts_3502'])
      return false
    },
    soc() {
      let bms = this.$store.state.monitor.pcs_bms
      let bmsBau = this.$store.state.monitor.pcs_bmsBau
      if (bms.length) return (bms.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bms.length).toFixed(1)
      else if (bmsBau.length) return (bmsBau.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bmsBau.length).toFixed(1)
      return 0
    }
  }
}
</script>

<style lang="scss" scoped>
.position_box {
  position: absolute;
  height: 240px;
  width: 620px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;


  .flow_box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .flow {
    flex: 1;
    position: relative;

    .dianzhan {
      position: absolute;
      left: -4px;
      top: 6px;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      display: flex;
      align-items: center;
    }

    .flow-detail {
      margin-left: 10px;
      padding: 0 10px;
      border: 1px solid #d6d6d6;
      border-radius: 10px;
      /* height: 35px; */
      display: flex;
      font-size: 14px;
      line-height: 35px;

      span {
        font-weight: 600;
      }
    }

    .dianchi {
      position: absolute;
      right: -38px;
      top: 75px;
      border-radius: 6px;
      padding: 8px;
      z-index: 1000;
    }

    .diandeng {
      position: absolute;
      border-radius: 6px;
      padding: 8px;
      top: 32px;
      left: 67px;
      display: flex;
      align-items: center;
      z-index: 1;
    }

    .transformer1 {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      right: 14px;
      top: 36px;
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
    }

    .guangfu {
      position: absolute;
      border-radius: 6px;
      // background-color: white;
      padding: 8px;
      right: 7px;
      top: 6px;
      display: flex;
      align-items: center;
    }
    .img {
      width: 70px;
    }
  }
}
.svg-img {
  width: 70px;
  height: 70px;
}
</style>
