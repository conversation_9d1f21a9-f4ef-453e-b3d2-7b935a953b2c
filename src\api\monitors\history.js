
import request from '@/utils/request'

export function list(queryInfo) {
  return request({
    url: '/system/serviceData/list',
    method: 'get',
    params: queryInfo
  })
}

export function uploadServiceData(data) {
  return request({
    url: '/system/sendMqtt/uploadServiceData',
    method: 'post',
    data
  })
}

export function deleteServiceData(id) {
  return request({
    url: `/system/serviceData/${id}`,
    method: 'delete'
  })
}
