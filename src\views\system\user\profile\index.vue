<!--
 * @Author: shengri1990 <EMAIL>
 * @Date: 2023-11-14 14:49:19
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-20 14:10:38
 * @FilePath: \办公文档\代码\elecloud_platform-main\src\views\system\user\profile\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>{{ $t('user.personInfo') }}</span>
          </div>
          <div>
            <div class="text-center">
              <userAvatar />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="account" />{{ $t('user.name') }}
                <div class="pull-right">{{ user.userName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="account-supervisor" />{{ $t('user.belongRole') }}
                <div class="pull-right">{{ roleGroup }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone (1)" />{{ $t('user.phone') }}
                <div class="pull-right">{{ user.phonenumber && '--' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email (1)" />{{ $t('user.email') }}
                <div class="pull-right">{{ user.email && '--' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="calendar-clock" />{{ $t('user.createDate') }}
                <div class="pull-right">{{ user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <!-- <div slot="header" class="clearfix">
            <span>{{ $t('user.changePwd') }}</span>
          </div>
          <resetPwd /> -->
          <el-tabs v-model="activeTab">
            <el-tab-pane :label="$t('monitor.baseInfo')" name="userinfo">
              <userInfo :user="user" v-if="activeTab == 'userinfo'" />
            </el-tab-pane>
            <el-tab-pane :label="$t('user.changePwd')" name="resetPwd">
              <resetPwd v-if="activeTab == 'resetPwd'" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import { getUserProfile } from "@/api/system/user";

export default {
  name: "Profile",
  components: { userAvatar, userInfo, resetPwd },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: "resetPwd"
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.svg-icon {
  margin-right: 4px;
}
</style>
