<template>
  <div class="box">
    <div class="tree elevation-4">
      <div class="header-title">{{ $t('项目') }}</div>
      <div>
        <el-input v-model="filterText" :placeholder="$t(`project['Please enter project name']`)" clearable size="small"
          prefix-icon="el-icon-search" style="margin: 20px 0;" />
      </div>
      <el-tree ref="treeRef" :data="data" node-key="treeId" :props="defaultProps" default-expand-all
        :expand-on-click-node="false" :check-strictly="true" @node-click="handleNodeClick"
        :filter-node-method="filterNode" />
    </div>
    <div class="cont elevation-4">
      <div class="input_box">
        <div class="header-title">
          {{ $route.meta.title }}
        </div>
        <div>
          <el-radio-group v-model="dateType" size="medium" class="input_ment" @input="changeDateType">
            <el-radio-button :label="$t('date.day')"></el-radio-button>
            <el-radio-button :label="$t('date.month')"></el-radio-button>
            <el-radio-button :label="$t('date.year')"></el-radio-button>
          </el-radio-group>
          <div class="input_ment">
            <el-date-picker v-model="date" :type="dateTypeCom" :valueFormat='valueFormat'
              :range-separator="$t('date.to')" :start-placeholder="$t('date.start')" :picker-options="pickerOptions"
              :end-placeholder="$t('date.end')" @change="handleDateChange" style="width: 240px">
            </el-date-picker>
          </div>
          <div class="input_ment">
            <el-select v-model="queryInfo.deviceType" :placeholder="$t('common.select')" style="width: 120px">
              <el-option :label="$t('common.all')" :value="undefined">
            </el-option>
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="input_ment">
            <el-input :placeholder="'SN'" style="width: 160px;"
              v-model="queryInfo.ac" clearable></el-input>
          </div>
          <div class="input_ment">
            <el-button type="primary" icon="el-icon-search" @click="handleSearchClick()">{{ $t('common.search')
              }}</el-button>
          </div>
          <div class="input_ment">
            <el-button type="primary" @click="handleExportClick()">{{ $t('common.exportReport') }}</el-button>
          </div>
        </div>
      </div>
      <div class="table_box">
        <!-- table -->
        <el-table :data="tableData" v-loading="loading" stripe show-summary :summary-method="getSummaries"
          :header-cell-style="{ 'text-align': 'center', 'font-size': '14px' }"
          :cell-style="{ 'text-align': 'center', 'font-size': '14px' }" style="width: 100%;" align="center" row-key="id"
          :tree-props="{ children: 'children' }">
          <el-table-column type="index" label="#" width="60" fixed="left" />
          <el-table-column prop="projectName" :label="$t('project.name')" show-overflow-tooltip width="140"
            fixed="left" />
          <el-table-column prop="ac" :label="$t('device.sn')" show-overflow-tooltip width="140" fixed="left" />
          <el-table-column prop="chargeCapacityCalculate" :label="$t('monitor.topItem4')" sortable show-overflow-tooltip
            width="200" />
          <el-table-column prop="dischargeCapacityCalculate" :label="$t('monitor.topItem5')" sortable
            show-overflow-tooltip width="200" />
          <el-table-column prop="energyStorageCharge" :label="$t('储能收益')" sortable show-overflow-tooltip width="200" />
          <el-table-column prop="gridSideTotalDischargeCalculate" :label="$t('上网电量')" sortable show-overflow-tooltip
            width="200" />
          <el-table-column prop="gridSideTotalDischarge" :label="$t('电网卖电收益')" sortable show-overflow-tooltip
            width="200" />
          <el-table-column prop="photovoltaicPowerCapacityCalculate" :label="$t('monitor.topItem7')" sortable
            show-overflow-tooltip width="200" />
          <el-table-column prop="photovoltaicPowerGeneration" :label="$t('系统收益')" sortable show-overflow-tooltip
            width="200" />
          <el-table-column prop="deviceType" :label="$t('device.type')" show-overflow-tooltip width="320">
            <template slot-scope="scope">
              <el-tag type="primary">{{ getDeviceTypeFn(scope.row.deviceType, true) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="statisticsTime" :label="$t('统计日期')" show-overflow-tooltip width="200" />
          <el-table-column prop="updateTime" :label="$t(`bill['更新时间']`)" show-overflow-tooltip width="200" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryInfo.pageNum" :limit.sync="queryInfo.pageSize"
          @pagination="getList" style="margin-top: 20px;text-align: right;" />
      </div>
    </div>
  </div>
</template>

<script>
import { billList, billExport } from '@/api/operation/bill'
import { handleExport } from '@/utils/export'
import _ from 'lodash'
import { getProjectTree } from '@/api/test/dataanalysis'
import { deviceTypeSingleOptions, deviceTypeGroupOptions, getDeviceType } from '@/hook/useDeviceType'

export default {
  data() {
    return {
      tableData: [],
      loading: false,
      total: 10,
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      date: undefined, // 获取当前日期
      dateType: this.$t('date.month'),
      pickerOptions: {
        shortcuts: [{
          text: this.$t('date.lastWeek'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.lastMonth'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: this.$t('date.last3Month'),
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      filterText: ''
    };
  },
  mounted() {
    this.getProjectTreeFn()
  },
  computed: {
    valueFormat() {
      if (this.dateType == this.$t('date.day')) {
        return 'yyyy-MM-dd'
      } else if (this.dateType == this.$t('date.month')) {
        return 'yyyy-MM'
      } else if (this.dateType == this.$t('date.year')) {
        return 'yyyy'
      }
    },
    dateTypeCom() {
      if (this.dateType == this.$t('date.day')) {
        return 'daterange'
      } else if (this.dateType == this.$t('date.month')) {
        return 'month'
      } else if (this.dateType == this.$t('date.year')) {
        return 'year'
      }
    },
    typeOptions() {
      return [...deviceTypeSingleOptions, ...deviceTypeGroupOptions]
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val);
    }
  },
  methods: {
    //搜索按键
    handleSearchClick() {
      this.getList()
    },
    // 获取列表
    getList() {
      this.loading = true
      billList(this.queryInfo).then(res => {
        let data = res.rows
        function mapData(list) {
          list.forEach(item => {
            // item.total = _.round(item.energyStorageCharge + item.energyStorageDischarge + item.gridSideTotalDischarge + item.photovoltaicPowerGeneration, 2)
            item.energyStorageCharge = item.energyStorageCharge != null ? _.round(item.energyStorageCharge, 2) : '--'
            item.energyStorageDischarge = item.energyStorageDischarge != null ? _.round(item.energyStorageDischarge, 2) : '--'
            item.chargeCapacityCalculate = item.chargeCapacityCalculate != null ? _.round(item.chargeCapacityCalculate, 2) : '--'
            item.dischargeCapacityCalculate = item.dischargeCapacityCalculate != null ? _.round(item.dischargeCapacityCalculate, 2) : '--'
            item.gridSideTotalDischarge = item.gridSideTotalDischarge != null ? _.round(item.gridSideTotalDischarge, 2) : '--'
            item.gridSideTotalDischargeCalculate = item.gridSideTotalDischargeCalculate != null ? _.round(item.gridSideTotalDischargeCalculate, 2) : '--'
            item.photovoltaicPowerGeneration = item.photovoltaicPowerGeneration != null ? _.round(item.photovoltaicPowerGeneration, 2) : '--'
            item.photovoltaicPlantTree = item.photovoltaicPlantTree != null ? _.round(item.photovoltaicPlantTree, 2) : '--'
            item.photovoltaicReduceCo2 = item.photovoltaicReduceCo2 != null ? _.round(item.photovoltaicReduceCo2, 2) : '--'
            item.photovoltaicPowerCapacityCalculate = item.photovoltaicPowerCapacityCalculate != null ? _.round(item.photovoltaicPowerCapacityCalculate, 2) : '--'
            if (item.children) mapData(item.children)
          })
        }
        mapData(data)
        this.tableData = data
        this.total = res.total
        this.loading = false
      });
    },
    handleDateChange(date) {
      if (!date) return this.changeDateType()
      if (this.dateType == this.$t('date.day')) {
        this.queryInfo.startDate = date[0]
        this.queryInfo.endDate = date[1]
      } else if (this.dateType == this.$t('date.month')) {
        this.queryInfo.startDate = date
        this.queryInfo.endDate = date
      } else if (this.dateType == this.$t('date.year')) {
        this.queryInfo.startDate = date
        this.queryInfo.endDate = date
      }
      this.getList()
    },
    changeDateType() {
      let startDate = ''
      let endDate = ''
      if (this.dateType == this.$t('date.day')) {
        this.date = [this.$moment(new Date()).startOf('day').format('YYYY-MM-DD'), this.$moment(new Date()).endOf('day').format('YYYY-MM-DD')]
        startDate = this.date[0]
        endDate = this.date[1]
        this.date = [
          startDate,
          endDate
        ]
      } else if (this.dateType == this.$t('date.month')) {
        this.date = [this.$moment().startOf('month').format("YYYY-MM"), this.$moment().endOf('month').format("YYYY-MM")]
        startDate = this.date[0]
        endDate = this.date[1]
        this.date = startDate
      } else if (this.dateType == this.$t('date.year')) {
        this.date = [this.$moment().startOf('year').format("YYYY"), this.$moment().endOf('year').format("YYYY")]
        startDate = this.date[0]
        endDate = this.date[1]
        this.date = startDate
      }
      this.queryInfo = {
        ...this.queryInfo,
        startDate,
        endDate
      }
      this.getList()
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t(`bill['合计']`);
          return;
        }
        if (index == 1 || index == 2 || index == 10 || index == 11) {
          sums[index] = ''
          return
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0).toFixed(2);
          if (index == 5 || index == 7 || index == 9) sums[index] += ` ${data[0].currency}`
          else sums[index] += ` kWh`
        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    handleExportClick() {
      this.$modal.loading(`${this.$t(`oss['正在下载文件，请稍候']`)}...`);
      billExport({
        ac: this.queryInfo.ac,
        startDate: this.queryInfo.startDate,
        endDate: this.queryInfo.endDate,
        projectId: this.queryInfo.projectId
      }).then(res => {
        if (res.code) if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let fileName = ''
        if (this.dateType == this.$t('date.day')) {
          fileName = `${this.queryInfo.startDate}-${this.queryInfo.endDate}_${this.$t(`log['收益统计报表']`)}`
        } else if (this.dateType == this.$t('date.month')) {
          fileName = `${this.queryInfo.startDate}${this.$t('date.month')}_${this.$t(`log['收益统计报表']`)}`
        } else if (this.dateType == this.$t('date.year')) {
          fileName = `${this.queryInfo.startDate}${this.$t('date.year')}_${this.$t(`log['收益统计报表']`)}`
        }
        handleExport(res, fileName)
        this.$modal.closeLoading()
      }).catch(() => {
        this.$modal.closeLoading()
      })
    },
    // 获取树形
    getProjectTreeFn() {
      this.data = []
      getProjectTree({
        deviceType: 0
      }).then(res => {
        if (res.code !== 200) return this.$message({
          type: 'error',
          message: res.msg
        })
        let items = res.data
        items = items.map(item => {
          return {
            ...item, children: []
          }
        });
        this.queryInfo.projectId = items[0].id
        this.data = items
        this.$nextTick(() => {
          this.$refs.treeRef.setCurrentKey(items[0].treeId)
        })
        this.changeDateType()
      })
    },
    handleNodeClick(data, node) {
      this.queryInfo.projectId = data.id
      this.getList()
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    getDeviceTypeFn(type, needGroup) {
      return getDeviceType(type, needGroup)
    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100%;
  display: flex;
  .tree {
    padding: 20px;
    width: 320px;
    background-color: #fff;
    margin-right: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

    :deep(.el-tree) {
      height: 90%;
      overflow: auto;

      .el-tree-node__content {
        height: 40px;
        font-size: 16px;
        /* border-radius: 8px; */
        /* margin-bottom: 10px; */
      }

      .is-current>.el-tree-node__content {
        background-color: #f6f6f6;
        font-weight: 600;
        color: var(--base-color);
      }
      .el-tree-node__content:hover, .el-upload-list__item:hover {
        background-color: #f6f6f6;
      }
    }
  }

  .cont {
    background: #fff;
    border-radius: 8px;
    flex: 1;
    overflow: auto;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  }

  .table_box {
    margin-top: 30px;
    width: 100%;
    background-color: white;
    padding-bottom: 10px;
    border-radius: 12px;

    .solve {
      display: flex;
      align-items: center;
    }
  }
}

::v-deep .alarm-state .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-table__footer-wrapper tbody td.el-table__cell {
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}
</style>
